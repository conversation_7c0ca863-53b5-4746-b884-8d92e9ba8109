<?php

declare(strict_types=1);

namespace FrontModule\Presenters\Homepage;

use App\Model\Orm\Orm;
use App\Tests\Helpers\ContainerFactory;
use Nette\Application\IPresenterFactory;
use Nette\Application\Request;
use Nette\Application\Responses\TextResponse;
use Nette\Application\UI\Presenter;
use Tester\Assert;
use Tester\DomQuery;
use Tester\TestCase;

$containerFactory = require __DIR__ . '/../../../bootstrap-integration.php';

/**
 * @testCase
 */
final class HomepagePresenterTest extends TestCase
{
	public function __construct(
		private readonly ContainerFactory $containerFactory,
	) {}

	public function testDefault(): void
	{
		 $container = $this->containerFactory->createContainer();

		 $presenterFactory = $container->getByType(IPresenterFactory::class);
		 $presenter = $presenterFactory->createPresenter('Front:Homepage');
		 $presenter->autoCanonicalize = false;

		 $treeRepository = $container->getByType(Orm::class)->tree;
		 $hpPage = $treeRepository->getById(1);

		 $request = new Request('Front:Homepage', 'GET', [
		 	Presenter::ACTION_KEY => Presenter::DEFAULT_ACTION,
		 	'object' => $hpPage,
		 ]);
		 $response = $presenter->run($request);
		 Assert::type(TextResponse::class, $response);

		 $html = (string) $response->getSource();
		 $dom = DomQuery::fromHtml($html);

		 Assert::true($dom->has('h1 img'), 'Homepage contains an image inside h1');
	}
}

// (new HomepagePresenterTest($containerFactory))->run();
