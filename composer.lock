{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "bc30e12a882f3fbc9c446dc5b20dfa74", "packages": [{"name": "a<PERSON><PERSON><PERSON><PERSON>/yasumi", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/azuyalabs/yasumi.git", "reference": "37d1215d4f4012d3185bb9990c76ca17a4ff1c30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/azuyalabs/yasumi/zipball/37d1215d4f4012d3185bb9990c76ca17a4ff1c30", "reference": "37d1215d4f4012d3185bb9990c76ca17a4ff1c30", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.0"}, "require-dev": {"ext-intl": "*", "friendsofphp/php-cs-fixer": "^2.19 || ^3.40", "mikey179/vfsstream": "^1.6", "phan/phan": "^5.4", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5 || ^9.6", "vimeo/psalm": "^5.16"}, "suggest": {"ext-calendar": "For calculating the date of Easter"}, "type": "library", "autoload": {"psr-4": {"Yasumi\\": "src/<PERSON><PERSON><PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sacha <PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "The easy PHP Library for calculating holidays.", "homepage": "https://www.yasumi.dev", "keywords": ["Bank", "calculation", "calendar", "celebration", "date", "holiday", "holidays", "national", "time"], "support": {"docs": "https://www.yasumi.dev", "issues": "https://github.com/azuyalabs/yasumi/issues", "source": "https://github.com/azuyalabs/yasumi"}, "funding": [{"url": "https://www.buymeacoffee.com/sachatelgenhof", "type": "other"}], "time": "2024-01-07T14:12:44+00:00"}, {"name": "bacon/bacon-qr-code", "version": "2.0.8", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "8674e51bb65af933a5ffaf1c308a660387c35c22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/8674e51bb65af933a5ffaf1c308a660387c35c22", "reference": "8674e51bb65af933a5ffaf1c308a660387c35c22", "shasum": ""}, "require": {"dasprid/enum": "^1.0.3", "ext-iconv": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phly/keep-a-changelog": "^2.1", "phpunit/phpunit": "^7 | ^8 | ^9", "spatie/phpunit-snapshot-assertions": "^4.2.9", "squizlabs/php_codesniffer": "^3.4"}, "suggest": {"ext-imagick": "to generate QR code images"}, "type": "library", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/2.0.8"}, "time": "2022-12-07T17:46:57+00:00"}, {"name": "brick/math", "version": "0.11.0", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/0ad82ce168c82ba30d1c01ec86116ab52f589478", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.0", "vimeo/psalm": "5.0.0"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.11.0"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2023-01-15T23:15:59+00:00"}, {"name": "brick/money", "version": "0.8.1", "source": {"type": "git", "url": "https://github.com/brick/money.git", "reference": "25f484a347756b7f3fbe7ad63ed9ad2d87b20004"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/money/zipball/25f484a347756b7f3fbe7ad63ed9ad2d87b20004", "reference": "25f484a347756b7f3fbe7ad63ed9ad2d87b20004", "shasum": ""}, "require": {"brick/math": "~0.10.1 || ~0.11.0", "ext-json": "*", "php": "^8.0"}, "require-dev": {"brick/varexporter": "~0.3.0", "ext-dom": "*", "ext-pdo": "*", "php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.4.3", "vimeo/psalm": "5.14.1"}, "suggest": {"ext-intl": "Required to format Money objects"}, "type": "library", "autoload": {"psr-4": {"Brick\\Money\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Money and currency library", "keywords": ["brick", "currency", "money"], "support": {"issues": "https://github.com/brick/money/issues", "source": "https://github.com/brick/money/tree/0.8.1"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2023-09-23T21:17:11+00:00"}, {"name": "clue/stream-filter", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.7.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2023-12-20T15:40:13+00:00"}, {"name": "composer/ca-bundle", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "0c5ccfcfea312b5c5a190a21ac5cef93f74baf99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/0c5ccfcfea312b5c5a190a21ac5cef93f74baf99", "reference": "0c5ccfcfea312b5c5a190a21ac5cef93f74baf99", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.5.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-03-15T14:00:32+00:00"}, {"name": "contributte/apitte", "version": "v0.11.1", "source": {"type": "git", "url": "https://github.com/contributte/apitte.git", "reference": "c651ea71356858d1ca7f2bd2830a7fd291a09dd1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/apitte/zipball/c651ea71356858d1ca7f2bd2830a7fd291a09dd1", "reference": "c651ea71356858d1ca7f2bd2830a7fd291a09dd1", "shasum": ""}, "require": {"contributte/middlewares": "^0.11.0", "contributte/psr7-http-message": "^0.9.0", "doctrine/annotations": "^1.13", "ext-json": "*", "koriym/attributes": "^1.0.2", "nette/utils": "^3.2.7 || ^4.0.0", "php": ">=8.0"}, "conflict": {"nette/tester": "<2.4.1"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"contributte/dev": "^0.3", "contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.3", "mockery/mockery": "^1.5.1", "nette/application": "^3.0.0", "nette/di": "^3.0.0", "nette/http": "^3.0.1", "nettrine/annotations": "^0.7.0", "psr/log": "^1.1|^2.0|^3.0", "symfony/console": "^5.4.3 || ^6.0.0", "symfony/validator": "^5.4.3 || ^6.0", "symfony/yaml": "^5.4.3 || ^6.0.0", "tracy/tracy": "^2.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "autoload": {"psr-4": {"Apitte\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "An opinionated and enjoyable API framework based on Nette Framework. Supporting content negotiation, debugging, middlewares, attributes, annotations and loving openapi/swagger.", "homepage": "https://github.com/contributte/apitte", "keywords": ["annotation", "api", "apitte", "http", "nette", "rest"], "support": {"issues": "https://github.com/contributte/apitte/issues", "source": "https://github.com/contributte/apitte/tree/v0.11.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-12-01T17:09:43+00:00"}, {"name": "contributte/application", "version": "v0.5.2", "source": {"type": "git", "url": "https://github.com/contributte/application.git", "reference": "f5f8637bd54eacd1cc45792f23bb8831873ddb35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/application/zipball/f5f8637bd54eacd1cc45792f23bb8831873ddb35", "reference": "f5f8637bd54eacd1cc45792f23bb8831873ddb35", "shasum": ""}, "require": {"nette/application": "^3.0.0 || ^4.0", "php": ">=7.2"}, "require-dev": {"nette/http": "~2.4.8 || ^3.0.0", "ninjify/nunjuck": "^0.3.0", "ninjify/qa": "^0.13.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "psr/http-message": "~1.0.1", "tracy/tracy": "~2.9.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Application\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Extra contrib to nette/application", "homepage": "https://github.com/contributte/application", "keywords": ["application", "component", "control", "nette", "presenter"], "support": {"issues": "https://github.com/contributte/application/issues", "source": "https://github.com/contributte/application/tree/v0.5.2"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-09-27T20:32:37+00:00"}, {"name": "contributte/console", "version": "v0.10.1", "source": {"type": "git", "url": "https://github.com/contributte/console.git", "reference": "dc2b84fb8dd795ea9988f396311aeed435aed495"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/console/zipball/dc2b84fb8dd795ea9988f396311aeed435aed495", "reference": "dc2b84fb8dd795ea9988f396311aeed435aed495", "shasum": ""}, "require": {"nette/di": "^3.1.8", "php": ">=8.1", "symfony/console": "^6.4.2 || ^7.0.2"}, "require-dev": {"contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.4", "mockery/mockery": "^1.6.7", "nette/http": "^3.2.3", "symfony/event-dispatcher": "^6.4.2 || ^7.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Console\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Best Symfony Console for Nette Framework", "homepage": "https://github.com/contributte/console", "keywords": ["console", "nette", "symfony"], "support": {"issues": "https://github.com/contributte/console/issues", "source": "https://github.com/contributte/console/tree/v0.10.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-01-04T20:10:58+00:00"}, {"name": "contributte/console-extra", "version": "v0.8.0", "source": {"type": "git", "url": "https://github.com/contributte/console-extra.git", "reference": "09e14dcde8676828976c4c86bd912c8a67835975"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/console-extra/zipball/09e14dcde8676828976c4c86bd912c8a67835975", "reference": "09e14dcde8676828976c4c86bd912c8a67835975", "shasum": ""}, "require": {"nette/di": "^3.1.8", "php": ">=8.1", "symfony/console": "^6.4.2 || ^7.0.2"}, "conflict": {"nette/schema": "<1.0.1"}, "require-dev": {"contributte/console": "~0.10", "contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.4", "latte/latte": "^3.0.12", "mockery/mockery": "^1.5.0", "nette/application": "^3.1.14", "nette/bootstrap": "^3.2.1", "nette/caching": "^3.2.3", "nette/security": "^3.1.8"}, "suggest": {"contributte/console": "Symfony\\Console for Nette"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.9.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Console\\Extra\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Nette-based console commands for latte, DIC, security, utils and many others", "homepage": "https://github.com/contributte/console-extra", "keywords": ["console", "nette", "symfony"], "support": {"issues": "https://github.com/contributte/console-extra/issues", "source": "https://github.com/contributte/console-extra/tree/v0.8.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-01-04T12:17:40+00:00"}, {"name": "contributte/di", "version": "v0.5.6", "source": {"type": "git", "url": "https://github.com/contributte/di.git", "reference": "49d6b93d46f57be319b1e811cd983bfed0c90979"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/di/zipball/49d6b93d46f57be319b1e811cd983bfed0c90979", "reference": "49d6b93d46f57be319b1e811cd983bfed0c90979", "shasum": ""}, "require": {"nette/di": "^3.1.0", "nette/utils": "^3.2.8 || ^4.0", "php": ">=7.2"}, "conflict": {"nette/schema": "<1.1.0"}, "require-dev": {"nette/bootstrap": "^3.1.4", "nette/robot-loader": "^3.4.2 || ^4.0", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.13", "phpstan/phpstan": "^1.9.11", "phpstan/phpstan-deprecation-rules": "^1.1.1", "phpstan/phpstan-nette": "^1.2.0", "phpstan/phpstan-strict-rules": "^1.4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\DI\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Extra contrib to nette/di", "homepage": "https://github.com/contributte/di", "keywords": ["dependency", "inject", "nette"], "support": {"issues": "https://github.com/contributte/di/issues", "source": "https://github.com/contributte/di/tree/v0.5.6"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-09-05T08:23:55+00:00"}, {"name": "contributte/elastica", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/contributte/elastica.git", "reference": "b562c0e275d0acb522065b9633c026f87e3b2953"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/elastica/zipball/b562c0e275d0acb522065b9633c026f87e3b2953", "reference": "b562c0e275d0acb522065b9633c026f87e3b2953", "shasum": ""}, "require": {"nette/di": "^3.0", "nette/utils": "^3.0", "php": ">=7.2", "ruflin/elastica": "^7.0"}, "conflict": {"nette/schema": "<1.2.0"}, "require-dev": {"nette/bootstrap": "^3.0", "nette/http": "^3.0", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^7.0|8.0|^9.0", "tracy/tracy": "^2.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Elastica\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Elastica implementation for Nette Framework", "homepage": "https://github.com/contributte/elastica", "keywords": ["elastic", "elastica", "elasticsearch", "es", "nette", "search"], "support": {"issues": "https://github.com/contributte/elastica/issues", "source": "https://github.com/contributte/elastica/tree/v1.1.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-09-28T10:53:57+00:00"}, {"name": "contributte/event-dispatcher", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/contributte/event-dispatcher.git", "reference": "00c44782f8e4c16c8c97859d83d42e7e6bdf063e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/event-dispatcher/zipball/00c44782f8e4c16c8c97859d83d42e7e6bdf063e", "reference": "00c44782f8e4c16c8c97859d83d42e7e6bdf063e", "shasum": ""}, "require": {"nette/di": "^3.1.8", "php": ">=8.1", "symfony/event-dispatcher": "^6.4.3 || ^7.0.3"}, "require-dev": {"contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.3", "mockery/mockery": "^1.5.0", "psr/log": "^2.0.0 || ^3.0.0", "tracy/tracy": "^2.10.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Contributte\\EventDispatcher\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Best event dispatcher / event manager / event emitter for Nette Framework", "homepage": "https://github.com/contributte/event-dispatcher", "keywords": ["dispatcher", "emitter", "event", "nette", "symfony"], "support": {"issues": "https://github.com/contributte/event-dispatcher/issues", "source": "https://github.com/contributte/event-dispatcher/tree/v0.9.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-02-05T16:00:14+00:00"}, {"name": "contributte/event-dispatcher-extra", "version": "v0.10.1", "source": {"type": "git", "url": "https://github.com/contributte/event-dispatcher-extra.git", "reference": "a4180b3683082b1840478a28717ff09611d1ff31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/event-dispatcher-extra/zipball/a4180b3683082b1840478a28717ff09611d1ff31", "reference": "a4180b3683082b1840478a28717ff09611d1ff31", "shasum": ""}, "require": {"nette/di": "^3.1.9", "php": ">=8.1", "symfony/event-dispatcher": "^6.4.3 || ^7.0.3"}, "conflict": {"latte/latte": "<3.0.13"}, "require-dev": {"contributte/event-dispatcher": "^0.9.0", "contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.3", "latte/latte": "^3.0.13", "nette/application": "^3.1.10", "nette/http": "^3.2.1", "nette/security": "^3.1.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Events\\Extra\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Nette-based Symfony events for application, presenter, form, latte, templates, security and many others.", "homepage": "https://github.com/contributte/event-dispatcher-extra", "keywords": ["Bridge", "application", "dispatcher", "event", "form", "latte", "nette", "security", "template"], "support": {"issues": "https://github.com/contributte/event-dispatcher-extra/issues", "source": "https://github.com/contributte/event-dispatcher-extra/tree/v0.10.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-02-05T19:02:34+00:00"}, {"name": "contributte/logging", "version": "v0.6.3", "source": {"type": "git", "url": "https://github.com/contributte/logging.git", "reference": "2cc959bcfbd05cf2946b6711432d14fc0deed418"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/logging/zipball/2cc959bcfbd05cf2946b6711432d14fc0deed418", "reference": "2cc959bcfbd05cf2946b6711432d14fc0deed418", "shasum": ""}, "require": {"php": ">=7.2", "tracy/tracy": "~2.5.5|~2.6.2|~2.7.0|~2.8.0|~2.9.0|~2.10.0"}, "conflict": {"nette/di": "<3.0"}, "require-dev": {"ext-json": "*", "nette/di": "^3.0.0", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "sentry/sdk": "^3.0.0"}, "suggest": {"nette/di": "to use TracyLoggingExtension", "sentry/sdk": "to use SentryLoggingExtension"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Logging\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Plug-in support logging for Tracy / Nette Framework", "homepage": "https://github.com/contributte/logging", "keywords": ["logging", "monolog", "nette", "plugins", "tracy"], "support": {"issues": "https://github.com/contributte/logging/issues", "source": "https://github.com/contributte/logging/tree/v0.6.3"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-04-03T15:20:33+00:00"}, {"name": "contributte/messenger", "version": "v0.1", "source": {"type": "git", "url": "https://github.com/contributte/messenger.git", "reference": "f19020bb1e12cbb1d31ceff82ec86c5fe2b47ac7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/messenger/zipball/f19020bb1e12cbb1d31ceff82ec86c5fe2b47ac7", "reference": "f19020bb1e12cbb1d31ceff82ec86c5fe2b47ac7", "shasum": ""}, "require": {"ext-json": "*", "nette/di": "^3.1.2", "php": ">=8.0", "psr/cache": "^3.0.0", "psr/container": "^2.0.2", "psr/log": "^3.0.0", "symfony/console": "^6.0.19 || ^6.2.10", "symfony/event-dispatcher": "^6.0.19 || ^6.2.8", "symfony/messenger": "^6.0.19 || ^6.2.8", "symfony/var-dumper": "^6.0.19 || ^6.2.10"}, "require-dev": {"contributte/console": "^0.10.0", "contributte/event-dispatcher": "^0.9.0", "contributte/phpstan": "^0.1.0", "contributte/qa": "^0.4.0", "contributte/tester": "^0.3.0", "mockery/mockery": "^1.3.3", "psr/container": "^2.0.2", "symfony/amqp-messenger": "^6.0.19 || ^6.2.8", "symfony/doctrine-messenger": "^6.0.19 || ^6.2.10", "symfony/redis-messenger": "^6.0.19 || ^6.2.10", "tracy/tracy": "^2.10.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.1.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Messenger\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Best Symfony Messenger for Nette framework", "homepage": "https://github.com/contributte/messenger", "keywords": ["<PERSON>", "async", "consumer", "nette", "publisher", "symfony"], "support": {"issues": "https://github.com/contributte/messenger/issues", "source": "https://github.com/contributte/messenger/tree/v0.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-07-31T14:31:59+00:00"}, {"name": "contributte/middlewares", "version": "v0.11.1", "source": {"type": "git", "url": "https://github.com/contributte/middlewares.git", "reference": "77b6e8d5db6b4690d4a156f38fb56471a94a0a5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/middlewares/zipball/77b6e8d5db6b4690d4a156f38fb56471a94a0a5a", "reference": "77b6e8d5db6b4690d4a156f38fb56471a94a0a5a", "shasum": ""}, "require": {"contributte/di": "^0.5.1", "contributte/psr7-http-message": "^0.9.0", "php": ">=7.2"}, "require-dev": {"nette/application": "^3.1.0", "nette/http": "^3.2.0", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.14", "phpstan/phpstan": "^1.6.8", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.2.3", "psr/log": "^1.0|^2.0|^3.0", "tracy/tracy": "^2.9.0"}, "suggest": {"nette/application": "to use PresenterMiddleware", "nette/http": "to use NetteMiddlewareExtension & NetteMiddlewareApplication", "tracy/tracy": "to use TracyMiddleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Middlewares\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Middleware / Relay / PSR-7 support to Nette Framework", "homepage": "https://github.com/contributte/middlewares", "keywords": ["<PERSON><PERSON>", "middleware", "nette"], "support": {"issues": "https://github.com/contributte/middlewares/issues", "source": "https://github.com/contributte/middlewares/tree/v0.11.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-02-25T10:58:16+00:00"}, {"name": "contributte/monolog", "version": "v0.5.2", "source": {"type": "git", "url": "https://github.com/contributte/monolog.git", "reference": "85511b300e5c6ac12b25bf09f5ece16de2fd2c18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/monolog/zipball/85511b300e5c6ac12b25bf09f5ece16de2fd2c18", "reference": "85511b300e5c6ac12b25bf09f5ece16de2fd2c18", "shasum": ""}, "require": {"contributte/di": "^0.5.3", "monolog/monolog": "^2.0.0 || ^3.0.0", "nette/utils": "^3.0.0 || ^4.0.0", "php": ">=7.2"}, "require-dev": {"ninjify/nunjuck": "^0.4", "ninjify/qa": "^v0.13.0", "phpstan/phpstan": "^1.8.11", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-nette": "^1.1.0", "phpstan/phpstan-strict-rules": "^1.4.4", "tracy/tracy": "^v2.9.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Monolog\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Monolog integration into Nette Framework", "homepage": "https://github.com/contributte/monolog", "keywords": ["logging", "monolog", "nette"], "support": {"issues": "https://github.com/contributte/monolog/issues", "source": "https://github.com/contributte/monolog/tree/v0.5.2"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-07-21T08:28:12+00:00"}, {"name": "contributte/psr7-http-message", "version": "v0.9.0", "source": {"type": "git", "url": "https://github.com/contributte/psr7-http-message.git", "reference": "f32ee0ca0b6c8bd7c01e2502833c4e3c21600ccc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/psr7-http-message/zipball/f32ee0ca0b6c8bd7c01e2502833c4e3c21600ccc", "reference": "f32ee0ca0b6c8bd7c01e2502833c4e3c21600ccc", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7.0 || ^2.1.0", "nette/utils": "^3.0.0", "php": ">=7.2"}, "conflict": {"nette/di": "<3.0.7", "nette/http": "<3.0.5"}, "require-dev": {"nette/application": "^3.1.0", "nette/di": "^3.0.7", "nette/http": "^3.0.5", "ninjify/nunjuck": "^v0.3.0", "ninjify/qa": "^v0.13.0", "phpstan/phpstan": "^1.8.2", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-nette": "^1.0.0", "phpstan/phpstan-strict-rules": "^1.3.0"}, "suggest": {"nette/application": "to use $request->withApplicationRequest[Nette\\Application\\Request]", "nette/di": "to use Psr7HttpExtension[CompilerExtension]", "nette/http": "to use $request->withHttpRequest[Nette\\Http\\Request], to use Psr7RequestFactory, to use Psr7ServerRequestFactory"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.9.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Psr7\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "PSR-7 (HTTP Message Interface) to Nette Framework", "homepage": "https://github.com/contributte/psr7-http-message", "keywords": ["http", "message", "nette", "psr7", "request", "response"], "support": {"issues": "https://github.com/contributte/psr7-http-message/issues", "source": "https://github.com/contributte/psr7-http-message/tree/v0.9.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-10-30T09:39:23+00:00"}, {"name": "contributte/redis", "version": "v0.5.4", "source": {"type": "git", "url": "https://github.com/contributte/redis.git", "reference": "a754af3f520ba1ac801aaa6267053ddc1e99520e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/redis/zipball/a754af3f520ba1ac801aaa6267053ddc1e99520e", "reference": "a754af3f520ba1ac801aaa6267053ddc1e99520e", "shasum": ""}, "require": {"ext-json": "*", "nette/di": "^2.4.17 || ^3.0.1", "php": ">=7.2", "predis/predis": "^1.1.6 || ^2.0.0"}, "require-dev": {"mockery/mockery": "^1.3.3", "nette/caching": "^2.5.0 || ^3.1.3", "nette/http": "^2.4.0 || ^3.0.1", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "tracy/tracy": "^2.7.0"}, "suggest": {"ext-igbinary": "For better serialization"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Redis\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Redis client integration into Nette framework", "homepage": "https://github.com/contributte/redis", "keywords": ["cache", "nette", "predis", "redis"], "support": {"issues": "https://github.com/contributte/redis/issues", "source": "https://github.com/contributte/redis/tree/v0.5.4"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-07-27T10:08:54+00:00"}, {"name": "contributte/validator", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/contributte/validator.git", "reference": "57739f9e033e0f5d55412c6bbeb63850e504db99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/validator/zipball/57739f9e033e0f5d55412c6bbeb63850e504db99", "reference": "57739f9e033e0f5d55412c6bbeb63850e504db99", "shasum": ""}, "require": {"nette/di": "^3.0.1", "php": ">=7.4", "symfony/cache": "^5.0 || ^6.0", "symfony/config": "^5.0 || ^6.0", "symfony/validator": "^5.2 || ^6.0"}, "require-dev": {"doctrine/annotations": "^1.8", "doctrine/cache": "^1.10", "nette/bootstrap": "^3.0", "nette/tester": "^2.4", "ninjify/nunjuck": "^0.3.0", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "symfony/translation": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Contributte\\Validator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://jiripudil.cz"}], "description": "Symfony/Validator integration for Nette Framework.", "homepage": "https://github.com/contributte/validator", "keywords": ["nette", "symfony", "validator"], "support": {"issues": "https://github.com/contributte/validator/issues", "source": "https://github.com/contributte/validator/tree/v1.1.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-05-24T12:34:07+00:00"}, {"name": "cweagans/composer-patches", "version": "1.7.3", "source": {"type": "git", "url": "https://github.com/cweagans/composer-patches.git", "reference": "e190d4466fe2b103a55467dfa83fc2fecfcaf2db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweagans/composer-patches/zipball/e190d4466fe2b103a55467dfa83fc2fecfcaf2db", "reference": "e190d4466fe2b103a55467dfa83fc2fecfcaf2db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3.0"}, "require-dev": {"composer/composer": "~1.0 || ~2.0", "phpunit/phpunit": "~4.6"}, "type": "composer-plugin", "extra": {"class": "cweagans\\Composer\\Patches"}, "autoload": {"psr-4": {"cweagans\\Composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a way to patch Composer packages.", "support": {"issues": "https://github.com/cweagans/composer-patches/issues", "source": "https://github.com/cweagans/composer-patches/tree/1.7.3"}, "time": "2022-12-20T22:53:13+00:00"}, {"name": "dasprid/enum", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/DASPRiD/Enum.git", "reference": "6faf451159fb8ba4126b925ed2d78acfce0dc016"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DASPRiD/Enum/zipball/6faf451159fb8ba4126b925ed2d78acfce0dc016", "reference": "6faf451159fb8ba4126b925ed2d78acfce0dc016", "shasum": ""}, "require": {"php": ">=7.1 <9.0"}, "require-dev": {"phpunit/phpunit": "^7 | ^8 | ^9", "squizlabs/php_codesniffer": "*"}, "type": "library", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "support": {"issues": "https://github.com/DASPRiD/Enum/issues", "source": "https://github.com/DASPRiD/Enum/tree/1.0.5"}, "time": "2023-08-25T16:18:39+00:00"}, {"name": "doctrine/annotations", "version": "1.14.3", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.3"}, "time": "2023-02-01T09:20:38+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/lexer", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:35:39+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v7.17.2", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "2d302233f2bb0926812d82823bb820d405e130fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/2d302233f2bb0926812d82823bb820d405e130fc", "reference": "2d302233f2bb0926812d82823bb820d405e130fc", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0", "LGPL-2.1-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v7.17.2"}, "time": "2023-04-21T15:31:12+00:00"}, {"name": "endroid/qr-code", "version": "4.8.5", "source": {"type": "git", "url": "https://github.com/endroid/qr-code.git", "reference": "0db25b506a8411a5e1644ebaa67123a6eb7b6a77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/endroid/qr-code/zipball/0db25b506a8411a5e1644ebaa67123a6eb7b6a77", "reference": "0db25b506a8411a5e1644ebaa67123a6eb7b6a77", "shasum": ""}, "require": {"bacon/bacon-qr-code": "^2.0.5", "php": "^8.1"}, "conflict": {"khanamiryan/qrcode-detector-decoder": "^1.0.6"}, "require-dev": {"endroid/quality": "dev-master", "ext-gd": "*", "khanamiryan/qrcode-detector-decoder": "^1.0.4||^2.0.2", "setasign/fpdf": "^1.8.2"}, "suggest": {"ext-gd": "Enables you to write PNG images", "khanamiryan/qrcode-detector-decoder": "Enables you to use the image validator", "roave/security-advisories": "Makes sure package versions with known security issues are not installed", "setasign/fpdf": "Enables you to use the PDF writer"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Endroid QR Code", "homepage": "https://github.com/endroid/qr-code", "keywords": ["code", "endroid", "php", "qr", "qrcode"], "support": {"issues": "https://github.com/endroid/qr-code/issues", "source": "https://github.com/endroid/qr-code/tree/4.8.5"}, "funding": [{"url": "https://github.com/endroid", "type": "github"}], "time": "2023-09-29T14:03:20+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/b4b5a025dfee70d6cd34c780e07330eb93d5b997", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.1.0"}, "time": "2022-10-24T12:58:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/7887fc8488013065f72f977dcb281994f5fde9f4", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.2.2"}, "time": "2022-12-07T11:28:53+00:00"}, {"name": "firebase/php-jwt", "version": "v6.10.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "a49db6f0a5033aef5143295342f1c95521b075ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/a49db6f0a5033aef5143295342f1c95521b075ff", "reference": "a49db6f0a5033aef5143295342f1c95521b075ff", "shasum": ""}, "require": {"php": "^7.4||^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.5||^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^1.0||^2.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.10.0"}, "time": "2023-12-01T16:26:39+00:00"}, {"name": "gopay/payments-sdk-php", "version": "v1.7.3", "source": {"type": "git", "url": "https://github.com/gopaycommunity/gopay-php-api.git", "reference": "2643a053c66ab82efda019154a0c63c156452f48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/gopaycommunity/gopay-php-api/zipball/2643a053c66ab82efda019154a0c63c156452f48", "reference": "2643a053c66ab82efda019154a0c63c156452f48", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.7.0", "php": ">=8.1", "symfony/deprecation-contracts": "^3.3.0"}, "require-dev": {"hamcrest/hamcrest-php": "*", "phpspec/prophecy": "~1.0", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "9.3.7"}, "type": "library", "autoload": {"files": ["factory.php"], "psr-4": {"GoPay\\": "src/"}, "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "GoPay", "homepage": "https://github.com/gopaycommunity/gopay-php-api/contributors"}], "description": "GoPay's PHP SDK for Payments REST API", "keywords": ["api", "gopay", "payments", "rest", "sdk"], "support": {"issues": "https://github.com/gopaycommunity/gopay-php-api/issues", "source": "https://github.com/gopaycommunity/gopay-php-api/tree/v1.7.3"}, "time": "2023-09-20T12:02:41+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.8.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/41042bc7ab002487b876a0683fc8dce04ddce104", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2023-12-03T20:35:24+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/bbff78d96034045e58e13dedd6ad91b5d1253223", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-12-03T20:19:20+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e4490cabc77465aaee90b20cfc9a770f8c04be6b", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.9.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-04-17T16:00:37+00:00"}, {"name": "heureka/overeno-zakazniky", "version": "v4.0.2", "source": {"type": "git", "url": "https://github.com/heureka/overeno-zakazniky.git", "reference": "30eb6f7ab47ee3068f71efbdbde4a0d4b019a0a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/heureka/overeno-zakazniky/zipball/30eb6f7ab47ee3068f71efbdbde4a0d4b019a0a2", "reference": "30eb6f7ab47ee3068f71efbdbde4a0d4b019a0a2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"mockery/mockery": "~1.4.3", "phpunit/phpunit": "~9.5.0"}, "suggest": {"ext-curl": "Simplifies the library usage (you don't have to provide your own requester)"}, "type": "library", "autoload": {"psr-4": {"Heureka\\": ["src/", "tests"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Heureka.cz", "email": "<EMAIL>"}], "description": "Heureka 'Ov<PERSON><PERSON>eno zákazníky' (ShopCertification) service API implementation", "support": {"issues": "https://github.com/heureka/overeno-zakazniky/issues", "source": "https://github.com/heureka/overeno-zakazniky/tree/v4.0.2"}, "time": "2023-10-16T07:06:56+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/8f06e92b95405216b237521cc64c804dd44c4a81", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7||^2.0", "php": ">=7.3", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/psr7": "Includes an HTTP factory starting in version 2.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "support": {"issues": "https://github.com/http-interop/http-factory-guzzle/issues", "source": "https://github.com/http-interop/http-factory-guzzle/tree/1.2.0"}, "time": "2021-07-21T13:50:14+00:00"}, {"name": "jaybizzle/crawler-detect", "version": "v1.2.116", "source": {"type": "git", "url": "https://github.com/JayBizzle/Crawler-Detect.git", "reference": "97e9fe30219e60092e107651abb379a38b342921"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JayBizzle/Crawler-Detect/zipball/97e9fe30219e60092e107651abb379a38b342921", "reference": "97e9fe30219e60092e107651abb379a38b342921", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "type": "library", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "support": {"issues": "https://github.com/JayBizzle/Crawler-Detect/issues", "source": "https://github.com/JayBizzle/Crawler-Detect/tree/v1.2.116"}, "time": "2023-07-21T15:49:49+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "f9fdd29ad8e6d024f52678b570e5593759b550b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/f9fdd29ad8e6d024f52678b570e5593759b550b4", "reference": "f9fdd29ad8e6d024f52678b570e5593759b550b4", "shasum": ""}, "require": {"composer-runtime-api": "^2.0.0", "php": "^7.1|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^7.5|^8.5|^9.4", "vimeo/psalm": "^4.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.0.6"}, "time": "2024-03-08T09:58:59+00:00"}, {"name": "koriym/attributes", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/koriym/Koriym.Attributes.git", "reference": "7f636a3a04a746ed03f6a36be644539e0d77edbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/koriym/Koriym.Attributes/zipball/7f636a3a04a746ed03f6a36be644539e0d77edbb", "reference": "7f636a3a04a746ed03f6a36be644539e0d77edbb", "shasum": ""}, "require": {"doctrine/annotations": "^1.12 || ^2.0", "php": "^7.2 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4", "ext-pdo": "*", "phpunit/phpunit": "^8.5.24 || ^9.5"}, "suggest": {"koriym/param-reader": "An attribute/annotation reader for parameters"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "target-directory": "vendor-bin", "forward-command": true}}, "autoload": {"psr-4": {"Koriym\\Attributes\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An annotation/attribute reader", "keywords": ["annotation", "attribute"], "support": {"issues": "https://github.com/koriym/Koriym.Attributes/issues", "source": "https://github.com/koriym/Koriym.Attributes/tree/1.0.5"}, "time": "2023-07-08T09:09:20+00:00"}, {"name": "latte/latte", "version": "v3.0.14", "source": {"type": "git", "url": "https://github.com/nette/latte.git", "reference": "1ab054b1af13ee647ccbc5bf2673a3483a7457d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/latte/zipball/1ab054b1af13ee647ccbc5bf2673a3483a7457d5", "reference": "1ab054b1af13ee647ccbc5bf2673a3483a7457d5", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "php": "8.0 - 8.3"}, "conflict": {"nette/application": "<3.1.7", "nette/caching": "<3.1.4"}, "require-dev": {"nette/php-generator": "^3.6 || ^4.0", "nette/tester": "^2.0", "nette/utils": "^3.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.3"}, "suggest": {"ext-fileinfo": "to use filter |datastream", "ext-iconv": "to use filters |reverse, |substring", "ext-mbstring": "to use filters like lower, upper, capitalize, ...", "nette/php-generator": "to use tag {templatePrint}", "nette/utils": "to use filter |webalize"}, "bin": ["bin/latte-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "☕ Latte: the intuitive and fast template engine for those who want the most secure PHP sites. Introduces context-sensitive escaping.", "homepage": "https://latte.nette.org", "keywords": ["context-sensitive", "engine", "escaping", "html", "nette", "security", "template", "twig"], "support": {"issues": "https://github.com/nette/latte/issues", "source": "https://github.com/nette/latte/tree/v3.0.14"}, "time": "2024-03-19T09:03:26+00:00"}, {"name": "league/csv", "version": "9.15.0", "source": {"type": "git", "url": "https://github.com/thephpleague/csv.git", "reference": "fa7e2441c0bc9b2360f4314fd6c954f7ff40d435"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/csv/zipball/fa7e2441c0bc9b2360f4314fd6c954f7ff40d435", "reference": "fa7e2441c0bc9b2360f4314fd6c954f7ff40d435", "shasum": ""}, "require": {"ext-filter": "*", "ext-json": "*", "ext-mbstring": "*", "php": "^8.1.2"}, "require-dev": {"doctrine/collections": "^2.1.4", "ext-dom": "*", "ext-xdebug": "*", "friendsofphp/php-cs-fixer": "^v3.22.0", "phpbench/phpbench": "^1.2.15", "phpstan/phpstan": "^1.10.57", "phpstan/phpstan-deprecation-rules": "^1.1.4", "phpstan/phpstan-phpunit": "^1.3.15", "phpstan/phpstan-strict-rules": "^1.5.2", "phpunit/phpunit": "^10.5.9", "symfony/var-dumper": "^6.4.2"}, "suggest": {"ext-dom": "Required to use the XMLConverter and the HTMLConverter classes", "ext-iconv": "Needed to ease transcoding CSV using iconv stream filters"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Csv\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "description": "CSV data manipulation made easy in PHP", "homepage": "https://csv.thephpleague.com", "keywords": ["convert", "csv", "export", "filter", "import", "read", "transform", "write"], "support": {"docs": "https://csv.thephpleague.com", "issues": "https://github.com/thephpleague/csv/issues", "rss": "https://github.com/thephpleague/csv/releases.atom", "source": "https://github.com/thephpleague/csv"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2024-02-20T20:00:00+00:00"}, {"name": "league/oauth2-client", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-client.git", "reference": "160d6274b03562ebeb55ed18399281d8118b76c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/160d6274b03562ebeb55ed18399281d8118b76c8", "reference": "160d6274b03562ebeb55ed18399281d8118b76c8", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0 || ^7.0", "paragonie/random_compat": "^1 || ^2 || ^9.99", "php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"mockery/mockery": "^1.3.5", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpunit/phpunit": "^5.7 || ^6.0 || ^9.5", "squizlabs/php_codesniffer": "^2.3 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.0.x-dev"}}, "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "support": {"issues": "https://github.com/thephpleague/oauth2-client/issues", "source": "https://github.com/thephpleague/oauth2-client/tree/2.7.0"}, "time": "2023-04-16T18:19:15+00:00"}, {"name": "league/oauth2-google", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-google.git", "reference": "1b01ba18ba31b29e88771e3e0979e5c91d4afe76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-google/zipball/1b01ba18ba31b29e88771e3e0979e5c91d4afe76", "reference": "1b01ba18ba31b29e88771e3e0979e5c91d4afe76", "shasum": ""}, "require": {"league/oauth2-client": "^2.0", "php": "^7.3 || ^8.0"}, "require-dev": {"eloquent/phony-phpunit": "^6.0 || ^7.1", "phpunit/phpunit": "^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://shadowhand.com"}], "description": "Google OAuth 2.0 Client Provider for The PHP League OAuth2-Client", "keywords": ["Authentication", "authorization", "client", "google", "o<PERSON>h", "oauth2"], "support": {"issues": "https://github.com/thephpleague/oauth2-google/issues", "source": "https://github.com/thephpleague/oauth2-google/tree/4.0.1"}, "time": "2023-03-17T15:20:52+00:00"}, {"name": "marc-mabe/php-enum", "version": "v4.7.0", "source": {"type": "git", "url": "https://github.com/marc-mabe/php-enum.git", "reference": "3da42cc1daceaf98c858e56f59d1ccd52b011fdc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc-mabe/php-enum/zipball/3da42cc1daceaf98c858e56f59d1ccd52b011fdc", "reference": "3da42cc1daceaf98c858e56f59d1ccd52b011fdc", "shasum": ""}, "require": {"ext-reflection": "*", "php": "^7.1 | ^8.0"}, "require-dev": {"phpbench/phpbench": "^0.16.10 || ^1.0.4", "phpstan/phpstan": "^1.3.1", "phpunit/phpunit": "^7.5.20 | ^8.5.22 | ^9.5.11", "vimeo/psalm": "^4.17.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.6-dev", "dev-3.x": "3.2-dev"}}, "autoload": {"psr-4": {"MabeEnum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mabe.berlin/", "role": "Lead"}], "description": "Simple and fast implementation of enumerations with native PHP", "homepage": "https://github.com/marc-mabe/php-enum", "keywords": ["enum", "enum-map", "enum-set", "enumeration", "enumerator", "enummap", "enumset", "map", "set", "type", "type-hint", "<PERSON><PERSON>t"], "support": {"issues": "https://github.com/marc-mabe/php-enum/issues", "source": "https://github.com/marc-mabe/php-enum/tree/v4.7.0"}, "time": "2022-04-19T02:21:46+00:00"}, {"name": "monolog/monolog", "version": "3.5.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "c915e2634718dbc8a4a15c61b0e62e7a44e14448"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/c915e2634718dbc8a4a15c61b0e62e7a44e14448", "reference": "c915e2634718dbc8a4a15c61b0e62e7a44e14448", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpstan/phpstan": "^1.9", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-strict-rules": "^1.4", "phpunit/phpunit": "^10.1", "predis/predis": "^1.1 || ^2", "ruflin/elastica": "^7", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.5.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2023-10-27T15:32:31+00:00"}, {"name": "mpdf/mpdf", "version": "v8.2.3", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "6f723a96becf989a831e38caf758d28364a69939"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/6f723a96becf989a831e38caf758d28364a69939", "reference": "6f723a96becf989a831e38caf758d28364a69939", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "mpdf/psr-http-message-shim": "^1.0 || ^2.0", "mpdf/psr-log-aware-trait": "^2.0 || ^3.0", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "~2.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "http://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2024-03-11T12:55:53+00:00"}, {"name": "mpdf/psr-http-message-shim", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/mpdf/psr-http-message-shim.git", "reference": "3206e6b80b6d2479e148ee497e9f2bebadc919db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-http-message-shim/zipball/3206e6b80b6d2479e148ee497e9f2bebadc919db", "reference": "3206e6b80b6d2479e148ee497e9f2bebadc919db", "shasum": ""}, "require": {"psr/http-message": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrHttpMessageShim\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Shim to allow support of different psr/message versions.", "support": {"issues": "https://github.com/mpdf/psr-http-message-shim/issues", "source": "https://github.com/mpdf/psr-http-message-shim/tree/1.0.0"}, "time": "2023-09-01T05:59:47+00:00"}, {"name": "mpdf/psr-log-aware-trait", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/mpdf/psr-log-aware-trait.git", "reference": "a633da6065e946cc491e1c962850344bb0bf3e78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-log-aware-trait/zipball/a633da6065e946cc491e1c962850344bb0bf3e78", "reference": "a633da6065e946cc491e1c962850344bb0bf3e78", "shasum": ""}, "require": {"psr/log": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrLogAwareTrait\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Trait to allow support of different psr/log versions.", "support": {"issues": "https://github.com/mpdf/psr-log-aware-trait/issues", "source": "https://github.com/mpdf/psr-log-aware-trait/tree/v3.0.0"}, "time": "2023-05-03T06:19:36+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2023-03-08T13:26:56+00:00"}, {"name": "nette/application", "version": "v3.1.14", "source": {"type": "git", "url": "https://github.com/nette/application.git", "reference": "0729ede7e66fad642046a3eb670d368845272573"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/application/zipball/0729ede7e66fad642046a3eb670d368845272573", "reference": "0729ede7e66fad642046a3eb670d368845272573", "shasum": ""}, "require": {"nette/component-model": "^3.0", "nette/http": "^3.0.2", "nette/routing": "^3.0.5", "nette/utils": "^3.2.1 || ~4.0.0", "php": ">=7.2"}, "conflict": {"latte/latte": "<2.7.1 || >=3.0.0 <3.0.8 || >=3.1", "nette/caching": "<3.1", "nette/di": "<3.0.7", "nette/forms": "<3.0", "nette/schema": "<1.2", "tracy/tracy": "<2.5"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "latte/latte": "^2.10.2 || ^3.0.3", "mockery/mockery": "^1.0", "nette/di": "^v3.0", "nette/forms": "^3.0", "nette/robot-loader": "^3.2", "nette/security": "^3.0", "nette/tester": "^2.3.1", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.6"}, "suggest": {"latte/latte": "Allows using Latte in templates", "nette/forms": "Allows to use Nette\\Application\\UI\\Form"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🏆 Nette Application: a full-stack component-based MVC kernel for PHP that helps you write powerful and modern web applications. Write less, have cleaner code and your work will bring you joy.", "homepage": "https://nette.org", "keywords": ["Forms", "component-based", "control", "framework", "mvc", "mvp", "nette", "presenter", "routing", "seo"], "support": {"issues": "https://github.com/nette/application/issues", "source": "https://github.com/nette/application/tree/v3.1.14"}, "time": "2023-10-09T02:45:43+00:00"}, {"name": "nette/bootstrap", "version": "v3.2.2", "source": {"type": "git", "url": "https://github.com/nette/bootstrap.git", "reference": "226e2df81b25207e382de3e89315eb17f4fe24a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/bootstrap/zipball/226e2df81b25207e382de3e89315eb17f4fe24a7", "reference": "226e2df81b25207e382de3e89315eb17f4fe24a7", "shasum": ""}, "require": {"nette/di": "^3.1", "nette/utils": "^3.2.1 || ^4.0", "php": "8.0 - 8.3"}, "conflict": {"tracy/tracy": "<2.6"}, "require-dev": {"latte/latte": "^2.8 || ^3.0", "nette/application": "^3.1", "nette/caching": "^3.0", "nette/database": "^3.0", "nette/forms": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0 || ^4.0", "nette/robot-loader": "^3.0 || ^4.0", "nette/safe-stream": "^2.2", "nette/security": "^3.0", "nette/tester": "^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"nette/robot-loader": "to use Configurator::createRobotLoader()", "tracy/tracy": "to use Configurator::enableTracy()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🅱  Nette Bootstrap: the simple way to configure and bootstrap your Nette application.", "homepage": "https://nette.org", "keywords": ["bootstrapping", "configurator", "nette"], "support": {"issues": "https://github.com/nette/bootstrap/issues", "source": "https://github.com/nette/bootstrap/tree/v3.2.2"}, "time": "2024-03-10T22:15:04+00:00"}, {"name": "nette/caching", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/nette/caching.git", "reference": "6821d74c1db82c493c02c47f6485022d79b63176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/caching/zipball/6821d74c1db82c493c02c47f6485022d79b63176", "reference": "6821d74c1db82c493c02c47f6485022d79b63176", "shasum": ""}, "require": {"nette/finder": "^2.4 || ^3.0", "nette/utils": "^3.2 || ~4.0.0", "php": "8.0 - 8.3"}, "require-dev": {"latte/latte": "^2.11 || ^3.0", "nette/di": "^3.1 || ^4.0", "nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-pdo_sqlite": "to use SQLiteStorage or SQLiteJournal"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⏱ Nette Caching: library with easy-to-use API and many cache backends.", "homepage": "https://nette.org", "keywords": ["cache", "journal", "memcached", "nette", "sqlite"], "support": {"issues": "https://github.com/nette/caching/issues", "source": "https://github.com/nette/caching/tree/v3.2.3"}, "time": "2023-09-26T11:12:20+00:00"}, {"name": "nette/component-model", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/nette/component-model.git", "reference": "9d97c0e1916bbf8e306283ab187834501fd4b1f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/component-model/zipball/9d97c0e1916bbf8e306283ab187834501fd4b1f5", "reference": "9d97c0e1916bbf8e306283ab187834501fd4b1f5", "shasum": ""}, "require": {"nette/utils": "^2.5 || ^3.0 || ~4.0.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⚛ Nette Component Model", "homepage": "https://nette.org", "keywords": ["components", "nette"], "support": {"issues": "https://github.com/nette/component-model/issues", "source": "https://github.com/nette/component-model/tree/v3.0.3"}, "time": "2023-01-09T20:16:05+00:00"}, {"name": "nette/database", "version": "v3.1.9", "source": {"type": "git", "url": "https://github.com/nette/database.git", "reference": "4a21417d545e226a8fe189b95111d23a454bd2b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/database/zipball/4a21417d545e226a8fe189b95111d23a454bd2b3", "reference": "4a21417d545e226a8fe189b95111d23a454bd2b3", "shasum": ""}, "require": {"ext-pdo": "*", "nette/caching": "^3.0", "nette/utils": "^3.2.1 || ~4.0.0", "php": "7.2 - 8.3"}, "conflict": {"nette/di": "<3.0-stable"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "mockery/mockery": "^1.3.4", "nette/di": "^v3.0", "nette/tester": "^2.4", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💾 Nette Database: layer with a familiar PDO-like API but much more powerful. Building queries, advanced joins, drivers for MySQL, PostgreSQL, SQLite, MS SQL Server and Oracle.", "homepage": "https://nette.org", "keywords": ["database", "mssql", "mysql", "nette", "notorm", "oracle", "pdo", "postgresql", "queries", "sqlite"], "support": {"issues": "https://github.com/nette/database/issues", "source": "https://github.com/nette/database/tree/v3.1.9"}, "time": "2023-11-05T19:41:36+00:00"}, {"name": "nette/di", "version": "v3.1.10", "source": {"type": "git", "url": "https://github.com/nette/di.git", "reference": "2645ec3eaa17fa2ab87c5eb4eaacb1fe6dd28284"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/di/zipball/2645ec3eaa17fa2ab87c5eb4eaacb1fe6dd28284", "reference": "2645ec3eaa17fa2ab87c5eb4eaacb1fe6dd28284", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/neon": "^3.3 || ^4.0", "nette/php-generator": "^3.5.4 || ^4.0", "nette/robot-loader": "^3.2 || ~4.0.0", "nette/schema": "^1.2.5", "nette/utils": "^3.2.5 || ~4.0.0", "php": "7.2 - 8.3"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💎 Nette Dependency Injection Container: Flexible, compiled and full-featured DIC with perfectly usable autowiring and support for all new PHP features.", "homepage": "https://nette.org", "keywords": ["compiled", "di", "dic", "factory", "ioc", "nette", "static"], "support": {"issues": "https://github.com/nette/di/issues", "source": "https://github.com/nette/di/tree/v3.1.10"}, "time": "2024-02-06T01:19:44+00:00"}, {"name": "nette/finder", "version": "v2.6.0", "source": {"type": "git", "url": "https://github.com/nette/finder.git", "reference": "991aefb42860abeab8e003970c3809a9d83cb932"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/finder/zipball/991aefb42860abeab8e003970c3809a9d83cb932", "reference": "991aefb42860abeab8e003970c3809a9d83cb932", "shasum": ""}, "require": {"nette/utils": "^2.4 || ^3.0", "php": ">=7.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔍 Nette Finder: find files and directories with an intuitive API.", "homepage": "https://nette.org", "keywords": ["filesystem", "glob", "iterator", "nette"], "support": {"issues": "https://github.com/nette/finder/issues", "source": "https://github.com/nette/finder/tree/v2.6.0"}, "time": "2022-10-13T01:31:15+00:00"}, {"name": "nette/forms", "version": "v3.1.15", "source": {"type": "git", "url": "https://github.com/nette/forms.git", "reference": "f373bcd5ea7a33672fa96035d4bf3110ab66ee44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/forms/zipball/f373bcd5ea7a33672fa96035d4bf3110ab66ee44", "reference": "f373bcd5ea7a33672fa96035d4bf3110ab66ee44", "shasum": ""}, "require": {"nette/component-model": "^3.0", "nette/http": "^3.1", "nette/utils": "^3.2.5 || ~4.0.0", "php": "7.2 - 8.3"}, "conflict": {"latte/latte": ">=3.0.0 <3.0.12 || >=3.1"}, "require-dev": {"latte/latte": "^2.10.2 || ^3.0.12", "nette/application": "^3.0", "nette/di": "^3.0", "nette/tester": "^2.4", "phpstan/phpstan-nette": "^1", "tracy/tracy": "^2.9"}, "suggest": {"ext-intl": "to use date/time controls"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📝 Nette Forms: generating, validating and processing secure forms in PHP. Handy API, fully customizable, server & client side validation and mature design.", "homepage": "https://nette.org", "keywords": ["Forms", "bootstrap", "csrf", "javascript", "nette", "validation"], "support": {"issues": "https://github.com/nette/forms/issues", "source": "https://github.com/nette/forms/tree/v3.1.15"}, "time": "2024-01-21T22:22:16+00:00"}, {"name": "nette/http", "version": "v3.2.4", "source": {"type": "git", "url": "https://github.com/nette/http.git", "reference": "d7cc833ee186d5139cde5aab43b39ee7aedd6f22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/http/zipball/d7cc833ee186d5139cde5aab43b39ee7aedd6f22", "reference": "d7cc833ee186d5139cde5aab43b39ee7aedd6f22", "shasum": ""}, "require": {"nette/utils": "^3.2.1 || ~4.0.0", "php": "7.2 - 8.3"}, "conflict": {"nette/di": "<3.0.3", "nette/schema": "<1.2"}, "require-dev": {"nette/di": "^3.0", "nette/security": "^3.0", "nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"ext-fileinfo": "to detect MIME type of uploaded files by Nette\\Http\\FileUpload", "ext-gd": "to use image function in Nette\\Http\\FileUpload", "ext-intl": "to support punycode by Nette\\Http\\Url", "ext-session": "to use Nette\\Http\\Session"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🌐 Nette Http: abstraction for HTTP request, response and session. Provides careful data sanitization and utility for URL and cookies manipulation.", "homepage": "https://nette.org", "keywords": ["cookies", "http", "nette", "proxy", "request", "response", "security", "session", "url"], "support": {"issues": "https://github.com/nette/http/issues", "source": "https://github.com/nette/http/tree/v3.2.4"}, "time": "2024-01-30T18:13:43+00:00"}, {"name": "nette/mail", "version": "v3.1.11", "source": {"type": "git", "url": "https://github.com/nette/mail.git", "reference": "804d70278458452863a2d6be4c1d5bf5f91b3950"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/mail/zipball/804d70278458452863a2d6be4c1d5bf5f91b3950", "reference": "804d70278458452863a2d6be4c1d5bf5f91b3950", "shasum": ""}, "require": {"ext-iconv": "*", "nette/utils": "^3.1 || ~4.0.0", "php": "7.1 - 8.3"}, "conflict": {"nette/di": "<3.0-stable"}, "require-dev": {"nette/di": "^3.0.0", "nette/tester": "^2.0", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.4"}, "suggest": {"ext-fileinfo": "to detect type of attached files", "ext-openssl": "to use Nette\\Mail\\DkimSigner"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📧 Nette Mail: handy email creation and transfer library for PHP with both text and MIME-compliant support.", "homepage": "https://nette.org", "keywords": ["mail", "mailer", "mime", "nette", "smtp"], "support": {"issues": "https://github.com/nette/mail/issues", "source": "https://github.com/nette/mail/tree/v3.1.11"}, "time": "2023-11-02T23:18:58+00:00"}, {"name": "nette/neon", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "457bfbf0560f600b30d9df4233af382a478bb44d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/457bfbf0560f600b30d9df4233af382a478bb44d", "reference": "457bfbf0560f600b30d9df4233af382a478bb44d", "shasum": ""}, "require": {"ext-json": "*", "php": "8.0 - 8.3"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.7"}, "bin": ["bin/neon-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍸 Nette NEON: encodes and decodes NEON file format.", "homepage": "https://ne-on.org", "keywords": ["export", "import", "neon", "nette", "yaml"], "support": {"issues": "https://github.com/nette/neon/issues", "source": "https://github.com/nette/neon/tree/v3.4.1"}, "time": "2023-09-27T08:59:11+00:00"}, {"name": "nette/nette", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/nette/nette.git", "reference": "0274f0b0cd8456297604ac5c40df6ace2d8e5910"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/nette/zipball/0274f0b0cd8456297604ac5c40df6ace2d8e5910", "reference": "0274f0b0cd8456297604ac5c40df6ace2d8e5910", "shasum": ""}, "require": {"latte/latte": "^2.10 || ^3.0", "nette/application": "^3.1", "nette/bootstrap": "^3.1", "nette/caching": "^3.1", "nette/database": "^3.1", "nette/di": "^3.1", "nette/finder": "^2.6 || ^3.0", "nette/forms": "^3.1", "nette/http": "^3.2", "nette/mail": "^3.1 || ^4.0", "nette/php-generator": "^3.6 || ^4.0", "nette/robot-loader": "^3.4 || ^4.0", "nette/safe-stream": "^2.5 || ^3.0", "nette/security": "^3.1", "nette/tokenizer": "^3.1", "nette/utils": "^3.2 || ^4.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["Nette/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "👪 Nette Framework - innovative framework for fast and easy development of secured web applications in PHP (metapackage)", "homepage": "https://nette.org", "keywords": ["framework", "metapackage", "mvc"], "support": {"issues": "https://github.com/nette/nette/issues", "source": "https://github.com/nette/nette/tree/v3.2.1"}, "time": "2023-11-05T22:07:52+00:00"}, {"name": "nette/php-generator", "version": "v4.1.4", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "b135071d8da108445e4df2fc6a75522b23c0237d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/b135071d8da108445e4df2fc6a75522b23c0237d", "reference": "b135071d8da108445e4df2fc6a75522b23c0237d", "shasum": ""}, "require": {"nette/utils": "^3.2.9 || ^4.0", "php": "8.0 - 8.3"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.4", "nikic/php-parser": "^4.18 || ^5.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"nikic/php-parser": "to use ClassType::from(withBodies: true) & ClassType::fromCode()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.3 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v4.1.4"}, "time": "2024-03-07T23:06:26+00:00"}, {"name": "nette/robot-loader", "version": "v3.4.2", "source": {"type": "git", "url": "https://github.com/nette/robot-loader.git", "reference": "970c8f82be98ec54180c88a468cd2b057855d993"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/robot-loader/zipball/970c8f82be98ec54180c88a468cd2b057855d993", "reference": "970c8f82be98ec54180c88a468cd2b057855d993", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/finder": "^2.5 || ^3.0", "nette/utils": "^3.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍀 Nette RobotLoader: high performance and comfortable autoloader that will search and autoload classes within your application.", "homepage": "https://nette.org", "keywords": ["autoload", "class", "interface", "nette", "trait"], "support": {"issues": "https://github.com/nette/robot-loader/issues", "source": "https://github.com/nette/robot-loader/tree/v3.4.2"}, "time": "2022-12-14T15:41:06+00:00"}, {"name": "nette/routing", "version": "v3.0.5", "source": {"type": "git", "url": "https://github.com/nette/routing.git", "reference": "ff709ff9ed38a14c4fe3472534526593a8461ff5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/routing/zipball/ff709ff9ed38a14c4fe3472534526593a8461ff5", "reference": "ff709ff9ed38a14c4fe3472534526593a8461ff5", "shasum": ""}, "require": {"nette/http": "^3.0 || ~4.0.0", "nette/utils": "^3.0 || ~4.0.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Routing: two-ways URL conversion", "homepage": "https://nette.org", "keywords": ["nette"], "support": {"issues": "https://github.com/nette/routing/issues", "source": "https://github.com/nette/routing/tree/v3.0.5"}, "time": "2023-10-08T21:37:46+00:00"}, {"name": "nette/safe-stream", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/nette/safe-stream.git", "reference": "b9a275f7f2517cacac6ab4360a73722340478bce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/safe-stream/zipball/b9a275f7f2517cacac6ab4360a73722340478bce", "reference": "b9a275f7f2517cacac6ab4360a73722340478bce", "shasum": ""}, "require": {"php": "8.0 - 8.3"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/loader.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette SafeStream: provides isolation for thread safe manipulation with files via native PHP functions.", "homepage": "https://nette.org", "keywords": ["atomic", "filesystem", "isolation", "nette", "safe", "thread safe"], "support": {"issues": "https://github.com/nette/safe-stream/issues", "source": "https://github.com/nette/safe-stream/tree/v3.0.1"}, "time": "2023-08-05T18:54:54+00:00"}, {"name": "nette/schema", "version": "v1.2.5", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "0462f0166e823aad657c9224d0f849ecac1ba10a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/0462f0166e823aad657c9224d0f849ecac1ba10a", "reference": "0462f0166e823aad657c9224d0f849ecac1ba10a", "shasum": ""}, "require": {"nette/utils": "^2.5.7 || ^3.1.5 ||  ^4.0", "php": "7.1 - 8.3"}, "require-dev": {"nette/tester": "^2.3 || ^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.5"}, "time": "2023-10-05T20:37:59+00:00"}, {"name": "nette/security", "version": "v3.1.8", "source": {"type": "git", "url": "https://github.com/nette/security.git", "reference": "9b8e5c76b2e738350498470c35a36a6f0d0e38d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/security/zipball/9b8e5c76b2e738350498470c35a36a6f0d0e38d6", "reference": "9b8e5c76b2e738350498470c35a36a6f0d0e38d6", "shasum": ""}, "require": {"nette/utils": "^3.2.1 || ~4.0.0", "php": "7.2 - 8.3"}, "conflict": {"nette/di": "<3.0-stable", "nette/http": "<3.1.3"}, "require-dev": {"mockery/mockery": "^1.3.6", "nette/di": "^3.0.1", "nette/http": "^3.0.0", "nette/tester": "^2.0", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔑 Nette Security: provides authentication, authorization and a role-based access control management via ACL (Access Control List)", "homepage": "https://nette.org", "keywords": ["Authentication", "acl", "authorization", "nette"], "support": {"issues": "https://github.com/nette/security/issues", "source": "https://github.com/nette/security/tree/v3.1.8"}, "time": "2023-10-18T17:05:31+00:00"}, {"name": "nette/tokenizer", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/nette/tokenizer.git", "reference": "370c5e4e2e10eb4d3e406d3a90526f821de98190"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tokenizer/zipball/370c5e4e2e10eb4d3e406d3a90526f821de98190", "reference": "370c5e4e2e10eb4d3e406d3a90526f821de98190", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"nette/tester": "~2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "<PERSON><PERSON>", "homepage": "https://nette.org", "support": {"source": "https://github.com/nette/tokenizer/tree/v3.1.1"}, "abandoned": true, "time": "2022-02-09T22:28:54+00:00"}, {"name": "nette/utils", "version": "v3.2.10", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "a4175c62652f2300c8017fb7e640f9ccb11648d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/a4175c62652f2300c8017fb7e640f9ccb11648d2", "reference": "a4175c62652f2300c8017fb7e640f9ccb11648d2", "shasum": ""}, "require": {"php": ">=7.2 <8.4"}, "conflict": {"nette/di": "<3.0.6"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "~2.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.10"}, "time": "2023-07-30T15:38:18+00:00"}, {"name": "nettrine/annotations", "version": "v0.7.0", "source": {"type": "git", "url": "https://github.com/contributte/doctrine-annotations.git", "reference": "fbb06d156a4edcbf37e4154e5b4ede079136388b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/doctrine-annotations/zipball/fbb06d156a4edcbf37e4154e5b4ede079136388b", "reference": "fbb06d156a4edcbf37e4154e5b4ede079136388b", "shasum": ""}, "require": {"contributte/di": "^0.5.0", "doctrine/annotations": "^1.6.1", "nettrine/cache": "^0.3.0", "php": ">=7.2"}, "require-dev": {"ninjify/qa": "^0.10.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan-deprecation-rules": "^0.11.0", "phpstan/phpstan-nette": "^0.11.0", "phpstan/phpstan-shim": "^0.11.5", "phpstan/phpstan-strict-rules": "^0.11.0", "phpunit/phpunit": "^8.1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.8.x-dev"}}, "autoload": {"psr-4": {"Nettrine\\Annotations\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/mabar"}], "description": "Doctrine Annotations for Nette Framework", "homepage": "https://github.com/nettrine/annotations", "keywords": ["annotations", "doctrine", "nette", "nettrine"], "support": {"issues": "https://github.com/contributte/doctrine-annotations/issues", "source": "https://github.com/contributte/doctrine-annotations/tree/v0.7.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2020-12-10T18:01:50+00:00"}, {"name": "nettrine/cache", "version": "v0.3.0", "source": {"type": "git", "url": "https://github.com/contributte/doctrine-cache.git", "reference": "8a58596de24cdd61e45866ef8f35788675f6d2bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/doctrine-cache/zipball/8a58596de24cdd61e45866ef8f35788675f6d2bc", "reference": "8a58596de24cdd61e45866ef8f35788675f6d2bc", "shasum": ""}, "require": {"contributte/di": "^0.5.0", "doctrine/cache": "^1.8.0", "php": ">=7.2"}, "require-dev": {"ninjify/qa": "^0.9.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan-deprecation-rules": "^0.11.0", "phpstan/phpstan-nette": "^0.11.0", "phpstan/phpstan-shim": "^0.11.5", "phpstan/phpstan-strict-rules": "^0.11.0", "phpunit/phpunit": "^8.1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.3.x-dev"}}, "autoload": {"psr-4": {"Nettrine\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MPL-2.0"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/mabar"}], "description": "Doctrine Cache for Nette Framework", "homepage": "https://github.com/nettrine/cache", "keywords": ["cache", "doctrine", "nette", "nettrine"], "support": {"issues": "https://github.com/contributte/doctrine-cache/issues", "source": "https://github.com/contributte/doctrine-cache/tree/v0.3.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2020-12-10T17:56:32+00:00"}, {"name": "nextras/dbal", "version": "v4.0.5", "source": {"type": "git", "url": "https://github.com/nextras/dbal.git", "reference": "a59d2ac11e12b2ab13d8739cdf4e851f868a1eb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/dbal/zipball/a59d2ac11e12b2ab13d8739cdf4e851f868a1eb0", "reference": "a59d2ac11e12b2ab13d8739cdf4e851f868a1eb0", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"mockery/mockery": "~1.3.0", "nette/caching": "~3.0", "nette/di": "~3.0", "nette/finder": "~2.5", "nette/neon": "~3.0", "nette/tester": "~2.3.1", "nette/utils": "~3.0", "phpstan/extension-installer": "1.0.5", "phpstan/phpstan": "1.0.0", "phpstan/phpstan-deprecation-rules": "1.0.0", "phpstan/phpstan-strict-rules": "1.0.0", "symfony/config": "~4.4 || ~5.0", "symfony/dependency-injection": "~4.4 || ~5.0", "symfony/http-kernel": "~4.4 || ~5.0", "tracy/tracy": "~2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"files": ["srcCompat/compatibility.php"], "psr-4": {"Nextras\\Dbal\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nextras Project", "homepage": "https://github.com/nextras/dbal/graphs/contributors"}], "description": "Nextras database abstraction layer", "homepage": "https://github.com/nextras/dbal", "keywords": ["database", "dbal", "nextras"], "support": {"issues": "https://github.com/nextras/dbal/issues", "source": "https://github.com/nextras/dbal/tree/v4.0.5"}, "funding": [{"url": "https://github.com/hrach", "type": "github"}], "time": "2022-11-03T20:40:25+00:00"}, {"name": "nextras/migrations", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/nextras/migrations.git", "reference": "c1a201d8b845edfc3bb210a9c5f19375743af4cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/migrations/zipball/c1a201d8b845edfc3bb210a9c5f19375743af4cf", "reference": "c1a201d8b845edfc3bb210a9c5f19375743af4cf", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"dibi/dibi": "~3.0 | ~4.0 | ~5.0", "doctrine/cache": "~1.11 | ~2.0", "doctrine/dbal": "~2.5 | ~3.0", "doctrine/orm": "~2.5", "ext-openssl": "*", "mockery/mockery": "~1.3", "nette/database": "~2.4 | ~3.0", "nette/di": "~2.4.10 | ~3.0", "nette/tester": "~2.3", "nette/utils": "~2.3 | ~3.0 | ~4.0", "nextras/dbal": "~1.0 | ~2.0 | ~3.0 | ~4.0 | ~5.0@dev", "symfony/config": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0 | ~7.0", "symfony/console": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0 | ~7.0", "symfony/dependency-injection": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0 | ~7.0", "symfony/framework-bundle": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0 | ~7.0", "symfony/http-kernel": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0 | ~7.0", "tracy/tracy": "~2.6"}, "suggest": {"dibi/dibi": "to use DibiAdapter", "doctrine/dbal": "to use DoctrineAdapter", "doctrine/orm": "to generate migrations with Doctrine SchemaTool", "nette/database": "to use NetteAdapter", "nextras/dbal": "to use NextrasAdapter", "symfony/console": "to use Symfony commands"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Nextras\\Migrations\\": "src/"}, "classmap": ["src/exceptions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Database migrations runner", "support": {"issues": "https://github.com/nextras/migrations/issues", "source": "https://github.com/nextras/migrations/tree/v3.3.1"}, "time": "2024-01-25T13:14:56+00:00"}, {"name": "nextras/orm", "version": "v4.0.7", "source": {"type": "git", "url": "https://github.com/nextras/orm.git", "reference": "28c98dd0f6ac559983b4c7f40d1dc2871e4c3c0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/orm/zipball/28c98dd0f6ac559983b4c7f40d1dc2871e4c3c0a", "reference": "28c98dd0f6ac559983b4c7f40d1dc2871e4c3c0a", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "nette/caching": "~2.5 || ~3.0", "nette/tokenizer": "~2.3 || ~3.0", "nette/utils": "~3.0 || ~4.0", "nextras/dbal": "~4.0@dev", "php": ">=7.1"}, "require-dev": {"marc-mabe/php-enum": "~3.0", "marc-mabe/php-enum-phpstan": "2.0.0", "mockery/mockery": "~1.2", "nette/bootstrap": "~2.4 || ~3.0", "nette/di": "~3.0", "nette/finder": "~2.4 || ~3.0", "nette/neon": "~2.4 || ~3.0", "nette/tester": "~2.3.5", "nextras/orm-phpstan": "1.0.0", "phpstan/extension-installer": "1.0.5", "phpstan/phpstan": "1.1.2", "phpstan/phpstan-deprecation-rules": "1.0.0", "phpstan/phpstan-mockery": "1.0.0", "phpstan/phpstan-nette": "1.0.0", "phpstan/phpstan-strict-rules": "1.0.0", "tracy/tracy": "~2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"files": ["srcCompat/compatibility.php"], "psr-4": {"Nextras\\Orm\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nextras Project", "homepage": "https://github.com/nextras/orm/graphs/contributors"}], "description": "Nextras Orm framework", "homepage": "https://github.com/nextras/orm", "keywords": ["database", "nextras", "orm"], "support": {"issues": "https://github.com/nextras/orm/issues", "source": "https://github.com/nextras/orm/tree/v4.0.7"}, "funding": [{"url": "https://github.com/hrach", "type": "github"}], "time": "2023-07-01T10:25:02+00:00"}, {"name": "nyholm/dsn", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/Nyholm/dsn.git", "reference": "9445621b426bac8c0ca161db8cd700da00a4e618"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/dsn/zipball/9445621b426bac8c0ca161db8cd700da00a4e618", "reference": "9445621b426bac8c0ca161db8cd700da00a4e618", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"symfony/phpunit-bridge": "^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Nyholm\\Dsn\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parse your DSN strings in a powerful and flexible way", "homepage": "http://tnyholm.se", "keywords": ["database", "dsn", "dsn parser", "parser"], "support": {"issues": "https://github.com/Nyholm/dsn/issues", "source": "https://github.com/Nyholm/dsn/tree/2.0.1"}, "funding": [{"url": "https://github.com/Nyholm", "type": "github"}], "time": "2021-11-18T09:23:29+00:00"}, {"name": "ontob/qrpayment", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/ontob/qrpayment.git", "reference": "c51183a507cd84216688bed6f2e35b8dc5e9511d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ontob/qrpayment/zipball/c51183a507cd84216688bed6f2e35b8dc5e9511d", "reference": "c51183a507cd84216688bed6f2e35b8dc5e9511d", "shasum": ""}, "require": {"endroid/qr-code": "^4", "php": "^7.4||^8.0"}, "require-dev": {"php": "^7.4||^8.0"}, "type": "library", "autoload": {"psr-4": {"Ontob\\QrPayment\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "QR payments code library", "support": {"issues": "https://github.com/ontob/qrpayment/issues", "source": "https://github.com/ontob/qrpayment/tree/v2.0.0"}, "time": "2022-05-23T08:15:16+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "pelago/emogrifier", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/MyIntervals/emogrifier.git", "reference": "727bdf7255b51798307f17dec52ff8a91f1c7de3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/emogrifier/zipball/727bdf7255b51798307f17dec52ff8a91f1c7de3", "reference": "727bdf7255b51798307f17dec52ff8a91f1c7de3", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "sabberworm/php-css-parser": "^8.4.0", "symfony/css-selector": "^4.4.23 || ^5.4.0 || ^6.0.0 || ^7.0.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "1.3.2", "phpunit/phpunit": "9.6.11", "rawr/cross-data-providers": "2.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "8.0.x-dev"}}, "autoload": {"psr-4": {"Pelago\\Emogrifier\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "Converts CSS styles into inline style attributes in your HTML code", "homepage": "https://www.myintervals.com/emogrifier.php", "keywords": ["css", "email", "pre-processing"], "support": {"issues": "https://github.com/MyIntervals/emogrifier/issues", "source": "https://github.com/MyIntervals/emogrifier"}, "time": "2023-12-06T02:00:20+00:00"}, {"name": "php-curl-class/php-curl-class", "version": "9.19.1", "source": {"type": "git", "url": "https://github.com/php-curl-class/php-curl-class.git", "reference": "680afbcfd964f299fb320dba6c30f9930d1d478e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-curl-class/php-curl-class/zipball/680afbcfd964f299fb320dba6c30f9930d1d478e", "reference": "680afbcfd964f299fb320dba6c30f9930d1d478e", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=7.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "*", "ext-gd": "*", "friendsofphp/php-cs-fixer": "*", "phpcompatibility/php-compatibility": "dev-develop", "phpcsstandards/phpcsutils": "@alpha", "phpunit/phpunit": "*", "squizlabs/php_codesniffer": "*", "vimeo/psalm": ">=0.3.63"}, "suggest": {"ext-mbstring": "*"}, "type": "library", "autoload": {"psr-4": {"Curl\\": "src/Curl/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Unlicense"], "authors": [{"name": "<PERSON>"}, {"name": "Contributors", "homepage": "https://github.com/php-curl-class/php-curl-class/graphs/contributors"}], "description": "PHP Curl Class makes it easy to send HTTP requests and integrate with web APIs.", "homepage": "https://github.com/php-curl-class/php-curl-class", "keywords": ["API-Client", "api", "class", "client", "curl", "framework", "http", "http-client", "http-proxy", "json", "php", "php-curl", "php-curl-library", "proxy", "requests", "restful", "web-scraper", "web-service", "xml"], "support": {"issues": "https://github.com/php-curl-class/php-curl-class/issues", "source": "https://github.com/php-curl-class/php-curl-class/tree/9.19.1"}, "time": "2024-02-27T18:03:38+00:00"}, {"name": "php-http/client-common", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "1e19c059b0e4d5f717bf5d524d616165aeab0612"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/1e19c059b0e4d5f717bf5d524d616165aeab0612", "reference": "1e19c059b0e4d5f717bf5d524d616165aeab0612", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "symfony/options-resolver": "~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.20 || ^8.5.33 || ^9.6.7"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "support": {"issues": "https://github.com/php-http/client-common/issues", "source": "https://github.com/php-http/client-common/tree/2.7.1"}, "time": "2023-11-30T10:31:25+00:00"}, {"name": "php-http/discovery", "version": "1.19.2", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "61e1a1eb69c92741f5896d9e05fb8e9d7e8bb0cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/61e1a1eb69c92741f5896d9e05fb8e9d7e8bb0cb", "reference": "61e1a1eb69c92741f5896d9e05fb8e9d7e8bb0cb", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "symfony/phpunit-bridge": "^6.2"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.19.2"}, "time": "2023-11-30T16:49:05+00:00"}, {"name": "php-http/httplug", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/625ad742c360c8ac580fcc647a1541d29e257f67", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.0"}, "time": "2023-04-14T15:10:03+00:00"}, {"name": "php-http/message", "version": "1.16.1", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "5997f3289332c699fa2545c427826272498a2088"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/5997f3289332c699fa2545c427826272498a2088", "reference": "5997f3289332c699fa2545c427826272498a2088", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.1"}, "time": "2024-03-07T13:22:09+00:00"}, {"name": "php-http/message-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/1.1.0"}, "abandoned": "psr/http-factory", "time": "2023-04-14T14:16:17+00:00"}, {"name": "php-http/promise", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.1"}, "time": "2024-03-15T13:55:21+00:00"}, {"name": "predis/predis", "version": "v2.2.2", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "b1d3255ed9ad4d7254f9f9bba386c99f4bb983d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/b1d3255ed9ad4d7254f9f9bba386c99f4bb983d1", "reference": "b1d3255ed9ad4d7254f9f9bba386c99f4bb983d1", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.3", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^8.0 || ~9.4.4"}, "suggest": {"ext-relay": "Faster connection with in-memory caching (>=0.6.2)"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "A flexible and feature-complete Redis client for PHP.", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.2.2"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2023-09-13T16:42:03+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "e616d01114759c4c489f93b099585439f795fe35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/e616d01114759c4c489f93b099585439f795fe35", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/1.0.2"}, "time": "2023-04-10T20:10:41+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.0"}, "time": "2021-07-14T16:46:02+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "react/promise", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.11.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-16T16:16:50+00:00"}, {"name": "ruflin/elastica", "version": "7.3.2", "source": {"type": "git", "url": "https://github.com/ruflin/Elastica.git", "reference": "84ba137678707a1aa4242d12bad891dc38fa2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ruflin/Elastica/zipball/84ba137678707a1aa4242d12bad891dc38fa2608", "reference": "84ba137678707a1aa4242d12bad891dc38fa2608", "shasum": ""}, "require": {"elasticsearch/elasticsearch": "^7.10", "ext-json": "*", "nyholm/dsn": "^2.0.0", "php": "^7.2 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2.2 || ^3.0", "symfony/polyfill-php73": "^1.19"}, "require-dev": {"aws/aws-sdk-php": "^3.155", "guzzlehttp/guzzle": "^6.3 || ^7.2", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5.8 || ^9.4", "symfony/phpunit-bridge": "^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow using IAM authentication with Amazon ElasticSearch Service", "guzzlehttp/guzzle": "Allow using guzzle as transport", "monolog/monolog": "Logging request"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0.x-dev"}}, "autoload": {"psr-4": {"Elastica\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://ruflin.com/"}], "description": "Elasticsearch Client", "homepage": "http://elastica.io/", "keywords": ["client", "search"], "support": {"issues": "https://github.com/ruflin/Elastica/issues", "source": "https://github.com/ruflin/Elastica/tree/7.3.2"}, "time": "2024-03-11T14:11:50+00:00"}, {"name": "sabberworm/php-css-parser", "version": "v8.5.1", "source": {"type": "git", "url": "https://github.com/MyIntervals/PHP-CSS-Parser.git", "reference": "4a3d572b0f8b28bb6fd016ae8bbfc445facef152"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/PHP-CSS-Parser/zipball/4a3d572b0f8b28bb6fd016ae8bbfc445facef152", "reference": "4a3d572b0f8b28bb6fd016ae8bbfc445facef152", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.6.20"}, "require-dev": {"phpunit/phpunit": "^5.7.27"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}, "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/MyIntervals/PHP-CSS-Parser/issues", "source": "https://github.com/MyIntervals/PHP-CSS-Parser/tree/v8.5.1"}, "time": "2024-02-15T16:41:13+00:00"}, {"name": "sentry/sdk", "version": "3.6.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php-sdk.git", "reference": "24c235ff2027401cbea099bf88689e1a1f197c7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php-sdk/zipball/24c235ff2027401cbea099bf88689e1a1f197c7a", "reference": "24c235ff2027401cbea099bf88689e1a1f197c7a", "shasum": ""}, "require": {"http-interop/http-factory-guzzle": "^1.0", "sentry/sentry": "^3.22", "symfony/http-client": "^4.3|^5.0|^6.0|^7.0"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "This is a metapackage shipping sentry/sentry with a recommended HTTP client.", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php-sdk/issues", "source": "https://github.com/getsentry/sentry-php-sdk/tree/3.6.0"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2023-12-04T10:49:33+00:00"}, {"name": "sentry/sentry", "version": "3.22.1", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "8859631ba5ab15bc1af420b0eeed19ecc6c9d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/8859631ba5ab15bc1af420b0eeed19ecc6c9d81d", "reference": "8859631ba5ab15bc1af420b0eeed19ecc6c9d81d", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "guzzlehttp/promises": "^1.5.3|^2.0", "jean85/pretty-package-versions": "^1.5|^2.0.4", "php": "^7.2|^8.0", "php-http/async-client-implementation": "^1.0", "php-http/client-common": "^1.5|^2.0", "php-http/discovery": "^1.15", "php-http/httplug": "^1.1|^2.0", "php-http/message": "^1.5", "php-http/message-factory": "^1.1", "psr/http-factory": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "symfony/options-resolver": "^3.4.43|^4.4.30|^5.0.11|^6.0|^7.0", "symfony/polyfill-php80": "^1.17"}, "conflict": {"php-http/client-common": "1.8.0", "raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19|3.4.*", "guzzlehttp/psr7": "^1.8.4|^2.1.1", "http-interop/http-factory-guzzle": "^1.0", "monolog/monolog": "^1.6|^2.0|^3.0", "nikic/php-parser": "^4.10.3", "php-http/mock-client": "^1.3", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.3", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5.14|^9.4", "symfony/phpunit-bridge": "^5.2|^6.0", "vimeo/psalm": "^4.17"}, "suggest": {"monolog/monolog": "Allow sending log messages to Sentry by using the included Monolog handler."}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Sentry\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "A PHP SDK for Sentry (http://sentry.io)", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php/issues", "source": "https://github.com/getsentry/sentry-php/tree/3.22.1"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2023-11-13T11:47:28+00:00"}, {"name": "setasign/fpdi", "version": "v2.6.0", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "a6db878129ec6c7e141316ee71872923e7f1b7ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/a6db878129ec6c7e141316ee71872923e7f1b7ad", "reference": "a6db878129ec6c7e141316ee71872923e7f1b7ad", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8.6", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.6.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2023-12-11T16:03:32+00:00"}, {"name": "symfony/cache", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "0ef36534694c572ff526d91c7181f3edede176e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/0ef36534694c572ff526d91c7181f3edede176e7", "reference": "0ef36534694c572ff526d91c7181f3edede176e7", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.3.6|^7.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/var-dumper": "<5.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-22T20:27:10+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "1d74b127da04ffa87aa940abe15446fa89653778"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/1d74b127da04ffa87aa940abe15446fa89653778", "reference": "1d74b127da04ffa87aa940abe15446fa89653778", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-09-25T12:52:38+00:00"}, {"name": "symfony/clock", "version": "v7.0.5", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "8b9d08887353d627d5f6c3bf3373b398b49051c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/8b9d08887353d627d5f6c3bf3373b398b49051c2", "reference": "8b9d08887353d627d5f6c3bf3373b398b49051c2", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.0.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-02T12:46:12+00:00"}, {"name": "symfony/config", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "6ea4affc27f2086c9d16b92ab5429ce1e3c38047"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/6ea4affc27f2086c9d16b92ab5429ce1e3c38047", "reference": "6ea4affc27f2086c9d16b92ab5429ce1e3c38047", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<5.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-26T07:52:26+00:00"}, {"name": "symfony/console", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "0d9e4eb5ad413075624378f474c4167ea202de78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/0d9e4eb5ad413075624378f474c4167ea202de78", "reference": "0d9e4eb5ad413075624378f474c4167ea202de78", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-22T20:27:10+00:00"}, {"name": "symfony/css-selector", "version": "v7.0.3", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "ec60a4edf94e63b0556b6a0888548bb400a3a3be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/ec60a4edf94e63b0556b6a0888548bb400a3a3be", "reference": "ec60a4edf94e63b0556b6a0888548bb400a3a3be", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.0.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T15:02:46+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/7c3aff79d10325257a001fcf92d991f24fc967cf", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-23T14:45:45+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "ae9d3a6f3003a6caf56acd7466d8d52378d44fef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ae9d3a6f3003a6caf56acd7466d8d52378d44fef", "reference": "ae9d3a6f3003a6caf56acd7466d8d52378d44fef", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "a76aed96a42d2b521153fb382d418e30d18b59df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/a76aed96a42d2b521153fb382d418e30d18b59df", "reference": "a76aed96a42d2b521153fb382d418e30d18b59df", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-23T14:45:45+00:00"}, {"name": "symfony/filesystem", "version": "v7.0.3", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "2890e3a825bc0c0558526c04499c13f83e1b6b12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/2890e3a825bc0c0558526c04499c13f83e1b6b12", "reference": "2890e3a825bc0c0558526c04499c13f83e1b6b12", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v7.0.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T15:02:46+00:00"}, {"name": "symfony/http-client", "version": "v7.0.5", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "425f462a59d8030703ee04a9e1c666575ed5db3b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/425f462a59d8030703ee04a9e1c666575ed5db3b", "reference": "425f462a59d8030703ee04a9e1c666575ed5db3b", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^3", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-foundation": "<6.4"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-02T12:46:12+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "1ee70e699b41909c209a0c930f11034b93578654"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/1ee70e699b41909c209a0c930f11034b93578654", "reference": "1ee70e699b41909c209a0c930f11034b93578654", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-07-30T20:28:31+00:00"}, {"name": "symfony/lock", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/lock.git", "reference": "1cabf3cc775b1aa6008ebd471fa773444af4e956"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/lock/zipball/1cabf3cc775b1aa6008ebd471fa773444af4e956", "reference": "1cabf3cc775b1aa6008ebd471fa773444af4e956", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"doctrine/dbal": "<2.13", "symfony/cache": "<6.2"}, "require-dev": {"doctrine/dbal": "^2.13|^3|^4", "predis/predis": "^1.1|^2.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Lock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Creates and manages locks, a mechanism to provide exclusive access to a shared resource", "homepage": "https://symfony.com", "keywords": ["cas", "flock", "locking", "mutex", "redlock", "semaphore"], "support": {"source": "https://github.com/symfony/lock/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/messenger", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/messenger.git", "reference": "443b2644a3f43678adb5281a4e3fae6fbf2473c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/messenger/zipball/443b2644a3f43678adb5281a4e3fae6fbf2473c7", "reference": "443b2644a3f43678adb5281a4e3fae6fbf2473c7", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/clock": "^6.3|^7.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/console": "<6.3", "symfony/event-dispatcher": "<5.4", "symfony/event-dispatcher-contracts": "<2.5", "symfony/framework-bundle": "<5.4", "symfony/http-kernel": "<5.4", "symfony/serializer": "<5.4"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/console": "^6.3|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/validator": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps applications send and receive messages to/from other applications or via message queues", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/messenger/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-26T07:52:26+00:00"}, {"name": "symfony/options-resolver", "version": "v7.0.0", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "700ff4096e346f54cb628ea650767c8130f1001f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/700ff4096e346f54cb628ea650767c8130f1001f", "reference": "700ff4096e346f54cb628ea650767c8130f1001f", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v7.0.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-08-08T10:20:21+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ef4d7e442ca910c4764bce785146269b30cb5fc4", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/32a9da87d7b3245e09ac426c83d334ae9f06f80f", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/bc45c394692b948b4d383a08d7753968bed9a83d", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "21bd091060673a1177ae842c0ef8fe30893114d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/21bd091060673a1177ae842c0ef8fe30893114d2", "reference": "21bd091060673a1177ae842c0ef8fe30893114d2", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/86fcae159633351e5fd145d1c47de6c528f8caff", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-php80": "^1.14"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/property-access", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "c0664db266024013e31446dd690b6bfcf218ad93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/c0664db266024013e31446dd690b6bfcf218ad93", "reference": "c0664db266024013e31446dd690b6bfcf218ad93", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/property-info": "^5.4|^6.0|^7.0"}, "require-dev": {"symfony/cache": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property-path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-16T13:31:43+00:00"}, {"name": "symfony/property-info", "version": "v7.0.3", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "e160f92ea827243abf2dbf36b8460b1377194406"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/e160f92ea827243abf2dbf36b8460b1377194406", "reference": "e160f92ea827243abf2dbf36b8460b1377194406", "shasum": ""}, "require": {"php": ">=8.2", "symfony/string": "^6.4|^7.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.5.1", "symfony/dependency-injection": "<6.4", "symfony/serializer": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v7.0.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T15:02:46+00:00"}, {"name": "symfony/redis-messenger", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/symfony/redis-messenger.git", "reference": "54b107003c5abc03cc5077c8847678b432b1e602"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/redis-messenger/zipball/54b107003c5abc03cc5077c8847678b432b1e602", "reference": "54b107003c5abc03cc5077c8847678b432b1e602", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/messenger": "^5.1|^6.0"}, "require-dev": {"symfony/property-access": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Redis\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Redis extension Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/redis-messenger/tree/v5.4.36"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-05T13:56:32+00:00"}, {"name": "symfony/service-contracts", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "fe07cbc8d837f60caf7018068e350cc5163681a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/fe07cbc8d837f60caf7018068e350cc5163681a0", "reference": "fe07cbc8d837f60caf7018068e350cc5163681a0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.4.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-12-26T14:02:43+00:00"}, {"name": "symfony/string", "version": "v7.0.4", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "f5832521b998b0bec40bee688ad5de98d4cf111b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/f5832521b998b0bec40bee688ad5de98d4cf111b", "reference": "f5832521b998b0bec40bee688ad5de98d4cf111b", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.0.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-01T13:17:36+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "06450585bf65e978026bda220cdebca3f867fde7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/06450585bf65e978026bda220cdebca3f867fde7", "reference": "06450585bf65e978026bda220cdebca3f867fde7", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.4.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-12-26T14:02:43+00:00"}, {"name": "symfony/validator", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "1cf92edc9a94d16275efef949fa6748d11cc8f47"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/1cf92edc9a94d16275efef949fa6748d11cc8f47", "reference": "1cf92edc9a94d16275efef949fa6748d11cc8f47", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.13", "doctrine/lexer": "<1.1", "symfony/dependency-injection": "<5.4", "symfony/expression-language": "<5.4", "symfony/http-kernel": "<5.4", "symfony/intl": "<5.4", "symfony/property-info": "<5.4", "symfony/translation": "<5.4.35|>=6.0,<6.3.12|>=6.4,<6.4.3|>=7.0,<7.0.3", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.13|^2", "egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4.35|~6.3.12|^6.4.3|^7.0.3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-22T20:27:10+00:00"}, {"name": "symfony/var-dumper", "version": "v6.4.11", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "ee14c8254a480913268b1e3b1cba8045ed122694"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/ee14c8254a480913268b1e3b1cba8045ed122694", "reference": "ee14c8254a480913268b1e3b1cba8045ed122694", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0|^7.0", "symfony/error-handler": "^6.3|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "twig/twig": "^2.13|^3.0.4"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-08-30T16:03:21+00:00"}, {"name": "symfony/var-exporter", "version": "v7.0.4", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "dfb0acb6803eb714f05d97dd4c5abe6d5fa9fe41"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/dfb0acb6803eb714f05d97dd4c5abe6d5fa9fe41", "reference": "dfb0acb6803eb714f05d97dd4c5abe6d5fa9fe41", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.0.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-26T10:35:24+00:00"}, {"name": "texy/texy", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/dg/texy.git", "reference": "99f4261956fda6fb3a28c1264ff33abce725044b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/texy/zipball/99f4261956fda6fb3a28c1264ff33abce725044b", "reference": "99f4261956fda6fb3a28c1264ff33abce725044b", "shasum": ""}, "require": {"php": "8.1 - 8.3"}, "conflict": {"latte/latte": ">=3.0.0 <3.0.14"}, "replace": {"dg/texy": "*"}, "require-dev": {"latte/latte": "^2.6 || ^3.0.14", "nette/tester": "^2.5", "phpstan/phpstan": "^1", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Texy converts plain text in easy to read Texy syntax into structurally valid (X)HTML. It supports adding of images, links, nested lists, tables and has full support for CSS. Texy supports hyphenation of long words (which reflects language rules), clickable emails and URL (emails are obfuscated against spambots), national typographic single and double quotation marks, ellipses, em dashes, dimension sign, nonbreakable spaces (e.g. in phone numbers), acronyms, arrows and many others. Texy code can optionally contain HTML tags.", "homepage": "https://texy.info", "keywords": ["html", "markdown", "markup language", "plain text", "text", "textile", "texy", "wiki"], "support": {"source": "https://github.com/dg/texy/tree/v3.2.1"}, "time": "2024-03-19T12:03:31+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "v2.2.7", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/83ee6f38df0a63106a9e4536e3060458b74ccedb", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5 || ^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.2.7"}, "time": "2023-12-08T13:03:43+00:00"}, {"name": "tracy/tracy", "version": "v2.10.5", "source": {"type": "git", "url": "https://github.com/nette/tracy.git", "reference": "86bdba4aa0f707d3f125933931d3df6e5c7aad79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tracy/zipball/86bdba4aa0f707d3f125933931d3df6e5c7aad79", "reference": "86bdba4aa0f707d3f125933931d3df6e5c7aad79", "shasum": ""}, "require": {"ext-json": "*", "ext-session": "*", "php": ">=8.0 <8.4"}, "conflict": {"nette/di": "<3.0"}, "require-dev": {"latte/latte": "^2.5", "nette/di": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0", "nette/tester": "^2.2", "nette/utils": "^3.0", "phpstan/phpstan": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.10-dev"}}, "autoload": {"files": ["src/Tracy/functions.php"], "classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "😎  Tracy: the addictive tool to ease debugging PHP code for cool developers. Friendly design, logging, profiler, advanced features like debugging AJAX calls or CLI support. You will love it.", "homepage": "https://tracy.nette.org", "keywords": ["Xdebug", "debug", "debugger", "nette", "profiler"], "support": {"issues": "https://github.com/nette/tracy/issues", "source": "https://github.com/nette/tracy/tree/v2.10.5"}, "time": "2023-10-16T21:54:18+00:00"}, {"name": "ublaboo/datagrid", "version": "v6.10.0", "source": {"type": "git", "url": "https://github.com/contributte/datagrid.git", "reference": "1a63088529f47692d57c186f43a9e084f4b52e88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/datagrid/zipball/1a63088529f47692d57c186f43a9e084f4b52e88", "reference": "1a63088529f47692d57c186f43a9e084f4b52e88", "shasum": ""}, "require": {"contributte/application": "^0.5.0", "nette/di": "^3.0.0", "nette/forms": "^3.1.3", "nette/utils": "^3.0.1 || ^4.0.0", "php": ">=7.2", "symfony/property-access": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0"}, "require-dev": {"contributte/code-rules": "^1.1.0", "dibi/dibi": "^3.0.0 || ^4.0.0", "doctrine/annotations": "^1.12.1", "doctrine/cache": "^1.11.0", "doctrine/orm": "^2.11.1", "elasticsearch/elasticsearch": "^7.1", "mockery/mockery": "^1.3.3", "nette/database": "^3.0.2", "nette/tester": "^2.3.4", "nextras/dbal": "^3.0.1 || ^4.0", "nextras/orm": "^3.1.0 || ^4.0", "ninjify/coding-standard": "^0.12.1", "phpstan/phpstan-nette": "^1.0.0", "tharos/leanmapper": "^3.4.2 || ^4.0.0", "tracy/tracy": "^2.6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.10.x-dev"}}, "autoload": {"psr-4": {"Ublaboo\\DataGrid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://paveljanda.com"}, {"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "DataGrid for Nette Framework: filtering, sorting, pagination, tree view, table view, translator, etc", "keywords": ["contributte", "data", "datagrid", "grid", "nette", "table"], "support": {"issues": "https://github.com/contributte/datagrid/issues", "source": "https://github.com/contributte/datagrid/tree/v6.10.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-03-03T16:52:06+00:00"}], "packages-dev": [{"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v0.7.2", "source": {"type": "git", "url": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer.git", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Dealerdirect/phpcodesniffer-composer-installer/zipball/1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0"}, "type": "composer-plugin", "extra": {"class": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/dealerdirect/phpcodesniffer-composer-installer/issues", "source": "https://github.com/dealerdirect/phpcodesniffer-composer-installer"}, "time": "2022-02-04T12:51:07+00:00"}, {"name": "deployer/deployer", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/deployphp/deployer.git", "reference": "3535bdb2f6360662bd95f6e26fce31dbc269af64"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/deployphp/deployer/zipball/3535bdb2f6360662bd95f6e26fce31dbc269af64", "reference": "3535bdb2f6360662bd95f6e26fce31dbc269af64", "shasum": ""}, "bin": ["dep"], "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Deployment Tool", "homepage": "https://deployer.org", "support": {"docs": "https://deployer.org/docs", "issues": "https://github.com/deployphp/deployer/issues", "source": "https://github.com/deployphp/deployer"}, "funding": [{"url": "https://github.com/sponsors/antonmedv", "type": "github"}], "time": "2023-11-07T10:27:12+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "mockery/mockery", "version": "1.6.10", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "47065d1be1fa05def58dc14c03cf831d3884ef0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/47065d1be1fa05def58dc14c03cf831d3884ef0b", "reference": "47065d1be1fa05def58dc14c03cf831d3884ef0b", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "type": "library", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "time": "2024-03-19T16:15:45+00:00"}, {"name": "nette/tester", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/nette/tester.git", "reference": "328d7b64579cdbc82e0e01d92ea9c58b9cf0a327"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tester/zipball/328d7b64579cdbc82e0e01d92ea9c58b9cf0a327", "reference": "328d7b64579cdbc82e0e01d92ea9c58b9cf0a327", "shasum": ""}, "require": {"php": ">=8.0 <8.4"}, "require-dev": {"ext-simplexml": "*", "phpstan/phpstan": "^1.0"}, "bin": ["src/tester"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/milo"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Tester: enjoyable unit testing in PHP with code coverage reporter. 🍏🍏🍎🍏", "homepage": "https://tester.nette.org", "keywords": ["Xdebug", "assertions", "clover", "code coverage", "nette", "pcov", "phpdbg", "phpunit", "testing", "unit"], "support": {"issues": "https://github.com/nette/tester/issues", "source": "https://github.com/nette/tester/tree/v2.5.2"}, "time": "2024-01-08T11:41:26+00:00"}, {"name": "nextras/orm-phpstan", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/nextras/orm-phpstan.git", "reference": "e215076ccf92632f8bd2aa4b79f4a172a338c83f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/orm-phpstan/zipball/e215076ccf92632f8bd2aa4b79f4a172a338c83f", "reference": "e215076ccf92632f8bd2aa4b79f4a172a338c83f", "shasum": ""}, "require": {"php": "~7.1 || ~8.0", "phpstan/phpstan": "^1.0"}, "require-dev": {"nette/tester": "^2.3.1", "nextras/orm": "~4.0 || ~5.0@dev", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Nextras\\OrmPhpStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan extension for Nextras Orm", "support": {"issues": "https://github.com/nextras/orm-phpstan/issues", "source": "https://github.com/nextras/orm-phpstan/tree/v1.0.1"}, "time": "2022-05-01T21:42:32+00:00"}, {"name": "ninjify/coding-standard", "version": "v0.12.1", "source": {"type": "git", "url": "https://github.com/ninjify/coding-standard.git", "reference": "c655eedbe1b4f9b307e9941ad347f9078fbdd58a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ninjify/coding-standard/zipball/c655eedbe1b4f9b307e9941ad347f9078fbdd58a", "reference": "c655eedbe1b4f9b307e9941ad347f9078fbdd58a", "shasum": ""}, "require": {"php": ">=7.2", "slevomat/coding-standard": "^7.0.18", "squizlabs/php_codesniffer": "^3.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.13.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Tuned & very strict coding standards for PHP projects. Trusted by Contributte, Apitte, Nettrine and many others.", "homepage": "https://github.com/ninjify/coding-standard", "keywords": ["Codestyle", "codesniffer", "ninji<PERSON>", "php"], "support": {"issues": "https://github.com/ninjify/coding-standard/issues", "source": "https://github.com/ninjify/coding-standard/tree/v0.12.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-02-11T14:34:15+00:00"}, {"name": "php-parallel-lint/php-console-color", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Console-Color.git", "reference": "7adfefd530aa2d7570ba87100a99e2483a543b88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Console-Color/zipball/7adfefd530aa2d7570ba87100a99e2483a543b88", "reference": "7adfefd530aa2d7570ba87100a99e2483a543b88", "shasum": ""}, "require": {"php": ">=5.3.2"}, "replace": {"jakub-onderka/php-console-color": "*"}, "require-dev": {"php-parallel-lint/php-code-style": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.0", "php-parallel-lint/php-var-dump-check": "0.*", "phpunit/phpunit": "^4.8.36 || ^5.7.21 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"PHP_Parallel_Lint\\PhpConsoleColor\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Simple library for creating colored console ouput.", "support": {"issues": "https://github.com/php-parallel-lint/PHP-Console-Color/issues", "source": "https://github.com/php-parallel-lint/PHP-Console-Color/tree/v1.0.1"}, "time": "2021-12-25T06:49:29+00:00"}, {"name": "php-parallel-lint/php-console-highlighter", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Console-Highlighter.git", "reference": "5b4803384d3303cf8e84141039ef56c8a123138d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Console-Highlighter/zipball/5b4803384d3303cf8e84141039ef56c8a123138d", "reference": "5b4803384d3303cf8e84141039ef56c8a123138d", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.2", "php-parallel-lint/php-console-color": "^1.0.1"}, "replace": {"jakub-onderka/php-console-highlighter": "*"}, "require-dev": {"php-parallel-lint/php-code-style": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.0", "php-parallel-lint/php-var-dump-check": "0.*", "phpunit/phpunit": "^4.8.36 || ^5.7.21 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"PHP_Parallel_Lint\\PhpConsoleHighlighter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.acci.cz/"}], "description": "Highlight PHP code in terminal", "support": {"issues": "https://github.com/php-parallel-lint/PHP-Console-Highlighter/issues", "source": "https://github.com/php-parallel-lint/PHP-Console-Highlighter/tree/v1.0.0"}, "time": "2022-02-18T08:23:19+00:00"}, {"name": "php-parallel-lint/php-parallel-lint", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Parallel-Lint.git", "reference": "6483c9832e71973ed29cf71bd6b3f4fde438a9de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Parallel-Lint/zipball/6483c9832e71973ed29cf71bd6b3f4fde438a9de", "reference": "6483c9832e71973ed29cf71bd6b3f4fde438a9de", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.3.0"}, "replace": {"grogy/php-parallel-lint": "*", "jakub-onderka/php-parallel-lint": "*"}, "require-dev": {"nette/tester": "^1.3 || ^2.0", "php-parallel-lint/php-console-highlighter": "0.* || ^1.0", "squizlabs/php_codesniffer": "^3.6"}, "suggest": {"php-parallel-lint/php-console-highlighter": "Highlight syntax in code snippet"}, "bin": ["parallel-lint"], "type": "library", "autoload": {"classmap": ["./src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This tool check syntax of PHP files about 20x faster than serial check.", "homepage": "https://github.com/php-parallel-lint/PHP-Parallel-Lint", "support": {"issues": "https://github.com/php-parallel-lint/PHP-Parallel-Lint/issues", "source": "https://github.com/php-parallel-lint/PHP-Parallel-Lint/tree/v1.3.2"}, "time": "2022-02-21T12:50:22+00:00"}, {"name": "phpstan/extension-installer", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/phpstan/extension-installer.git", "reference": "f45734bfb9984c6c56c4486b71230355f066a58a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/extension-installer/zipball/f45734bfb9984c6c56c4486b71230355f066a58a", "reference": "f45734bfb9984c6c56c4486b71230355f066a58a", "shasum": ""}, "require": {"composer-plugin-api": "^2.0", "php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.9.0"}, "require-dev": {"composer/composer": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.2.0", "phpstan/phpstan-strict-rules": "^0.11 || ^0.12 || ^1.0"}, "type": "composer-plugin", "extra": {"class": "PHPStan\\ExtensionInstaller\\Plugin"}, "autoload": {"psr-4": {"PHPStan\\ExtensionInstaller\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Composer plugin for automatic installation of PHPStan extensions", "support": {"issues": "https://github.com/phpstan/extension-installer/issues", "source": "https://github.com/phpstan/extension-installer/tree/1.3.1"}, "time": "2023-05-24T08:59:17+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.26.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "231e3186624c03d7e7c890ec662b81e6b0405227"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/231e3186624c03d7e7c890ec662b81e6b0405227", "reference": "231e3186624c03d7e7c890ec662b81e6b0405227", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^4.15", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.26.0"}, "time": "2024-02-23T16:05:55+00:00"}, {"name": "phpstan/phpstan", "version": "1.10.63", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "ad12836d9ca227301f5fb9960979574ed8628339"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/ad12836d9ca227301f5fb9960979574ed8628339", "reference": "ad12836d9ca227301f5fb9960979574ed8628339", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpstan/phpstan", "type": "tidelift"}], "time": "2024-03-18T16:53:53+00:00"}, {"name": "phpstan/phpstan-deprecation-rules", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-deprecation-rules.git", "reference": "089d8a8258ed0aeefdc7b68b6c3d25572ebfdbaa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-deprecation-rules/zipball/089d8a8258ed0aeefdc7b68b6c3d25572ebfdbaa", "reference": "089d8a8258ed0aeefdc7b68b6c3d25572ebfdbaa", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.10.3"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-php-parser": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan rules for detecting usage of deprecated classes, methods, properties, constants and traits.", "support": {"issues": "https://github.com/phpstan/phpstan-deprecation-rules/issues", "source": "https://github.com/phpstan/phpstan-deprecation-rules/tree/1.1.4"}, "time": "2023-08-05T09:02:04+00:00"}, {"name": "phpstan/phpstan-nette", "version": "1.2.9", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-nette.git", "reference": "0e3a6805917811d685e59bb83c2286315f2f6d78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-nette/zipball/0e3a6805917811d685e59bb83c2286315f2f6d78", "reference": "0e3a6805917811d685e59bb83c2286315f2f6d78", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.10"}, "conflict": {"nette/application": "<2.3.0", "nette/component-model": "<2.3.0", "nette/di": "<2.3.0", "nette/forms": "<2.3.0", "nette/http": "<2.3.0", "nette/utils": "<2.3.0"}, "require-dev": {"nette/application": "^3.0", "nette/forms": "^3.0", "nette/utils": "^2.3.0 || ^3.0.0", "nikic/php-parser": "^4.13.2", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-php-parser": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon", "rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Nette Framework class reflection extension for PHPStan", "support": {"issues": "https://github.com/phpstan/phpstan-nette/issues", "source": "https://github.com/phpstan/phpstan-nette/tree/1.2.9"}, "time": "2023-04-12T14:11:53+00:00"}, {"name": "roave/security-advisories", "version": "dev-latest", "source": {"type": "git", "url": "https://github.com/Roave/SecurityAdvisories.git", "reference": "a93bfa3d635bfaf71fa8c459a6df6278c5cdb9e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Roave/SecurityAdvisories/zipball/a93bfa3d635bfaf71fa8c459a6df6278c5cdb9e8", "reference": "a93bfa3d635bfaf71fa8c459a6df6278c5cdb9e8", "shasum": ""}, "conflict": {"3f/pygmentize": "<1.2", "admidio/admidio": "<4.2.13", "adodb/adodb-php": "<=5.20.20|>=5.21,<=5.21.3", "aheinze/cockpit": "<2.2", "aimeos/ai-admin-graphql": ">=2022.04.1,<2022.10.10|>=2023.04.1,<2023.10.6|>=2024.04.1,<2024.04.6", "aimeos/ai-admin-jsonadm": "<2020.10.13|>=2021.04.1,<2021.10.6|>=2022.04.1,<2022.10.3|>=2023.04.1,<2023.10.4|==2024.04.1", "aimeos/ai-client-html": ">=2020.04.1,<2020.10.27|>=2021.04.1,<2021.10.22|>=2022.04.1,<2022.10.13|>=2023.04.1,<2023.10.15|>=2024.04.1,<2024.04.7", "aimeos/ai-controller-frontend": "<2020.10.15|>=2021.04.1,<2021.10.8|>=2022.04.1,<2022.10.8|>=2023.04.1,<2023.10.9", "aimeos/aimeos-core": ">=2022.04.1,<2022.10.17|>=2023.04.1,<2023.10.17|>=2024.04.1,<2024.04.7", "aimeos/aimeos-typo3": "<19.10.12|>=20,<20.10.5", "airesvsg/acf-to-rest-api": "<=3.1", "akaunting/akaunting": "<2.1.13", "akeneo/pim-community-dev": "<5.0.119|>=6,<6.0.53", "alextselegidis/easyappointments": "<1.5", "alterphp/easyadmin-extension-bundle": ">=1.2,<1.2.11|>=1.3,<1.3.1", "amazing/media2click": ">=1,<1.3.3", "amphp/artax": "<1.0.6|>=2,<2.0.6", "amphp/http": "<=1.7.2|>=2,<=2.1", "amphp/http-client": ">=4,<4.4", "anchorcms/anchor-cms": "<=0.12.7", "andreapollastri/cipi": "<=3.1.15", "andrewhaine/silverstripe-form-capture": ">=0.2,<=0.2.3|>=1,<1.0.2|>=2,<2.2.5", "apache-solr-for-typo3/solr": "<2.8.3", "apereo/phpcas": "<1.6", "api-platform/core": ">=2.2,<2.2.10|>=2.3,<2.3.6|>=2.6,<2.7.10|>=3,<3.0.12|>=3.1,<3.1.3", "appwrite/server-ce": "<=1.2.1", "arc/web": "<3", "area17/twill": "<1.2.5|>=2,<2.5.3", "artesaos/seotools": "<0.17.2", "asymmetricrypt/asymmetricrypt": "<9.9.99", "athlon1600/php-proxy": "<=5.1", "athlon1600/php-proxy-app": "<=3", "austintoddj/canvas": "<=3.4.2", "auth0/wordpress": "<=4.6", "automad/automad": "<=*******-alpha5", "automattic/jetpack": "<9.8", "awesome-support/awesome-support": "<=6.0.7", "aws/aws-sdk-php": "<3.288.1", "azuracast/azuracast": "<0.18.3", "backdrop/backdrop": "<1.24.2", "backpack/crud": "<3.4.9", "bacula-web/bacula-web": "<*******-RC2-dev", "badaso/core": "<2.7", "bagisto/bagisto": "<2.1", "barrelstrength/sprout-base-email": "<1.2.7", "barrelstrength/sprout-forms": "<3.9", "barryvdh/laravel-translation-manager": "<0.6.2", "barzahlen/barzahlen-php": "<2.0.1", "baserproject/basercms": "<5.0.9", "bassjobsen/bootstrap-3-typeahead": ">4.0.2", "bbpress/bbpress": "<2.6.5", "bcosca/fatfree": "<3.7.2", "bedita/bedita": "<4", "bigfork/silverstripe-form-capture": ">=3,<3.1.1", "billz/raspap-webgui": "<2.9.5", "bk2k/bootstrap-package": ">=7.1,<7.1.2|>=8,<8.0.8|>=9,<9.0.4|>=9.1,<9.1.3|>=10,<10.0.10|>=11,<11.0.3", "blueimp/jquery-file-upload": "==6.4.4", "bmarshall511/wordpress_zero_spam": "<5.2.13", "bolt/bolt": "<3.7.2", "bolt/core": "<=4.2", "born05/craft-twofactorauthentication": "<3.3.4", "bottelet/flarepoint": "<2.2.1", "bref/bref": "<2.1.17", "brightlocal/phpwhois": "<=4.2.5", "brotkrueml/codehighlight": "<2.7", "brotkrueml/schema": "<1.13.1|>=2,<2.5.1", "brotkrueml/typo3-matomo-integration": "<1.3.2", "buddypress/buddypress": "<7.2.1", "bugsnag/bugsnag-laravel": ">=2,<2.0.2", "bytefury/crater": "<6.0.2", "cachethq/cachet": "<2.5.1", "cakephp/cakephp": "<3.10.3|>=4,<4.0.10|>=4.1,<4.1.4|>=4.2,<4.2.12|>=4.3,<4.3.11|>=4.4,<4.4.10", "cakephp/database": ">=4.2,<4.2.12|>=4.3,<4.3.11|>=4.4,<4.4.10", "cardgate/magento2": "<2.0.33", "cardgate/woocommerce": "<=3.1.15", "cart2quote/module-quotation": ">=4.1.6,<=4.4.5|>=5,<5.4.4", "cart2quote/module-quotation-encoded": ">=4.1.6,<=4.4.5|>=5,<5.4.4", "cartalyst/sentry": "<=2.1.6", "catfan/medoo": "<1.7.5", "causal/oidc": "<2.1", "cecil/cecil": "<7.47.1", "centreon/centreon": "<22.10.15", "cesnet/simplesamlphp-module-proxystatistics": "<3.1", "chriskacerguis/codeigniter-restserver": "<=2.7.1", "civicrm/civicrm-core": ">=4.2,<4.2.9|>=4.3,<4.3.3", "ckeditor/ckeditor": "<4.24", "cockpit-hq/cockpit": "<2.7|==2.7", "codeception/codeception": "<3.1.3|>=4,<4.1.22", "codeigniter/framework": "<3.1.9", "codeigniter4/framework": "<4.4.7", "codeigniter4/shield": "<1.0.0.0-beta8", "codiad/codiad": "<=2.8.4", "composer/composer": "<1.10.27|>=2,<2.2.24|>=2.3,<2.7.7", "concrete5/concrete5": "<9.2.8", "concrete5/core": "<8.5.8|>=9,<9.1", "contao-components/mediaelement": ">=2.14.2,<2.21.1", "contao/comments-bundle": ">=2,<4.13.40|>=*******-RC1-dev,<5.3.4", "contao/contao": ">=3,<3.5.37|>=4,<4.4.56|>=4.5,<4.9.40|>=4.10,<4.11.7|>=4.13,<4.13.21|>=5.1,<5.1.4", "contao/core": "<3.5.39", "contao/core-bundle": "<4.13.40|>=5,<5.3.4", "contao/listing-bundle": ">=3,<=3.5.30|>=4,<4.4.8", "contao/managed-edition": "<=1.5", "corveda/phpsandbox": "<1.3.5", "cosenary/instagram": "<=2.3", "craftcms/cms": "<4.6.2", "croogo/croogo": "<4", "cuyz/valinor": "<0.12", "czproject/git-php": "<4.0.3", "dapphp/securimage": "<3.6.6", "darylldoyle/safe-svg": "<1.9.10", "datadog/dd-trace": ">=0.30,<0.30.2", "datatables/datatables": "<1.10.10", "david-garcia/phpwhois": "<=4.3.1", "dbrisinajumi/d2files": "<1", "dcat/laravel-admin": "<=2.1.3.0-beta", "derhansen/fe_change_pwd": "<2.0.5|>=3,<3.0.3", "derhansen/sf_event_mgt": "<4.3.1|>=5,<5.1.1|>=7,<7.4", "desperado/xml-bundle": "<=0.1.7", "devgroup/dotplant": "<2020.09.14-dev", "directmailteam/direct-mail": "<6.0.3|>=7,<7.0.3|>=8,<9.5.2", "doctrine/annotations": "<1.2.7", "doctrine/cache": ">=1,<1.3.2|>=1.4,<1.4.2", "doctrine/common": "<2.4.3|>=2.5,<2.5.1", "doctrine/dbal": ">=2,<2.0.8|>=2.1,<2.1.2|>=3,<3.1.4", "doctrine/doctrine-bundle": "<1.5.2", "doctrine/doctrine-module": "<0.7.2", "doctrine/mongodb-odm": "<1.0.2", "doctrine/mongodb-odm-bundle": "<3.0.1", "doctrine/orm": ">=1,<1.2.4|>=2,<2.4.8|>=2.5,<2.5.1|>=2.8.3,<2.8.4", "dolibarr/dolibarr": "<19.0.2", "dompdf/dompdf": "<2.0.4", "doublethreedigital/guest-entries": "<3.1.2", "drupal/core": ">=6,<6.38|>=7,<7.96|>=8,<10.1.8|>=10.2,<10.2.2", "drupal/drupal": ">=5,<5.11|>=6,<6.38|>=7,<7.80|>=8,<8.9.16|>=9,<9.1.12|>=9.2,<9.2.4", "duncanmcclean/guest-entries": "<3.1.2", "dweeves/magmi": "<=0.7.24", "ec-cube/ec-cube": "<2.4.4|>=2.11,<=2.17.1|>=3,<=3.0.18.0-patch4|>=4,<=4.1.2", "ecodev/newsletter": "<=4", "ectouch/ectouch": "<=2.7.2", "egroupware/egroupware": "<23.1.20240624", "elefant/cms": "<2.0.7", "elgg/elgg": "<3.3.24|>=4,<4.0.5", "elijaa/phpmemcacheadmin": "<=1.3", "encore/laravel-admin": "<=1.8.19", "endroid/qr-code-bundle": "<3.4.2", "enhavo/enhavo-app": "<=0.13.1", "enshrined/svg-sanitize": "<0.15", "erusev/parsedown": "<1.7.2", "ether/logs": "<3.0.4", "evolutioncms/evolution": "<=3.2.3", "exceedone/exment": "<4.4.3|>=5,<5.0.3", "exceedone/laravel-admin": "<2.2.3|==3", "ezsystems/demobundle": ">=5.4,<5.4.6.1-dev", "ezsystems/ez-support-tools": ">=2.2,<2.2.3", "ezsystems/ezdemo-ls-extension": ">=5.4,<5.4.2.1-dev", "ezsystems/ezfind-ls": ">=5.3,<5.3.6.1-dev|>=5.4,<5.4.11.1-dev|>=2017.12,<2017.12.0.1-dev", "ezsystems/ezplatform": "<=1.13.6|>=2,<=2.5.24", "ezsystems/ezplatform-admin-ui": ">=1.3,<1.3.5|>=1.4,<1.4.6|>=1.5,<1.5.29|>=2.3,<2.3.26", "ezsystems/ezplatform-admin-ui-assets": ">=4,<4.2.1|>=5,<5.0.1|>=5.1,<5.1.1", "ezsystems/ezplatform-graphql": ">=1.0.0.0-RC1-dev,<1.0.13|>=*******-beta1,<2.3.12", "ezsystems/ezplatform-kernel": "<1.2.5.1-dev|>=1.3,<1.3.35", "ezsystems/ezplatform-rest": ">=1.2,<=1.2.2|>=1.3,<1.3.8", "ezsystems/ezplatform-richtext": ">=2.3,<*******-dev", "ezsystems/ezplatform-solr-search-engine": ">=1.7,<1.7.12|>=2,<2.0.2|>=3.3,<3.3.15", "ezsystems/ezplatform-user": ">=1,<1.0.1", "ezsystems/ezpublish-kernel": "<********-dev|>=7,<7.5.31", "ezsystems/ezpublish-legacy": "<=2017.12.7.3|>=2018.6,<=2019.03.5.1", "ezsystems/platform-ui-assets-bundle": ">=4.2,<4.2.3", "ezsystems/repository-forms": ">=2.3,<*******-dev|>=2.5,<2.5.15", "ezyang/htmlpurifier": "<4.1.1", "facade/ignition": "<1.16.15|>=2,<2.4.2|>=2.5,<2.5.2", "facturascripts/facturascripts": "<=2022.08", "fastly/magento2": "<1.2.26", "feehi/cms": "<=2.1.1", "feehi/feehicms": "<=2.1.1", "fenom/fenom": "<=2.12.1", "filegator/filegator": "<7.8", "filp/whoops": "<2.1.13", "fineuploader/php-traditional-server": "<=1.2.2", "firebase/php-jwt": "<6", "fixpunkt/fp-masterquiz": "<2.2.1|>=3,<3.5.2", "fixpunkt/fp-newsletter": "<1.1.1|>=2,<2.1.2|>=2.2,<3.2.6", "flarum/core": "<1.8.5", "flarum/flarum": "<*******-beta8", "flarum/framework": "<1.8.5", "flarum/mentions": "<1.6.3", "flarum/sticky": ">=*******-beta14,<=*******-beta15", "flarum/tags": "<=*******-beta13", "floriangaerber/magnesium": "<0.3.1", "fluidtypo3/vhs": "<5.1.1", "fof/byobu": ">=*******-beta2,<1.1.7", "fof/upload": "<1.2.3", "foodcoopshop/foodcoopshop": ">=3.2,<3.6.1", "fooman/tcpdf": "<6.2.22", "forkcms/forkcms": "<5.11.1", "fossar/tcpdf-parser": "<6.2.22", "francoisjacquet/rosariosis": "<=11.5.1", "frappant/frp-form-answers": "<3.1.2|>=4,<4.0.2", "friendsofsymfony/oauth2-php": "<1.3", "friendsofsymfony/rest-bundle": ">=1.2,<1.2.2", "friendsofsymfony/user-bundle": ">=1,<1.3.5", "friendsofsymfony1/swiftmailer": ">=4,<5.4.13|>=6,<6.2.5", "friendsofsymfony1/symfony1": ">=1.1,<1.5.19", "friendsoftypo3/mediace": ">=7.6.2,<7.6.5", "friendsoftypo3/openid": ">=4.5,<4.5.31|>=4.7,<4.7.16|>=6,<6.0.11|>=6.1,<6.1.6", "froala/wysiwyg-editor": "<3.2.7|>=4.0.1,<=4.1.3", "froxlor/froxlor": "<2.1.9", "frozennode/administrator": "<=5.0.12", "fuel/core": "<1.8.1", "funadmin/funadmin": "<=3.2|>=3.3.2,<=3.3.3", "gaoming13/wechat-php-sdk": "<=1.10.2", "genix/cms": "<=1.1.11", "getformwork/formwork": "<1.13.1|==*******-beta1", "getgrav/grav": "<1.7.46", "getkirby/cms": "<4.1.1", "getkirby/kirby": "<=2.5.12", "getkirby/panel": "<2.5.14", "getkirby/starterkit": "<=3.7.0.2", "gilacms/gila": "<=1.15.4", "gleez/cms": "<=1.3|==2", "globalpayments/php-sdk": "<2", "gogentooss/samlbase": "<1.2.7", "google/protobuf": "<3.15", "gos/web-socket-bundle": "<1.10.4|>=2,<2.6.1|>=3,<3.3", "gree/jose": "<2.2.1", "gregwar/rst": "<1.0.3", "grumpydictator/firefly-iii": "<6.1.17", "gugoan/economizzer": "<=0.9.0.0-beta1", "guzzlehttp/guzzle": "<6.5.8|>=7,<7.4.5", "guzzlehttp/psr7": "<1.9.1|>=2,<2.4.5", "haffner/jh_captcha": "<=2.1.3|>=3,<=3.0.2", "harvesthq/chosen": "<1.8.7", "helloxz/imgurl": "<=2.31", "hhxsv5/laravel-s": "<3.7.36", "hillelcoren/invoice-ninja": "<5.3.35", "himiklab/yii2-jqgrid-widget": "<1.0.8", "hjue/justwriting": "<=1", "hov/jobfair": "<1.0.13|>=2,<2.0.2", "httpsoft/http-message": "<1.0.12", "hyn/multi-tenant": ">=5.6,<5.7.2", "ibexa/admin-ui": ">=4.2,<4.2.3", "ibexa/core": ">=4,<4.0.7|>=4.1,<4.1.4|>=4.2,<4.2.3|>=4.5,<4.5.6|>=4.6,<4.6.2", "ibexa/graphql": ">=2.5,<2.5.31|>=3.3,<3.3.28|>=4.2,<4.2.3", "ibexa/post-install": "<=1.0.4", "ibexa/solr": ">=4.5,<4.5.4", "ibexa/user": ">=4,<4.4.3", "icecoder/icecoder": "<=8.1", "idno/known": "<=1.3.1", "ilicmiljan/secure-props": ">=1.2,<1.2.2", "illuminate/auth": "<5.5.10", "illuminate/cookie": ">=4,<=4.0.11|>=4.1,<6.18.31|>=7,<7.22.4", "illuminate/database": "<6.20.26|>=7,<7.30.5|>=8,<8.40", "illuminate/encryption": ">=4,<=4.0.11|>=4.1,<=4.1.31|>=4.2,<=4.2.22|>=5,<=5.0.35|>=5.1,<=5.1.46|>=5.2,<=5.2.45|>=5.3,<=5.3.31|>=5.4,<=5.4.36|>=5.5,<5.5.40|>=5.6,<5.6.15", "illuminate/view": "<6.20.42|>=7,<7.30.6|>=8,<8.75", "imdbphp/imdbphp": "<=5.1.1", "impresscms/impresscms": "<=1.4.5", "impresspages/impresspages": "<=1.0.12", "in2code/femanager": "<5.5.3|>=6,<6.3.4|>=7,<7.2.3", "in2code/ipandlanguageredirect": "<5.1.2", "in2code/lux": "<17.6.1|>=18,<24.0.2", "innologi/typo3-appointments": "<2.0.6", "intelliants/subrion": "<4.2.2", "inter-mediator/inter-mediator": "==5.5", "islandora/islandora": ">=2,<2.4.1", "ivankristianto/phpwhois": "<=4.3", "jackalope/jackalope-doctrine-dbal": "<1.7.4", "james-heinrich/getid3": "<1.9.21", "james-heinrich/phpthumb": "<1.7.12", "jasig/phpcas": "<1.3.3", "jcbrand/converse.js": "<3.3.3", "johnbillion/wp-crontrol": "<1.16.2", "joomla/application": "<1.0.13", "joomla/archive": "<1.1.12|>=2,<2.0.1", "joomla/filesystem": "<1.6.2|>=2,<2.0.1", "joomla/filter": "<1.4.4|>=2,<2.0.1", "joomla/framework": "<1.5.7|>=2.5.4,<=3.8.12", "joomla/input": ">=2,<2.0.2", "joomla/joomla-cms": ">=2.5,<3.9.12", "joomla/session": "<1.3.1", "joyqi/hyper-down": "<=2.4.27", "jsdecena/laracom": "<2.0.9", "jsmitty12/phpwhois": "<5.1", "juzaweb/cms": "<=3.4", "jweiland/events2": "<8.3.8|>=9,<9.0.6", "kazist/phpwhois": "<=4.2.6", "kelvinmo/simplexrd": "<3.1.1", "kevinpapst/kimai2": "<1.16.7", "khodakhah/nodcms": "<=3", "kimai/kimai": "<2.16", "kitodo/presentation": "<3.2.3|>=3.3,<3.3.4", "klaviyo/magento2-extension": ">=1,<3", "knplabs/knp-snappy": "<=1.4.2", "kohana/core": "<3.3.3", "krayin/laravel-crm": "<1.2.2", "kreait/firebase-php": ">=3.2,<3.8.1", "kumbiaphp/kumbiapp": "<=1.1.1", "la-haute-societe/tcpdf": "<6.2.22", "laminas/laminas-diactoros": "<2.18.1|==2.19|==2.20|==2.21|==2.22|==2.23|>=2.24,<2.24.2|>=2.25,<2.25.2", "laminas/laminas-form": "<2.17.1|>=3,<3.0.2|>=3.1,<3.1.1", "laminas/laminas-http": "<2.14.2", "laravel/fortify": "<1.11.1", "laravel/framework": "<6.20.44|>=7,<7.30.6|>=8,<8.75", "laravel/laravel": ">=5.4,<5.4.22", "laravel/socialite": ">=1,<2.0.10", "latte/latte": "<2.10.8", "lavalite/cms": "<=9|==10.1", "lcobucci/jwt": ">=3.4,<3.4.6|>=4,<4.0.4|>=4.1,<4.1.5", "league/commonmark": "<0.18.3", "league/flysystem": "<1.1.4|>=2,<2.1.1", "league/oauth2-server": ">=8.3.2,<8.4.2|>=8.5,<8.5.3", "lexik/jwt-authentication-bundle": "<2.10.7|>=2.11,<2.11.3", "libreform/libreform": ">=2,<=2.0.8", "librenms/librenms": "<2017.08.18", "liftkit/database": "<2.13.2", "lightsaml/lightsaml": "<1.3.5", "limesurvey/limesurvey": "<3.27.19", "livehelperchat/livehelperchat": "<=3.91", "livewire/livewire": ">2.2.4,<2.2.6|>=3.3.5,<3.4.9", "lms/routes": "<2.1.1", "localizationteam/l10nmgr": "<7.4|>=8,<8.7|>=9,<9.2", "luyadev/yii-helpers": "<1.2.1", "magento/community-edition": "<2.4.5|==2.4.5|>=2.4.5.0-patch1,<2.4.5.0-patch8|==2.4.6|>=2.4.6.0-patch1,<2.4.6.0-patch6|==2.4.7", "magento/core": "<=1.9.4.5", "magento/magento1ce": "<1.9.4.3-dev", "magento/magento1ee": ">=1,<1.14.4.3-dev", "magento/product-community-edition": "<2.4.4.0-patch9|>=2.4.5,<2.4.5.0-patch8|>=2.4.6,<2.4.6.0-patch6|>=2.4.7,<2.4.7.0-patch1", "magneto/core": "<1.9.4.4-dev", "maikuolan/phpmussel": ">=1,<1.6", "mainwp/mainwp": "<=4.4.3.3", "mantisbt/mantisbt": "<2.26.2", "marcwillmann/turn": "<0.3.3", "matyhtf/framework": "<3.0.6", "mautic/core": "<4.4.12|>=*******-alpha,<5.0.4", "mdanter/ecc": "<2", "mediawiki/core": "<1.36.2", "mediawiki/matomo": "<2.4.3", "mediawiki/semantic-media-wiki": "<4.0.2", "melisplatform/melis-asset-manager": "<5.0.1", "melisplatform/melis-cms": "<5.0.1", "melisplatform/melis-front": "<5.0.1", "mezzio/mezzio-swoole": "<3.7|>=4,<4.3", "mgallegos/laravel-jqgrid": "<=1.3", "microsoft/microsoft-graph": ">=1.16,<1.109.1|>=2,<2.0.1", "microsoft/microsoft-graph-beta": "<2.0.1", "microsoft/microsoft-graph-core": "<2.0.2", "microweber/microweber": "<=2.0.4", "mikehaertl/php-shellcommand": "<1.6.1", "miniorange/miniorange-saml": "<1.4.3", "mittwald/typo3_forum": "<1.2.1", "mobiledetect/mobiledetectlib": "<2.8.32", "modx/revolution": "<=2.8.3.0-patch", "mojo42/jirafeau": "<4.4", "mongodb/mongodb": ">=1,<1.9.2", "monolog/monolog": ">=1.8,<1.12", "moodle/moodle": "<4.3.5|>=*******-beta,<4.4.1", "mos/cimage": "<0.7.19", "movim/moxl": ">=0.8,<=0.10", "movingbytes/social-network": "<=1.2.1", "mpdf/mpdf": "<=7.1.7", "munkireport/comment": "<4.1", "munkireport/managedinstalls": "<2.6", "munkireport/munki_facts": "<1.5", "munkireport/munkireport": ">=2.5.3,<5.6.3", "munkireport/reportdata": "<3.5", "munkireport/softwareupdate": "<1.6", "mustache/mustache": ">=2,<2.14.1", "namshi/jose": "<2.2", "neoan3-apps/template": "<1.1.1", "neorazorx/facturascripts": "<2022.04", "neos/flow": ">=1,<1.0.4|>=1.1,<1.1.1|>=2,<2.0.1|>=2.3,<2.3.16|>=3,<3.0.12|>=3.1,<3.1.10|>=3.2,<3.2.13|>=3.3,<3.3.13|>=4,<4.0.6", "neos/form": ">=1.2,<4.3.3|>=5,<5.0.9|>=5.1,<5.1.3", "neos/media-browser": "<7.3.19|>=8,<8.0.16|>=8.1,<8.1.11|>=8.2,<8.2.11|>=8.3,<8.3.9", "neos/neos": ">=1.1,<1.1.3|>=1.2,<1.2.13|>=2,<2.0.4|>=2.3,<3.0.20|>=3.1,<3.1.18|>=3.2,<3.2.14|>=3.3,<5.3.10|>=7,<7.0.9|>=7.1,<7.1.7|>=7.2,<7.2.6|>=7.3,<7.3.4|>=8,<8.0.2", "neos/swiftmailer": "<5.4.5", "netgen/tagsbundle": ">=3.4,<3.4.11|>=4,<4.0.15", "nette/application": ">=2,<2.0.19|>=2.1,<2.1.13|>=2.2,<2.2.10|>=2.3,<2.3.14|>=2.4,<2.4.16|>=3,<3.0.6", "nette/nette": ">=2,<2.0.19|>=2.1,<2.1.13", "nilsteampassnet/teampass": "<3.0.10", "nonfiction/nterchange": "<4.1.1", "notrinos/notrinos-erp": "<=0.7", "noumo/easyii": "<=0.9", "novaksolutions/infusionsoft-php-sdk": "<1", "nukeviet/nukeviet": "<4.5.02", "nyholm/psr7": "<1.6.1", "nystudio107/craft-seomatic": "<3.4.12", "nzedb/nzedb": "<0.8", "nzo/url-encryptor-bundle": ">=4,<4.3.2|>=5,<5.0.1", "october/backend": "<1.1.2", "october/cms": "<1.0.469|==1.0.469|==1.0.471|==1.1.1", "october/october": "<=3.4.4", "october/rain": "<1.0.472|>=1.1,<1.1.2", "october/system": "<1.0.476|>=1.1,<1.1.12|>=2,<2.2.34|>=3,<3.5.15", "omeka/omeka-s": "<4.0.3", "onelogin/php-saml": "<2.10.4", "oneup/uploader-bundle": ">=1,<1.9.3|>=2,<2.1.5", "open-web-analytics/open-web-analytics": "<1.7.4", "opencart/opencart": ">=0", "openid/php-openid": "<2.3", "openmage/magento-lts": "<20.5", "opensolutions/vimbadmin": "<=3.0.15", "opensource-workshop/connect-cms": "<1.7.2|>=2,<2.3.2", "orchid/platform": ">=9,<9.4.4|>=14.0.0.0-alpha4,<14.5", "oro/calendar-bundle": ">=4.2,<=4.2.6|>=5,<=5.0.6|>=5.1,<5.1.1", "oro/commerce": ">=4.1,<5.0.11|>=5.1,<5.1.1", "oro/crm": ">=1.7,<1.7.4|>=3.1,<4.1.17|>=4.2,<4.2.7", "oro/crm-call-bundle": ">=4.2,<=4.2.5|>=5,<5.0.4|>=5.1,<5.1.1", "oro/customer-portal": ">=4.1,<=4.1.13|>=4.2,<=4.2.10|>=5,<=5.0.11|>=5.1,<=5.1.3", "oro/platform": ">=1.7,<1.7.4|>=3.1,<3.1.29|>=4.1,<4.1.17|>=4.2,<=4.2.10|>=5,<=5.0.12|>=5.1,<=5.1.3", "oxid-esales/oxideshop-ce": "<4.5", "oxid-esales/paymorrow-module": ">=1,<1.0.2|>=2,<2.0.1", "packbackbooks/lti-1-3-php-library": "<5", "padraic/humbug_get_contents": "<1.1.2", "pagarme/pagarme-php": "<3", "pagekit/pagekit": "<=1.0.18", "paragonie/ecc": "<2.0.1", "paragonie/random_compat": "<2", "passbolt/passbolt_api": "<4.6.2", "paypal/adaptivepayments-sdk-php": "<=3.9.2", "paypal/invoice-sdk-php": "<=3.9", "paypal/merchant-sdk-php": "<3.12", "paypal/permissions-sdk-php": "<=3.9.1", "pear/archive_tar": "<1.4.14", "pear/auth": "<1.2.4", "pear/crypt_gpg": "<1.6.7", "pear/pear": "<=1.10.1", "pegasus/google-for-jobs": "<1.5.1|>=2,<2.1.1", "personnummer/personnummer": "<3.0.2", "phanan/koel": "<5.1.4", "phenx/php-svg-lib": "<0.5.2", "php-censor/php-censor": "<2.0.13|>=2.1,<2.1.5", "php-mod/curl": "<2.3.2", "phpbb/phpbb": "<3.2.10|>=3.3,<3.3.1", "phpems/phpems": ">=6,<=6.1.3", "phpfastcache/phpfastcache": "<6.1.5|>=7,<7.1.2|>=8,<8.0.7", "phpmailer/phpmailer": "<6.5", "phpmussel/phpmussel": ">=1,<1.6", "phpmyadmin/phpmyadmin": "<5.2.1", "phpmyfaq/phpmyfaq": "<3.2.5|==3.2.5", "phpoffice/common": "<0.2.9", "phpoffice/phpexcel": "<1.8", "phpoffice/phpspreadsheet": "<1.16", "phpseclib/phpseclib": "<2.0.47|>=3,<3.0.36", "phpservermon/phpservermon": "<3.6", "phpsysinfo/phpsysinfo": "<3.4.3", "phpunit/phpunit": ">=4.8.19,<4.8.28|>=5.0.10,<5.6.3", "phpwhois/phpwhois": "<=4.2.5", "phpxmlrpc/extras": "<0.6.1", "phpxmlrpc/phpxmlrpc": "<4.9.2", "pi/pi": "<=2.5", "pimcore/admin-ui-classic-bundle": "<=1.4.2", "pimcore/customer-management-framework-bundle": "<4.0.6", "pimcore/data-hub": "<1.2.4", "pimcore/demo": "<10.3", "pimcore/ecommerce-framework-bundle": "<1.0.10", "pimcore/perspective-editor": "<1.5.1", "pimcore/pimcore": "<11.2.4", "pixelfed/pixelfed": "<0.11.11", "plotly/plotly.js": "<2.25.2", "pocketmine/bedrock-protocol": "<8.0.2", "pocketmine/pocketmine-mp": "<5.11.2", "pocketmine/raklib": ">=0.14,<0.14.6|>=0.15,<0.15.1", "pressbooks/pressbooks": "<5.18", "prestashop/autoupgrade": ">=4,<4.10.1", "prestashop/blockreassurance": "<=5.1.3", "prestashop/blockwishlist": ">=2,<2.1.1", "prestashop/contactform": ">=1.0.1,<4.3", "prestashop/gamification": "<2.3.2", "prestashop/prestashop": "<8.1.6", "prestashop/productcomments": "<5.0.2", "prestashop/ps_emailsubscription": "<2.6.1", "prestashop/ps_facetedsearch": "<3.4.1", "prestashop/ps_linklist": "<3.1", "privatebin/privatebin": "<1.4|>=1.5,<1.7.4", "processwire/processwire": "<=3.0.229", "propel/propel": ">=*******-alpha1,<=*******-alpha7", "propel/propel1": ">=1,<=1.7.1", "pterodactyl/panel": "<1.11.6", "ptheofan/yii2-statemachine": ">=*******-RC1-dev,<=2", "ptrofimov/beanstalk_console": "<1.7.14", "pubnub/pubnub": "<6.1", "pusher/pusher-php-server": "<2.2.1", "pwweb/laravel-core": "<=*******-beta", "pyrocms/pyrocms": "<=3.9.1", "qcubed/qcubed": "<=3.1.1", "quickapps/cms": "<=*******-beta2", "rainlab/blog-plugin": "<1.4.1", "rainlab/debugbar-plugin": "<3.1", "rainlab/user-plugin": "<=1.4.5", "rankmath/seo-by-rank-math": "<=1.0.95", "rap2hpoutre/laravel-log-viewer": "<0.13", "react/http": ">=0.7,<1.9", "really-simple-plugins/complianz-gdpr": "<6.4.2", "redaxo/source": "<=5.15.1", "remdex/livehelperchat": "<4.29", "reportico-web/reportico": "<=8.1", "rhukster/dom-sanitizer": "<1.0.7", "rmccue/requests": ">=1.6,<1.8", "robrichards/xmlseclibs": ">=1,<3.0.4", "roots/soil": "<4.1", "rudloff/alltube": "<3.0.3", "s-cart/core": "<6.9", "s-cart/s-cart": "<6.9", "sabberworm/php-css-parser": ">=1,<1.0.1|>=2,<2.0.1|>=3,<3.0.1|>=4,<4.0.1|>=5,<5.0.9|>=5.1,<5.1.3|>=5.2,<5.2.1|>=6,<6.0.2|>=7,<7.0.4|>=8,<8.0.1|>=8.1,<8.1.1|>=8.2,<8.2.1|>=8.3,<8.3.1", "sabre/dav": ">=1.6,<1.7.11|>=1.8,<1.8.9", "scheb/two-factor-bundle": "<3.26|>=4,<4.11", "sensiolabs/connect": "<4.2.3", "serluck/phpwhois": "<=4.2.6", "sfroemken/url_redirect": "<=1.2.1", "sheng/yiicms": "<=1.2", "shopware/core": "<*******-dev|>=*******-RC1-dev,<6.6.1", "shopware/platform": "<*******-dev|>=*******-RC1-dev,<6.6.1", "shopware/production": "<=*******", "shopware/shopware": "<=5.7.17", "shopware/storefront": "<=*******|>=6.5.8,<*******-dev", "shopxo/shopxo": "<=6.1", "showdoc/showdoc": "<2.10.4", "silverstripe-australia/advancedreports": ">=1,<=2", "silverstripe/admin": "<1.13.19|>=2,<2.1.8", "silverstripe/assets": ">=1,<1.11.1", "silverstripe/cms": "<4.11.3", "silverstripe/comments": ">=1.3,<3.1.1", "silverstripe/forum": "<=0.6.1|>=0.7,<=0.7.3", "silverstripe/framework": "<5.2.16", "silverstripe/graphql": ">=2,<2.0.5|>=3,<3.8.2|>=4,<4.3.7|>=5,<5.1.3", "silverstripe/hybridsessions": ">=1,<2.4.1|>=2.5,<2.5.1", "silverstripe/recipe-cms": ">=4.5,<4.5.3", "silverstripe/registry": ">=2.1,<2.1.2|>=2.2,<2.2.1", "silverstripe/reports": "<5.2.3", "silverstripe/restfulserver": ">=1,<1.0.9|>=2,<2.0.4|>=2.1,<2.1.2", "silverstripe/silverstripe-omnipay": "<2.5.2|>=3,<3.0.2|>=3.1,<3.1.4|>=3.2,<3.2.1", "silverstripe/subsites": ">=2,<2.6.1", "silverstripe/taxonomy": ">=1.3,<1.3.1|>=2,<2.0.1", "silverstripe/userforms": "<3|>=5,<5.4.2", "silverstripe/versioned-admin": ">=1,<1.11.1", "simple-updates/phpwhois": "<=1", "simplesamlphp/saml2": "<1.10.6|>=2,<2.3.8|>=3,<3.1.4|==*******-alpha12", "simplesamlphp/simplesamlphp": "<1.18.6", "simplesamlphp/simplesamlphp-module-infocard": "<1.0.1", "simplesamlphp/simplesamlphp-module-openid": "<1", "simplesamlphp/simplesamlphp-module-openidprovider": "<0.9", "simplesamlphp/xml-security": "==1.6.11", "simplito/elliptic-php": "<1.0.6", "sitegeist/fluid-components": "<3.5", "sjbr/sr-freecap": "<2.4.6|>=2.5,<2.5.3", "slim/psr7": "<1.4.1|>=1.5,<1.5.1|>=1.6,<1.6.1", "slim/slim": "<2.6", "slub/slub-events": "<3.0.3", "smarty/smarty": "<4.5.3|>=5,<5.1.1", "snipe/snipe-it": "<6.4.2", "socalnick/scn-social-auth": "<1.15.2", "socialiteproviders/steam": "<1.1", "spatie/browsershot": "<3.57.4", "spatie/image-optimizer": "<1.7.3", "spipu/html2pdf": "<5.2.8", "spoon/library": "<1.4.1", "spoonity/tcpdf": "<6.2.22", "squizlabs/php_codesniffer": ">=1,<2.8.1|>=3,<3.0.1", "ssddanbrown/bookstack": "<24.05.1", "statamic/cms": "<4.46|>=5.3,<5.6.2", "stormpath/sdk": "<9.9.99", "studio-42/elfinder": "<2.1.62", "studiomitte/friendlycaptcha": "<0.1.4", "subhh/libconnect": "<7.0.8|>=8,<8.1", "sukohi/surpass": "<1", "sulu/form-bundle": ">=2,<2.5.3", "sulu/sulu": "<1.6.44|>=2,<2.4.17|>=2.5,<2.5.13", "sumocoders/framework-user-bundle": "<1.4", "superbig/craft-audit": "<3.0.2", "swag/paypal": "<5.4.4", "swiftmailer/swiftmailer": "<6.2.5", "swiftyedit/swiftyedit": "<1.2", "sylius/admin-bundle": ">=1,<1.0.17|>=1.1,<1.1.9|>=1.2,<1.2.2", "sylius/grid": ">=1,<1.1.19|>=1.2,<1.2.18|>=1.3,<1.3.13|>=1.4,<1.4.5|>=1.5,<1.5.1", "sylius/grid-bundle": "<1.10.1", "sylius/paypal-plugin": ">=1,<1.2.4|>=1.3,<1.3.1", "sylius/resource-bundle": ">=1,<1.3.14|>=1.4,<1.4.7|>=1.5,<1.5.2|>=1.6,<1.6.4", "sylius/sylius": "<1.12.19|>=********-alpha1,<1.13.4", "symbiote/silverstripe-multivaluefield": ">=3,<3.1", "symbiote/silverstripe-queuedjobs": ">=3,<3.0.2|>=3.1,<3.1.4|>=4,<4.0.7|>=4.1,<4.1.2|>=4.2,<4.2.4|>=4.3,<4.3.3|>=4.4,<4.4.3|>=4.5,<4.5.1|>=4.6,<4.6.4", "symbiote/silverstripe-seed": "<6.0.3", "symbiote/silverstripe-versionedfiles": "<=2.0.3", "symfont/process": ">=0", "symfony/cache": ">=3.1,<3.4.35|>=4,<4.2.12|>=4.3,<4.3.8", "symfony/dependency-injection": ">=2,<2.0.17|>=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/error-handler": ">=4.4,<4.4.4|>=5,<5.0.4", "symfony/form": ">=2.3,<2.3.35|>=2.4,<2.6.12|>=2.7,<2.7.50|>=2.8,<2.8.49|>=3,<3.4.20|>=4,<4.0.15|>=4.1,<4.1.9|>=4.2,<4.2.1", "symfony/framework-bundle": ">=2,<2.3.18|>=2.4,<2.4.8|>=2.5,<2.5.2|>=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7|>=5.3.14,<5.3.15|>=5.4.3,<5.4.4|>=6.0.3,<6.0.4", "symfony/http-foundation": ">=2,<2.8.52|>=3,<3.4.35|>=4,<4.2.12|>=4.3,<4.3.8|>=4.4,<4.4.7|>=5,<5.0.7", "symfony/http-kernel": ">=2,<4.4.50|>=5,<5.4.20|>=6,<6.0.20|>=6.1,<6.1.12|>=6.2,<6.2.6", "symfony/intl": ">=2.7,<2.7.38|>=2.8,<2.8.31|>=3,<3.2.14|>=3.3,<3.3.13", "symfony/maker-bundle": ">=1.27,<1.29.2|>=1.30,<1.31.1", "symfony/mime": ">=4.3,<4.3.8", "symfony/phpunit-bridge": ">=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/polyfill": ">=1,<1.10", "symfony/polyfill-php55": ">=1,<1.10", "symfony/proxy-manager-bridge": ">=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/routing": ">=2,<2.0.19", "symfony/security": ">=2,<2.7.51|>=2.8,<3.4.49|>=4,<4.4.24|>=5,<5.2.8", "symfony/security-bundle": ">=2,<4.4.50|>=5,<5.4.20|>=6,<6.0.20|>=6.1,<6.1.12|>=6.2,<6.2.6", "symfony/security-core": ">=2.4,<2.6.13|>=2.7,<2.7.9|>=2.7.30,<2.7.32|>=2.8,<3.4.49|>=4,<4.4.24|>=5,<5.2.9", "symfony/security-csrf": ">=2.4,<2.7.48|>=2.8,<2.8.41|>=3,<3.3.17|>=3.4,<3.4.11|>=4,<4.0.11", "symfony/security-guard": ">=2.8,<3.4.48|>=4,<4.4.23|>=5,<5.2.8", "symfony/security-http": ">=2.3,<2.3.41|>=2.4,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.2.12|>=4.3,<4.3.8|>=4.4,<4.4.7|>=5,<5.0.7|>=5.1,<5.2.8|>=5.3,<5.3.2|>=5.4,<5.4.31|>=6,<6.3.8", "symfony/serializer": ">=2,<2.0.11|>=4.1,<4.4.35|>=5,<5.3.12", "symfony/symfony": ">=2,<4.4.51|>=5,<5.4.31|>=6,<6.3.8", "symfony/translation": ">=2,<2.0.17", "symfony/twig-bridge": ">=2,<4.4.51|>=5,<5.4.31|>=6,<6.3.8", "symfony/ux-autocomplete": "<2.11.2", "symfony/validator": ">=2,<2.0.24|>=2.1,<2.1.12|>=2.2,<2.2.5|>=2.3,<2.3.3", "symfony/var-exporter": ">=4.2,<4.2.12|>=4.3,<4.3.8", "symfony/web-profiler-bundle": ">=2,<2.3.19|>=2.4,<2.4.9|>=2.5,<2.5.4", "symfony/webhook": ">=6.3,<6.3.8", "symfony/yaml": ">=2,<2.0.22|>=2.1,<2.1.7|>=2.2.0.0-beta1,<2.2.0.0-beta2", "symphonycms/symphony-2": "<2.6.4", "t3/dce": "<0.11.5|>=2.2,<2.6.2", "t3g/svg-sanitizer": "<1.0.3", "t3s/content-consent": "<1.0.3|>=2,<2.0.2", "tastyigniter/tastyigniter": "<3.3", "tcg/voyager": "<=1.4", "tecnickcom/tcpdf": "<=6.7.4", "terminal42/contao-tablelookupwizard": "<3.3.5", "thelia/backoffice-default-template": ">=2.1,<2.1.2", "thelia/thelia": ">=2.1,<2.1.3", "theonedemon/phpwhois": "<=4.2.5", "thinkcmf/thinkcmf": "<6.0.8", "thorsten/phpmyfaq": "<3.2.2", "tikiwiki/tiki-manager": "<=17.1", "timber/timber": ">=0.16.6,<1.23.1|>=1.24,<1.24.1|>=2,<2.1", "tinymce/tinymce": "<7.2", "tinymighty/wiki-seo": "<1.2.2", "titon/framework": "<9.9.99", "tobiasbg/tablepress": "<=*******-RC1", "topthink/framework": "<6.0.17|>=6.1,<6.1.5|>=8,<8.0.4", "topthink/think": "<=6.1.1", "topthink/thinkphp": "<=3.2.3", "torrentpier/torrentpier": "<=2.4.3", "tpwd/ke_search": "<4.0.3|>=4.1,<4.6.6|>=5,<5.0.2", "tribalsystems/zenario": "<9.5.60602", "truckersmp/phpwhois": "<=4.3.1", "ttskch/pagination-service-provider": "<1", "twig/twig": "<1.44.7|>=2,<2.15.3|>=3,<3.4.3", "typo3/cms": "<9.5.29|>=10,<10.4.35|>=11,<11.5.23|>=12,<12.2", "typo3/cms-backend": "<4.1.14|>=4.2,<4.2.15|>=4.3,<4.3.7|>=4.4,<4.4.4|>=7,<=7.6.50|>=8,<=8.7.39|>=9,<=9.5.24|>=10,<=10.4.13|>=11,<=11.1", "typo3/cms-core": "<=8.7.56|>=9,<=9.5.47|>=10,<=10.4.44|>=11,<=11.5.36|>=12,<=12.4.14|>=13,<=13.1", "typo3/cms-extbase": "<6.2.24|>=7,<7.6.8|==8.1.1", "typo3/cms-fluid": "<4.3.4|>=4.4,<4.4.1", "typo3/cms-form": ">=8,<=8.7.39|>=9,<=9.5.24|>=10,<=10.4.13|>=11,<=11.1", "typo3/cms-frontend": "<4.3.9|>=4.4,<4.4.5", "typo3/cms-install": "<4.1.14|>=4.2,<4.2.16|>=4.3,<4.3.9|>=4.4,<4.4.5|>=12.2,<12.4.8", "typo3/cms-rte-ckeditor": ">=9.5,<9.5.42|>=10,<10.4.39|>=11,<11.5.30", "typo3/flow": ">=1,<1.0.4|>=1.1,<1.1.1|>=2,<2.0.1|>=2.3,<2.3.16|>=3,<3.0.12|>=3.1,<3.1.10|>=3.2,<3.2.13|>=3.3,<3.3.13|>=4,<4.0.6", "typo3/html-sanitizer": ">=1,<=1.5.2|>=2,<=2.1.3", "typo3/neos": ">=1.1,<1.1.3|>=1.2,<1.2.13|>=2,<2.0.4|>=2.3,<2.3.99|>=3,<3.0.20|>=3.1,<3.1.18|>=3.2,<3.2.14|>=3.3,<3.3.23|>=4,<4.0.17|>=4.1,<4.1.16|>=4.2,<4.2.12|>=4.3,<4.3.3", "typo3/phar-stream-wrapper": ">=1,<2.1.1|>=3,<3.1.1", "typo3/swiftmailer": ">=4.1,<4.1.99|>=5.4,<5.4.5", "typo3fluid/fluid": ">=2,<2.0.8|>=2.1,<2.1.7|>=2.2,<2.2.4|>=2.3,<2.3.7|>=2.4,<2.4.4|>=2.5,<2.5.11|>=2.6,<2.6.10", "ua-parser/uap-php": "<3.8", "uasoft-indonesia/badaso": "<=2.9.7", "unisharp/laravel-filemanager": "<2.6.4", "userfrosting/userfrosting": ">=0.3.1,<4.6.3", "usmanhalalit/pixie": "<1.0.3|>=2,<2.0.2", "uvdesk/community-skeleton": "<=1.1.1", "uvdesk/core-framework": "<=1.1.1", "vanilla/safecurl": "<0.9.2", "verbb/comments": "<1.5.5", "verbb/formie": "<2.1.6", "verbb/image-resizer": "<2.0.9", "verbb/knock-knock": "<1.2.8", "verot/class.upload.php": "<=2.1.6", "villagedefrance/opencart-overclocked": "<=1.11.1", "vova07/yii2-fileapi-widget": "<0.1.9", "vrana/adminer": "<4.8.1", "vufind/vufind": ">=2,<9.1.1", "waldhacker/hcaptcha": "<2.1.2", "wallabag/tcpdf": "<6.2.22", "wallabag/wallabag": "<2.6.7", "wanglelecc/laracms": "<=1.0.3", "web-auth/webauthn-framework": ">=3.3,<3.3.4|>=4.5,<4.9", "web-auth/webauthn-lib": ">=4.5,<4.9", "web-feet/coastercms": "==5.5", "webbuilders-group/silverstripe-kapost-bridge": "<0.4", "webcoast/deferred-image-processing": "<1.0.2", "webklex/laravel-imap": "<5.3", "webklex/php-imap": "<5.3", "webpa/webpa": "<3.1.2", "wikibase/wikibase": "<=1.39.3", "wikimedia/parsoid": "<0.12.2", "willdurand/js-translation-bundle": "<2.1.1", "winter/wn-backend-module": "<1.2.4", "winter/wn-dusk-plugin": "<2.1", "winter/wn-system-module": "<1.2.4", "wintercms/winter": "<=1.2.3", "woocommerce/woocommerce": "<6.6|>=8.8,<8.8.5|>=8.9,<8.9.3", "wp-cli/wp-cli": ">=0.12,<2.5", "wp-graphql/wp-graphql": "<=1.14.5", "wp-premium/gravityforms": "<2.4.21", "wpanel/wpanel4-cms": "<=4.3.1", "wpcloud/wp-stateless": "<3.2", "wpglobus/wpglobus": "<=1.9.6", "wwbn/avideo": "<14.3", "xataface/xataface": "<3", "xpressengine/xpressengine": "<3.0.15", "yab/quarx": "<2.4.5", "yeswiki/yeswiki": "<4.1", "yetiforce/yetiforce-crm": "<=6.4", "yidashi/yii2cmf": "<=2", "yii2mod/yii2-cms": "<1.9.2", "yiisoft/yii": "<1.1.29", "yiisoft/yii2": "<********-dev", "yiisoft/yii2-authclient": "<2.2.15", "yiisoft/yii2-bootstrap": "<2.0.4", "yiisoft/yii2-dev": "<2.0.43", "yiisoft/yii2-elasticsearch": "<2.0.5", "yiisoft/yii2-gii": "<=2.2.4", "yiisoft/yii2-jui": "<2.0.4", "yiisoft/yii2-redis": "<2.0.8", "yikesinc/yikes-inc-easy-mailchimp-extender": "<6.8.6", "yoast-seo-for-typo3/yoast_seo": "<7.2.3", "yourls/yourls": "<=1.8.2", "yuan1994/tpadmin": "<=1.3.12", "zencart/zencart": "<=1.5.7.0-beta", "zendesk/zendesk_api_client_php": "<2.2.11", "zendframework/zend-cache": ">=2.4,<2.4.8|>=2.5,<2.5.3", "zendframework/zend-captcha": ">=2,<2.4.9|>=2.5,<2.5.2", "zendframework/zend-crypt": ">=2,<2.4.9|>=2.5,<2.5.2", "zendframework/zend-db": "<2.2.10|>=2.3,<2.3.5", "zendframework/zend-developer-tools": ">=1.2.2,<1.2.3", "zendframework/zend-diactoros": "<1.8.4", "zendframework/zend-feed": "<2.10.3", "zendframework/zend-form": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-http": "<2.8.1", "zendframework/zend-json": ">=2.1,<2.1.6|>=2.2,<2.2.6", "zendframework/zend-ldap": ">=2,<2.0.99|>=2.1,<2.1.99|>=2.2,<2.2.8|>=2.3,<2.3.3", "zendframework/zend-mail": "<2.4.11|>=2.5,<2.7.2", "zendframework/zend-navigation": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-session": ">=2,<2.2.9|>=2.3,<2.3.4", "zendframework/zend-validator": ">=2.3,<2.3.6", "zendframework/zend-view": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-xmlrpc": ">=2.1,<2.1.6|>=2.2,<2.2.6", "zendframework/zendframework": "<=3", "zendframework/zendframework1": "<1.12.20", "zendframework/zendopenid": "<2.0.2", "zendframework/zendrest": "<2.0.2", "zendframework/zendservice-amazon": "<2.0.3", "zendframework/zendservice-api": "<1", "zendframework/zendservice-audioscrobbler": "<2.0.2", "zendframework/zendservice-nirvanix": "<2.0.2", "zendframework/zendservice-slideshare": "<2.0.2", "zendframework/zendservice-technorati": "<2.0.2", "zendframework/zendservice-windowsazure": "<2.0.2", "zendframework/zendxml": ">=1,<1.0.1", "zenstruck/collection": "<0.2.1", "zetacomponents/mail": "<1.8.2", "zf-commons/zfc-user": "<1.2.2", "zfcampus/zf-apigility-doctrine": ">=1,<1.0.3", "zfr/zfr-oauth2-server-module": "<0.1.2", "zoujingli/thinkadmin": "<=6.1.53"}, "default-branch": true, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "maintainer"}], "description": "Prevents installation of composer packages with known security vulnerabilities: no API, simply require it", "keywords": ["dev"], "support": {"issues": "https://github.com/Roave/SecurityAdvisories/issues", "source": "https://github.com/Roave/SecurityAdvisories/tree/latest"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/roave/security-advisories", "type": "tidelift"}], "time": "2024-03-18T21:04:52+00:00"}, {"name": "slevomat/coding-standard", "version": "7.2.1", "source": {"type": "git", "url": "https://github.com/slevomat/coding-standard.git", "reference": "aff06ae7a84e4534bf6f821dc982a93a5d477c90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slevomat/coding-standard/zipball/aff06ae7a84e4534bf6f821dc982a93a5d477c90", "reference": "aff06ae7a84e4534bf6f821dc982a93a5d477c90", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.6.2 || ^0.7", "php": "^7.2 || ^8.0", "phpstan/phpdoc-parser": "^1.5.1", "squizlabs/php_codesniffer": "^3.6.2"}, "require-dev": {"phing/phing": "2.17.3", "php-parallel-lint/php-parallel-lint": "1.3.2", "phpstan/phpstan": "1.4.10|1.7.1", "phpstan/phpstan-deprecation-rules": "1.0.0", "phpstan/phpstan-phpunit": "1.0.0|1.1.1", "phpstan/phpstan-strict-rules": "1.2.3", "phpunit/phpunit": "7.5.20|8.5.21|9.5.20"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"SlevomatCodingStandard\\": "SlevomatCodingStandard"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Slevomat Coding Standard for PHP_CodeSniffer complements Consistence Coding Standard by providing sniffs with additional checks.", "support": {"issues": "https://github.com/slevomat/coding-standard/issues", "source": "https://github.com/slevomat/coding-standard/tree/7.2.1"}, "funding": [{"url": "https://github.com/kukulich", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/slevomat/coding-standard", "type": "tidelift"}], "time": "2022-05-25T10:58:12+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "d63cee4890a8afaf86a22e51ad4d97c91dd4579b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/d63cee4890a8afaf86a22e51ad4d97c91dd4579b", "reference": "d63cee4890a8afaf86a22e51ad4d97c91dd4579b", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-02-16T15:06:51+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"deployer/deployer": 5, "roave/security-advisories": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^8.3", "ext-fileinfo": "*", "ext-gd": "*", "ext-json": "*", "ext-zip": "*"}, "platform-dev": [], "platform-overrides": {"php": "8.3.7"}, "plugin-api-version": "2.6.0"}