
# ********** STAGE *******************

# Regular import articles + events from VUT API

11 * * * * /usr/bin/php8.3 /chroot/u15389/srv/app/current/bin/console erp:import:articles > /dev/null 2>&1
22 * * * * /usr/bin/php8.3 /chroot/u15389/srv/app/current/bin/console erp:process:articles > /dev/null 2>&1

17 * * * * /usr/bin/php8.3 /chroot/u15389/srv/app/current/bin/console erp:import:events > /dev/null 2>&1
27 * * * * /usr/bin/php8.3 /chroot/u15389/srv/app/current/bin/console erp:process:events > /dev/null 2>&1

22 1 * * 0 /usr/bin/php8.3 /chroot/u15389/srv/app/current/bin/console elastic:index:create -pcs > /dev/null 2>&1

* */4 * * * /usr/bin/php8.3 /chroot/u15389/srv/app/current/bin/console cleanImportCache > /dev/null 2>&1

14 4 * * * /usr/bin/php8.3 /chroot/u15389/srv/app/current/bin/console sitemap > /dev/null 2>&1
