---
openapi: 3.0.0
info:
  title: Dokumenty
  version: v2
servers:
- url: https://www.vut.cz/api/
  description: Aktuální server
- url: https://www.vut.cz/api
  description: Hlavní server
- url: https://test.cis.vut.cz/api
  description: Testovací server
paths:
  /dokument/v2/detail/{dokument_id}:
    get:
      tags:
      - Dokumenty
      summary: 'Detail dokumentu: Detail daného dokumentu'
      operationId: get_e09c7b652684ea127fa5995d425f89e7
      parameters:
      - name: Accept-Language
        in: header
        description: Jazyk ví<PERSON>ja<PERSON>čn<PERSON><PERSON> polož<PERSON>. Pokud nen<PERSON>, bude uvaž<PERSON>na
          hodnota 'cs'.
        schema:
          $ref: '#/components/schemas/LangCode'
      - name: dokument_id
        in: path
        required: true
        schema:
          type: integer
          pattern: .*\d+.*
      - name: fields
        in: query
        description: <PERSON><PERSON><PERSON>, kter<PERSON> budou obsaž<PERSON>y v odpovědi serveru. Používá tečkovou
          notaci tzn. atribut 'prop' objektu 'obj' je zaps<PERSON> jako 'obj.prop'.
        required: false
        style: form
        explode: false
        schema:
          type: array
          items:
            type: string
      responses:
        200:
          description: Detail dokumentu
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DokumentDetail'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/DokumentDetail'
        429:
          description: Překročena povolená kvóta.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
        500:
          description: Interní chyba (chyba serveru).
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
        400:
          description: Chyba validace - poskytnuta nevalidní data.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationViolation'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ValidationViolation'
        401:
          description: Neautorizovaný přístup - nebyl poskytnut validní token.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
        403:
          description: Neoprávněný přístup - nedostatetčná oprávnění k provedení požadované
            akce, anebo nebyla poskytnutá povolená IP adresa.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
      security:
      - OpenIdAuth: []
  /dokument/v2/slozky/{slozka_ids}/dokumenty:
    get:
      tags:
      - Dokumenty
      summary: 'Dokumenty složek: Dokumenty daných složek'
      operationId: get_400b73faf29be8ce422a867ddbf35844
      parameters:
      - name: Accept-Language
        in: header
        description: Jazyk vícejazyčných položek. Pokud není zadáno, bude uvažována
          hodnota 'cs'.
        schema:
          $ref: '#/components/schemas/LangCode'
      - name: slozka_ids
        in: path
        description: ""
        required: true
        schema:
          type: array
          items:
            type: integer
            xml:
              name: item
          pattern: .*((?:\d|,)+).*
          xml:
            wrapped: true
      - name: autor_per_id
        in: query
        schema:
          type: integer
          default: ~
          nullable: true
      - name: tag_id[in]
        in: query
        description: ""
        style: form
        explode: false
        schema:
          type: array
          items:
            type: integer
            xml:
              name: item
          default: []
          xml:
            wrapped: true
      - name: sort_by_datum_konani
        in: query
        schema:
          type: boolean
          default: false
      - name: page
        in: query
        description: Budou vráceny hodnoty na této stránce.
        required: false
        schema:
          type: integer
          default: 1
          minimum: 1
      - name: limit
        in: query
        description: Maximální počet hodnot, které budou vráceny.
        required: false
        schema:
          type: integer
          default: 20
          minimum: 1
      - name: fields
        in: query
        description: Položky, které budou obsaženy v odpovědi serveru. Používá tečkovou
          notaci tzn. atribut 'prop' objektu 'obj' je zapsán jako 'obj.prop'.
        required: false
        style: form
        explode: false
        schema:
          type: array
          items:
            type: string
      responses:
        200:
          description: Seznam dokumentů daných složek
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DokumentIterator'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/DokumentIterator'
        429:
          description: Překročena povolená kvóta.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
        500:
          description: Interní chyba (chyba serveru).
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
        400:
          description: Chyba validace - poskytnuta nevalidní data.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationViolation'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ValidationViolation'
        401:
          description: Neautorizovaný přístup - nebyl poskytnut validní token.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
        403:
          description: Neoprávněný přístup - nedostatetčná oprávnění k provedení požadované
            akce, anebo nebyla poskytnutá povolená IP adresa.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
      security:
      - OpenIdAuth: []
components:
  schemas:
    LangCode:
      description: "* cs - CS \n* en - EN \n"
      type: string
      enum:
      - cs
      - en
    DokumentDetail:
      properties:
        prilohy:
          type: array
          items:
            xml:
              name: item
            allOf:
            - $ref: '#/components/schemas/Priloha'
          xml:
            wrapped: true
        dokument_id:
          type: integer
        stranka_id:
          type: integer
          nullable: true
        doporuceny:
          type: boolean
        typ_stavu_id:
          type: integer
          nullable: true
        slozka:
          allOf:
          - $ref: '#/components/schemas/Slozka'
        vytvoril:
          allOf:
          - $ref: '#/components/schemas/AuditInfo'
        upravil:
          allOf:
          - $ref: '#/components/schemas/AuditInfo'
        autor:
          default: ~
          nullable: true
          allOf:
          - $ref: '#/components/schemas/Autor'
        tela:
          type: array
          items:
            xml:
              name: item
            allOf:
            - $ref: '#/components/schemas/Telo'
          xml:
            wrapped: true
        stitky:
          type: array
          items:
            xml:
              name: item
            allOf:
            - $ref: '#/components/schemas/Stitek'
          xml:
            wrapped: true
      type: object
    ErrorMessage:
      properties:
        type:
          type: string
        title:
          type: string
        status:
          type: integer
        detail:
          type: string
      type: object
    ValidationViolation:
      properties:
        violations:
          type: array
          items:
            xml:
              name: item
            allOf:
            - $ref: '#/components/schemas/ViolationMessage'
          xml:
            wrapped: true
        type:
          type: string
        title:
          type: string
        status:
          type: integer
        detail:
          type: string
      type: object
    DokumentIterator:
      properties:
        page:
          type: integer
        item_count:
          type: integer
        items_per_page:
          type: integer
        data:
          type: array
          items:
            xml:
              name: item
            allOf:
            - $ref: '#/components/schemas/Dokument'
          xml:
            wrapped: true
      type: object
    Priloha:
      properties:
        dokument_priloha_id:
          type: integer
        per_id:
          type: integer
        nazev:
          type: string
          nullable: true
        lang_code:
          type: string
        nazev_souboru:
          type: string
          nullable: true
        url:
          type: string
        velikost_souboru:
          type: integer
          nullable: true
        mime_type:
          type: string
          nullable: true
        poradi:
          type: integer
          nullable: true
        verze:
          type: integer
          nullable: true
        hash_souboru:
          type: string
          nullable: true
        zobrazit_vzdy:
          type: boolean
          nullable: true
        platna_verze:
          type: boolean
        pocet_otevreni:
          type: integer
          nullable: true
        css_priloha:
          allOf:
          - $ref: '#/components/schemas/CssPriloha'
        vytvoril:
          allOf:
          - $ref: '#/components/schemas/AuditInfo'
        upravil:
          allOf:
          - $ref: '#/components/schemas/AuditInfo'
      type: object
    Slozka:
      properties:
        slozka_id:
          type: integer
        nazev:
          type: string
          nullable: true
      type: object
    AuditInfo:
      properties:
        timestamp:
          type: string
          format: date-time
        per_id:
          type: integer
        jmenovka:
          type: string
      type: object
    Autor:
      properties:
        per_id:
          type: integer
        jmenovka:
          type: string
        jmenovka_pr:
          type: string
        jmenovka_bez_titulu:
          type: string
        pozice:
          type: string
          nullable: true
        profil:
          type: string
          nullable: true
      type: object
    Telo:
      properties:
        dokument_telo_id:
          type: integer
        lang_code:
          type: string
        nadpis:
          type: string
          nullable: true
        upoutavka:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        url:
          title: URL těla dokumentu v dané jazykové verzi
          type: string
        aktivni:
          type: boolean
        datum_vystaveni:
          type: string
          format: date-time
          nullable: true
        datum_platnosti:
          type: string
          format: date-time
          nullable: true
        datum_konani:
          type: string
          format: date-time
          nullable: true
        datum_konani_do:
          type: string
          format: date-time
          nullable: true
        poradi:
          type: integer
          nullable: true
        vytvoril:
          allOf:
          - $ref: '#/components/schemas/AuditInfo'
        upravil:
          allOf:
          - $ref: '#/components/schemas/AuditInfo'
        lokalizovana_slozka:
          title: Informace o složce lokalizované podle jazyku těla dokumentu
          allOf:
          - $ref: '#/components/schemas/Slozka'
      type: object
    Stitek:
      properties:
        stitek_id:
          type: integer
        nazev:
          type: string
      type: object
    ViolationMessage:
      properties:
        path:
          type: string
        expected_type:
          type: string
        title:
          type: string
      type: object
    Dokument:
      properties:
        dokument_id:
          type: integer
        stranka_id:
          type: integer
          nullable: true
        doporuceny:
          type: boolean
        typ_stavu_id:
          type: integer
          nullable: true
        slozka:
          allOf:
          - $ref: '#/components/schemas/Slozka'
        vytvoril:
          allOf:
          - $ref: '#/components/schemas/AuditInfo'
        upravil:
          allOf:
          - $ref: '#/components/schemas/AuditInfo'
        autor:
          default: ~
          nullable: true
          allOf:
          - $ref: '#/components/schemas/Autor'
        tela:
          type: array
          items:
            xml:
              name: item
            allOf:
            - $ref: '#/components/schemas/Telo'
          xml:
            wrapped: true
        stitky:
          type: array
          items:
            xml:
              name: item
            allOf:
            - $ref: '#/components/schemas/Stitek'
          xml:
            wrapped: true
      type: object
    CssPriloha:
      properties:
        id:
          type: integer
        nazev:
          type: string
      type: object
  securitySchemes:
    OpenIdAuth:
      type: openIdConnect
      openIdConnectUrl: https://id.vut.cz/auth/.well-known/openid-configuration
...
