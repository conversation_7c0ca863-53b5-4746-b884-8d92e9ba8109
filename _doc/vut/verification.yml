---
openapi: 3.0.0
info:
  title: Studium - absolvent
  version: v2
servers:
- url: https://www.vut.cz/api/
  description: Aktuální server
- url: https://www.vut.cz/api
  description: Hlavní server
- url: https://test.cis.vut.cz/api
  description: Testovací server
paths:
  /studium/absolvent/v2/overeni:
    post:
      summary: 'Ověření absolventa: Ověření absolventa podle rodného čísla'
      description: |-
        ##### **Práva ovlivňující koncový bod**:
        - ##### vutapiAbsolventOvereniRC (177529): Právo k endpointu na ověření absolventa
      operationId: post_cd30c92093b22325d414c4c4298b8d1d
      parameters:
      - name: Accept-Language
        in: header
        description: Jazyk vícejazyčný<PERSON>. Pokud nen<PERSON>, bude uva<PERSON>na
          hodnota 'cs'.
        schema:
          $ref: '#/components/schemas/LangCode'
      - name: fields
        in: query
        description: <PERSON><PERSON><PERSON>, kter<PERSON> budou obsaženy v odpovědi serveru. Používá tečkovou
          notaci tzn. atribut 'prop' objektu 'obj' je zapsán jako 'obj.prop'.
        required: false
        style: form
        explode: false
        schema:
          type: array
          items:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Overeni'
          application/xml:
            schema:
              xml:
                name: data
                wrapped: true
              allOf:
              - $ref: '#/components/schemas/Overeni'
      responses:
        200:
          description: Detail absolventa
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Absolvent'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/Absolvent'
        429:
          description: Překročena povolená kvóta.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
        500:
          description: Interní chyba (chyba serveru).
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
        400:
          description: Chyba validace - poskytnuta nevalidní data.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationViolation'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ValidationViolation'
        401:
          description: Neautorizovaný přístup - nebyl poskytnut validní token.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
        403:
          description: Neoprávněný přístup - nedostatetčná oprávnění k provedení požadované
            akce, anebo nebyla poskytnutá povolená IP adresa.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
            application/xml:
              schema:
                xml:
                  name: data
                  wrapped: true
                allOf:
                - $ref: '#/components/schemas/ErrorMessage'
      security:
      - OpenIdAuth: []
components:
  schemas:
    LangCode:
      description: "* cs - CS \n* en - EN \n"
      type: string
      enum:
      - cs
      - en
    Overeni:
      properties:
        rodne_cislo:
          title: Rodné číslo absolventa bez lomítka či jiných speciálních znaků
          type: string
          default: ~
          nullable: true
        per_id:
          title: Osobní číslo (per_id) absolventa
          type: integer
          default: ~
          nullable: true
      type: object
    Absolvent:
      properties:
        jmeno:
          type: string
        prijmeni:
          type: string
        prostredni_jmeno:
          type: string
          nullable: true
        tituly_pred:
          type: string
          nullable: true
        tituly_za:
          type: string
          nullable: true
      type: object
    ErrorMessage:
      properties:
        type:
          type: string
        title:
          type: string
        status:
          type: integer
        detail:
          type: string
      type: object
    ValidationViolation:
      properties:
        violations:
          type: array
          items:
            xml:
              name: item
            allOf:
            - $ref: '#/components/schemas/ViolationMessage'
          xml:
            wrapped: true
        type:
          type: string
        title:
          type: string
        status:
          type: integer
        detail:
          type: string
      type: object
    ViolationMessage:
      properties:
        path:
          type: string
        expected_type:
          type: string
        title:
          type: string
      type: object
  securitySchemes:
    OpenIdAuth:
      type: openIdConnect
      openIdConnectUrl: https://id.vut.cz/auth/.well-known/openid-configuration
...
