const gulp = require('gulp');
const path = require('path');
const notifier = require('node-notifier');
const browserSync = require('browser-sync');

const isProduction = require('./helpers/isProduction');
const config = require('./helpers/getConfig.js');

const sass = require('./sass');
const twig = require('./twig');
const iconSvg = require('./iconSvg');
const copyJs = require('./copyJs');
const webpack = require('./webpack');
const copyImages = require('./copyImages');
const copyRoot = require('./copyRoot');

const { series, watch: watchGulp } = gulp;

const getRelativeBasePath = (url) => `${path.relative(config.basePath.src, url).split(path.sep).join('/')}/`;

module.exports = function watch(done) {
	return series(
		function syncing(syncDone) {
			browserSync(config.browserSync);
			syncDone();
		},
		function watching() {
			watchGulp('**/*.scss', { cwd: path.join(__dirname, '../', config.src.styles) }, sass);
			watchGulp('**/*.twig', { cwd: path.join(__dirname, '../', config.src.templates) }, twig);
			watchGulp('**/*.svg', { cwd: path.join(__dirname, '../', config.src.icons) }, iconSvg);
			watchGulp('static/*.js', { cwd: path.join(__dirname, '../', config.src.scripts) }, copyJs);
			if (isProduction()) {
				watchGulp(['**/*', '!static/*.js'], { cwd: path.join(__dirname, '../', config.src.scripts) }, webpack);
			}

			watchGulp(['**/*', '!icons/*'], { cwd: path.join(__dirname, '../', config.src.images) }, copyImages);
			watchGulp(
				[
					'**/*',
					`!admin/*`,
					`!${getRelativeBasePath(config.src.styles)}**/*`,
					`!${getRelativeBasePath(config.src.scripts)}**/*`,
					`!${getRelativeBasePath(config.src.images)}**/*`,
					`!${getRelativeBasePath(config.src.templates)}**/*`,
				],
				{ cwd: path.join(__dirname, '../', config.basePath.src) },
				copyRoot,
			);

			notifier.notify({
				title: 'Start Project',
				message: 'Gulp is watching files.',
				sound: 'Hero',
			});
		},
	)(done);
};
