
DROP TABLE IF EXISTS `story`;
CREATE TABLE `story` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `facultyId` int(11) DEFAULT NULL,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFields<PERSON>son` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `facultyId` (`facultyId`),
  CONSTRAINT `story_ibfk_1` FOREIGN KEY (`facultyId`) REFERENCES `faculty` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `story_localization`;
CREATE TABLE `story_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `storyId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT '0',
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`storyId`) USING BTREE,
  CONSTRAINT `story_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
  CONSTRAINT `story_localization_ibfk_2` FOREIGN KEY (`storyId`) REFERENCES `story` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `story_localization_tree`;
CREATE TABLE `story_localization_tree` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `storyLocalizationId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `sort` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`Id`),
  KEY `FK_blog_localization_tree_blog_localization` (`storyLocalizationId`),
  KEY `FK_blog_localization_tree_tree` (`treeId`),
  CONSTRAINT `story_localization_tree_ibfk_1` FOREIGN KEY (`storyLocalizationId`) REFERENCES `story_localization` (`id`),
  CONSTRAINT `story_localization_tree_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `story_tag`;
CREATE TABLE `story_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `story_tag_localization`;
CREATE TABLE `story_tag_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `storyTagId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `sort` int(11) NOT NULL DEFAULT '0',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_tag_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_tag_localization_blog_tag` (`storyTagId`),
  CONSTRAINT `story_tag_localization_ibfk_1` FOREIGN KEY (`storyTagId`) REFERENCES `story_tag` (`id`),
  CONSTRAINT `story_tag_localization_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `story_x_story`;
CREATE TABLE `story_x_story` (
  `storyId` int(11) NOT NULL,
  `attachedStoryId` int(11) NOT NULL,
  PRIMARY KEY (`storyId`,`attachedStoryId`) USING BTREE,
  KEY `FK_blog_x_blog_blog` (`storyId`) USING BTREE,
  KEY `FK_blog_x_blog_blog_2` (`attachedStoryId`) USING BTREE,
  CONSTRAINT `story_x_story_ibfk_1` FOREIGN KEY (`storyId`) REFERENCES `story` (`id`),
  CONSTRAINT `story_x_story_ibfk_2` FOREIGN KEY (`attachedStoryId`) REFERENCES `story` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `story_x_story_tag`;
CREATE TABLE `story_x_story_tag` (
  `storyId` int(11) NOT NULL,
  `storyTagId` int(11) NOT NULL,
  PRIMARY KEY (`storyId`,`storyTagId`) USING BTREE,
  KEY `FK__blog` (`storyId`) USING BTREE,
  KEY `FK__blog_tag` (`storyTagId`) USING BTREE,
  CONSTRAINT `story_x_story_tag_ibfk_1` FOREIGN KEY (`storyId`) REFERENCES `story` (`id`),
  CONSTRAINT `story_x_story_tag_ibfk_2` FOREIGN KEY (`storyTagId`) REFERENCES `story_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

