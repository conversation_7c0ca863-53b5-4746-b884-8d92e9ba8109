DROP TABLE IF EXISTS `newsletter`;
CREATE TABLE `newsletter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customF<PERSON>s<PERSON>son` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

DROP TABLE IF EXISTS `newsletter_localization`;
CREATE TABLE `newsletter_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `newsletterId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT '0',
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`newsletterId`) USING BTREE,
  CONSTRAINT `newsletter_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
  CONSTRAINT `newsletter_localization_ibfk_2` FOREIGN KEY (`newsletterId`) REFERENCES `newsletter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `newsletter_localization_tree`;
CREATE TABLE `newsletter_localization_tree` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `newsletterLocalizationId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `sort` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`Id`),
  KEY `FK_blog_localization_tree_blog_localization` (`newsletterLocalizationId`),
  KEY `FK_blog_localization_tree_tree` (`treeId`),
  CONSTRAINT `newsletter_localization_tree_ibfk_1` FOREIGN KEY (`newsletterLocalizationId`) REFERENCES `newsletter_localization` (`id`),
  CONSTRAINT `newsletter_localization_tree_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
