DROP TABLE IF EXISTS `blog_x_faculty`;
CREATE TABLE `blog_x_faculty` (
	`blogId` int(11) NOT NULL,
	`facultyId` int(11) NOT NULL,
	PRIMARY KEY (`blogId`,`facultyId`) USING BTREE,
	<PERSON>EY `FK__blog` (`blogId`) USING BTREE,
	KEY `FK__faculty` (`facultyId`) USING BTREE,
	CONSTRAINT `FK_blog_x_faculty_blog` FOREIGN KEY (`blogId`) REFERENCES `blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_blog_x_faculty_faculty` FOREIGN KEY (`facultyId`) REFERENCES `faculty` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

DROP TABLE IF EXISTS `calendar_x_faculty`;
CREATE TABLE `calendar_x_faculty` (
	`calendarId` int(11) NOT NULL,
	`facultyId` int(11) NOT NULL,
	<PERSON>IMAR<PERSON>Y (`calendarId`,`facultyId`) USING BTREE,
	KEY `FK__calendar` (`calendarId`) USING BTREE,
	KEY `FK__faculty` (`facultyId`) USING BTREE,
	CONSTRAINT `FK_calendar_x_faculty_calendar` FOREIGN KEY (`calendarId`) REFERENCES `calendar` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_calendar_x_faculty_faculty` FOREIGN KEY (`facultyId`) REFERENCES `faculty` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

DROP TABLE IF EXISTS `story_x_faculty`;
CREATE TABLE `story_x_faculty` (
	`storyId` int(11) NOT NULL,
	`facultyId` int(11) NOT NULL,
	PRIMARY KEY (`storyId`,`facultyId`) USING BTREE,
	KEY `FK__story` (`storyId`) USING BTREE,
	KEY `FK__faculty` (`facultyId`) USING BTREE,
	CONSTRAINT `FK_story_x_faculty_story` FOREIGN KEY (`storyId`) REFERENCES `story` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_story_x_faculty_faculty` FOREIGN KEY (`facultyId`) REFERENCES `faculty` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
