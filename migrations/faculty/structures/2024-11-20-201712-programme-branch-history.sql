DROP TABLE IF EXISTS `programme_history`;
CREATE TABLE `programme_history` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`programmeId` int(11) NOT NULL,
	`extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL,
	`name` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL,
	`nameEn` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL,
	KEY `programme` (`programmeId`),
	CONSTRAINT `programme_history_ibfk_1` FOREIGN KEY (`programmeId`) REFERENCES `programme` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `branch_history`;
CREATE TABLE `branch_history` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`branchId` int(11) NOT NULL,
	`extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL,
	`name` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL,
	`nameEn` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL,
	KEY `branch` (`branchId`),
	CONSTRAINT `branch_history_ibfk_1` FOREIGN KEY (`branchId`) REFERENCES `branch` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
