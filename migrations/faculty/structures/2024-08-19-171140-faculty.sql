
DROP TABLE IF EXISTS `faculty`;
CREATE TABLE `faculty` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `customFields<PERSON>son` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `faculty_localization`;
CREATE TABLE `faculty_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `facultyId` int(11) NOT NULL,
  `name` varchar(250) NOT NULL,
  `public` int(11) NOT NULL,
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext,
  `customContent<PERSON>son` longtext,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  KEY `facultyId` (`facultyId`),
  CONSTRAINT `faculty_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
  CONSTRAINT `faculty_localization_ibfk_2` FOREIGN KEY (`facultyId`) REFERENCES `faculty` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


ALTER TABLE `blog`
ADD `facultyId` int(11) NULL AFTER `id`,
ADD FOREIGN KEY (`facultyId`) REFERENCES `faculty` (`id`);
