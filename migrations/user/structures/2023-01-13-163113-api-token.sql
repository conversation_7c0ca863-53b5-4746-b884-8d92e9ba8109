CREATE TABLE IF NOT EXISTS `api_token` (
	`id` int(11) NOT NULL PRIMARY KEY AUTO_INCREMENT,
	`token` varchar(255) NOT NULL COLLATE utf8mb4_bin,
	`description` varchar(255) NOT NULL COLLATE utf8mb4_bin,
	`issuerId` int(11) NOT NULL,
	`issuedAt` datetime NOT NULL,
	`expiresAt` datetime,
	`revokedAt` datetime,
	`scope` text NOT NULL COLLATE utf8mb4_bin
) ENGINE=InnoDB;

CREATE INDEX issuerId
	ON api_token (issuerId);

ALTER TABLE api_token
	ADD CONSTRAINT api_token_ibfk_1
		FOREIGN KEY (issuerId)
			REFERENCES user (id)
			ON DELETE CASCADE;
