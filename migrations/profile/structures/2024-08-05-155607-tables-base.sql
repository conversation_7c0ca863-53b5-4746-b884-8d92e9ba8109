CREATE TABLE `profile` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`customFields<PERSON>son` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
	PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

CREATE TABLE `profile_localization` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mutationId` int(11) NOT NULL,
	`profileId` int(11) NOT NULL,
	`name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
	`keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
	`isTop` int(11) NOT NULL DEFAULT '0',
	`public` int(11) NOT NULL DEFAULT '0',
	`forceNoIndex` int(11) NOT NULL DEFAULT '0',
	`hideInSearch` int(11) NOT NULL DEFAULT '0',
	`hideInSitemap` int(11) NOT NULL DEFAULT '0',
	`publicFrom` datetime DEFAULT NULL,
	`publicTo` datetime DEFAULT NULL,
	`edited` int(11) DEFAULT NULL,
	`editedTime` datetime DEFAULT NULL,
	`customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
	`customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
	`customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
	`viewsNumber` int(10) unsigned NOT NULL DEFAULT '0',
	 PRIMARY KEY (`id`) USING BTREE,
	 KEY `FK_profile_mutation` (`mutationId`) USING BTREE,
	 KEY `FK_profile_localization_profile` (`profileId`) USING BTREE,
	 CONSTRAINT `FK_profile_localization_profile` FOREIGN KEY (`profileId`) REFERENCES `profile` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	 CONSTRAINT `FK_profile_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

CREATE TABLE `profile_localization_tree` (
	`Id` int(11) NOT NULL AUTO_INCREMENT,
	`profileLocalizationId` int(11) NOT NULL,
	`treeId` int(11) NOT NULL,
	`sort` int(11) NOT NULL DEFAULT '1',
	PRIMARY KEY (`Id`),
	KEY `FK_profile_localization_tree_profile_localization` (`profileLocalizationId`),
	KEY `FK_profile_localization_tree_tree` (`treeId`),
	CONSTRAINT `FK_profile_localization_tree_profile_localization` FOREIGN KEY (`profileLocalizationId`) REFERENCES `profile_localization` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_profile_localization_tree_tree` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
