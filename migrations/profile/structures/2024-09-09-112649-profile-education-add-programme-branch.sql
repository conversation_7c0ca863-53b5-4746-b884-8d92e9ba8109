DROP TABLE IF EXISTS `programme`;
CREATE TABLE `programme` (
   `id` int(11) NOT NULL AUTO_INCREMENT,
   `facultyId` int(11) NULL,
   `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
   `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
   `customContentJson` longtext,
   KEY `faculty` (`facultyId`),
   CONSTRAINT `programme_ibfk_1` FOREIGN KEY (`facultyId`) REFERENCES `faculty` (`id`) ON UPDATE CASCADE ON DELETE SET NULL,
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `programme_localization`;
CREATE TABLE `programme_localization` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mutationId` int(11) NOT NULL,
	`programmeId` int(11) NOT NULL,
	`name` varchar(250) NOT NULL,
	`public` int(11) NOT NULL,
	`publicFrom` datetime DEFAULT NULL,
	`publicTo` datetime DEFAULT NULL,
	`edited` int(11) DEFAULT NULL,
	`editedTime` datetime DEFAULT NULL,
	`customFieldsJson` longtext,
	`customContentJson` longtext,
	PRIMARY KEY (`id`),
	KEY `mutationId` (`mutationId`),
	KEY `programmeId` (`programmeId`),
	CONSTRAINT `programme_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
	CONSTRAINT `programme_localization_ibfk_2` FOREIGN KEY (`programmeId`) REFERENCES `programme` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `branch`;
CREATE TABLE `branch` (
	 `id` int(11) NOT NULL AUTO_INCREMENT,
	 `programmeId` int(11) NULL,
	 `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
	 `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
	 `customContentJson` longtext,
	 KEY `programme` (`programmeId`),
	 CONSTRAINT `branch_ibfk_1` FOREIGN KEY (`programmeId`) REFERENCES `programme` (`id`) ON UPDATE CASCADE ON DELETE SET NULL,
	 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `branch_localization`;
CREATE TABLE `branch_localization` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mutationId` int(11) NOT NULL,
	`branchId` int(11) NOT NULL,
	`name` varchar(250) NOT NULL,
	`public` int(11) NOT NULL,
	`publicFrom` datetime DEFAULT NULL,
	`publicTo` datetime DEFAULT NULL,
	`edited` int(11) DEFAULT NULL,
	`editedTime` datetime DEFAULT NULL,
	`customFieldsJson` longtext,
	`customContentJson` longtext,
	PRIMARY KEY (`id`),
	KEY `mutationId` (`mutationId`),
	KEY `branchId` (`branchId`),
	CONSTRAINT `branch_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
	CONSTRAINT `branch_localization_ibfk_2` FOREIGN KEY (`branchId`) REFERENCES `branch` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `profile_education` ADD `programmeId` int(11) NULL;
ALTER TABLE `profile_education` ADD CONSTRAINT `FK_programme` FOREIGN KEY (`programmeId`) REFERENCES `programme` (`id`) ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE `profile_education` ADD `branchId` int(11) NULL;
ALTER TABLE `profile_education` ADD CONSTRAINT `FK_branch` FOREIGN KEY (`branchId`) REFERENCES `branch` (`id`) ON UPDATE CASCADE ON DELETE SET NULL;
