DROP TABLE IF EXISTS `profile_invite`;
CREATE TABLE `profile_invite` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`profileId` int(11) NOT NULL,
	`name` varchar(250) NOT NULL,
	`email` varchar(50) NOT NULL,
	`hash` varchar(255) NOT NULL,
	`sent` datetime DEFAULT NULL,
	`visited` datetime DEFAULT NULL,
	`registeredId` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `hash_unique` (`hash`),
	UNIQUE KEY `invite_unique` (`email`,`profileId`),
	KEY `profileId` (`profileId`),
	<PERSON><PERSON><PERSON> `registeredId` (`registeredId`),
	CONSTRAINT `FK_profile_invite_profile` FOREIGN KEY (`profileId`) REFERENCES `profile` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT `FK_profile_invite_registered` FOREI<PERSON><PERSON> KEY (`registeredId`) REFERENCES `profile` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
