CREATE TABLE `profile_education`
(
	`id`        int(11) NOT NULL AUTO_INCREMENT,
	`profileId` int(11) NOT NULL,
	`from`      datetime DEFAULT NULL,
	`to`        datetime DEFAULT NULL,
	PRIMARY KEY (`id`),
	<PERSON><PERSON><PERSON> `profileId` (`profileId`),
	CONSTRAINT `FK_profile_education` FOREIGN KEY (`profileId`) REFERENCES `profile` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
ALTER TABLE `profile_education` ADD `facultyId` int(11) NOT NULL;
ALTER TABLE `profile_education` ADD CONSTRAINT `FK_faculty` FOREIGN KEY (`facultyId`) REFERENCES `faculty` (`id`) ON UPDATE CASCADE ON DELETE RESTRICT;
