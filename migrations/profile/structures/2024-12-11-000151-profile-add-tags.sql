DROP TABLE IF EXISTS `profile_tag`;
CREATE TABLE `profile_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFields<PERSON>son` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `profile_tag_localization`;
CREATE TABLE `profile_tag_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `profileTagId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `sort` int(11) NOT NULL DEFAULT '0',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_profile_tag_mutation` (`mutationId`) USING BTREE,
  KEY `FK_profile_tag_localization_profile_tag` (`profileTagId`),
  CONSTRAINT `profile_tag_localization_ibfk_3` FOREIGN KEY (`profileTagId`) REFERENCES `profile_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `profile_tag_localization_ibfk_4` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `profile_x_author`;
CREATE TABLE `profile_x_author` (
  `profileId` int(11) NOT NULL,
  `authorId` int(11) NOT NULL,
  PRIMARY KEY (`profileId`,`authorId`) USING BTREE,
  KEY `FK_profile_x_author_profile` (`profileId`) USING BTREE,
  KEY `FK_profile_x_author_author` (`authorId`) USING BTREE,
  CONSTRAINT `profile_x_author_ibfk_1` FOREIGN KEY (`profileId`) REFERENCES `profile` (`id`),
  CONSTRAINT `profile_x_author_ibfk_2` FOREIGN KEY (`authorId`) REFERENCES `author` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `profile_x_profile`;
CREATE TABLE `profile_x_profile` (
  `profileId` int(11) NOT NULL,
  `attachedProfileId` int(11) NOT NULL,
  PRIMARY KEY (`profileId`,`attachedProfileId`) USING BTREE,
  KEY `FK_profile_x_profile_profile` (`profileId`) USING BTREE,
  KEY `FK_profile_x_profile_profile_2` (`attachedProfileId`) USING BTREE,
  CONSTRAINT `profile_x_profile_ibfk_1` FOREIGN KEY (`profileId`) REFERENCES `profile` (`id`),
  CONSTRAINT `profile_x_profile_ibfk_2` FOREIGN KEY (`attachedProfileId`) REFERENCES `profile` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `profile_x_profile_tag`;
CREATE TABLE `profile_x_profile_tag` (
  `profileId` int(11) NOT NULL,
  `profileTagId` int(11) NOT NULL,
  PRIMARY KEY (`profileId`,`profileTagId`) USING BTREE,
  KEY `FK__profile` (`profileId`) USING BTREE,
  KEY `FK__profile_tag` (`profileTagId`) USING BTREE,
  CONSTRAINT `profile_x_profile_tag_ibfk_1` FOREIGN KEY (`profileId`) REFERENCES `profile` (`id`),
  CONSTRAINT `profile_x_profile_tag_ibfk_2` FOREIGN KEY (`profileTagId`) REFERENCES `profile_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

