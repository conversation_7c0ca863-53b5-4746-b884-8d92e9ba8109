DROP TABLE IF EXISTS `profile_following`;
CREATE TABLE `profile_following` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`followerId` int(11) NOT NULL,
	`followingId` int(11) NOT NULL,
	`since` datetime DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `follower_unique` (`followerId`,`followingId`),
	<PERSON><PERSON>Y `followingId` (`followingId`),
	K<PERSON>Y `followerId` (`followerId`),
	CONSTRAINT `FK_profile_follower` FOREIGN KEY (`followerId`) REFERENCES `profile` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT `FK_profile_following` FOREIGN KEY (`followingId`) REFERENCES `profile` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
