

DROP TABLE IF EXISTS `calendar`;
CREATE TABLE `calendar` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `facultyId` int(11) DEFAULT NULL,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFields<PERSON>son` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `facultyId` (`facultyId`),
  CONSTRAINT `calendar_ibfk_1` FOREIGN KEY (`facultyId`) REFERENCES `faculty` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `calendar_localization`;
CREATE TABLE `calendar_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `calendarId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT '0',
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`calendarId`) USING BTREE,
  CONSTRAINT `calendar_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
  CONSTRAINT `calendar_localization_ibfk_2` FOREIGN KEY (`calendarId`) REFERENCES `calendar` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `calendar_localization_tree`;
CREATE TABLE `calendar_localization_tree` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `calendarLocalizationId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `sort` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`Id`),
  KEY `FK_blog_localization_tree_blog_localization` (`calendarLocalizationId`),
  KEY `FK_blog_localization_tree_tree` (`treeId`),
  CONSTRAINT `calendar_localization_tree_ibfk_1` FOREIGN KEY (`calendarLocalizationId`) REFERENCES `calendar_localization` (`id`),
  CONSTRAINT `calendar_localization_tree_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `calendar_tag`;
CREATE TABLE `calendar_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `calendar_tag_localization`;
CREATE TABLE `calendar_tag_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `calendarTagId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `sort` int(11) NOT NULL DEFAULT '0',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_tag_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_tag_localization_blog_tag` (`calendarTagId`),
  CONSTRAINT `calendar_tag_localization_ibfk_1` FOREIGN KEY (`calendarTagId`) REFERENCES `calendar_tag` (`id`),
  CONSTRAINT `calendar_tag_localization_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `calendar_x_author`;
CREATE TABLE `calendar_x_author` (
  `calendarId` int(11) NOT NULL,
  `authorId` int(11) NOT NULL,
  PRIMARY KEY (`calendarId`,`authorId`) USING BTREE,
  KEY `FK_blog_x_author_blog` (`calendarId`) USING BTREE,
  KEY `FK_blog_x_author_author` (`authorId`) USING BTREE,
  CONSTRAINT `calendar_x_author_ibfk_1` FOREIGN KEY (`calendarId`) REFERENCES `calendar` (`id`),
  CONSTRAINT `calendar_x_author_ibfk_2` FOREIGN KEY (`authorId`) REFERENCES `author` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `calendar_x_calendar`;
CREATE TABLE `calendar_x_calendar` (
  `calendarId` int(11) NOT NULL,
  `attachedCalendarId` int(11) NOT NULL,
  PRIMARY KEY (`calendarId`,`attachedCalendarId`) USING BTREE,
  KEY `FK_blog_x_blog_blog` (`calendarId`) USING BTREE,
  KEY `FK_blog_x_blog_blog_2` (`attachedCalendarId`) USING BTREE,
  CONSTRAINT `calendar_x_calendar_ibfk_1` FOREIGN KEY (`calendarId`) REFERENCES `calendar` (`id`),
  CONSTRAINT `calendar_x_calendar_ibfk_2` FOREIGN KEY (`attachedCalendarId`) REFERENCES `calendar` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `calendar_x_calendar_tag`;
CREATE TABLE `calendar_x_calendar_tag` (
  `calendarId` int(11) NOT NULL,
  `calendarTagId` int(11) NOT NULL,
  PRIMARY KEY (`calendarId`,`calendarTagId`) USING BTREE,
  KEY `FK__blog` (`calendarId`) USING BTREE,
  KEY `FK__blog_tag` (`calendarTagId`) USING BTREE,
  CONSTRAINT `calendar_x_calendar_tag_ibfk_1` FOREIGN KEY (`calendarId`) REFERENCES `calendar` (`id`),
  CONSTRAINT `calendar_x_calendar_tag_ibfk_2` FOREIGN KEY (`calendarTagId`) REFERENCES `calendar_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


