
DROP TABLE IF EXISTS `benefit`;
CREATE TABLE `benefit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `facultyId` int(11) DEFAULT NULL,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFields<PERSON>son` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `facultyId` (`facultyId`),
  CONSTRAINT `benefit_ibfk_1` FOREIGN KEY (`facultyId`) REFERENCES `faculty` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;



DROP TABLE IF EXISTS `benefit_localization`;
CREATE TABLE `benefit_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `benefitId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT '0',
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`benefitId`) USING BTREE,
  CONSTRAINT `benefit_localization_ibfk_3` FOREIGN KEY (`benefitId`) REFERENCES `benefit` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `benefit_localization_ibfk_4` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `benefit_localization_tree`;
CREATE TABLE `benefit_localization_tree` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `benefitLocalizationId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `sort` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`Id`),
  KEY `FK_blog_localization_tree_blog_localization` (`benefitLocalizationId`),
  KEY `FK_blog_localization_tree_tree` (`treeId`),
  CONSTRAINT `benefit_localization_tree_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE,
  CONSTRAINT `benefit_localization_tree_ibfk_3` FOREIGN KEY (`benefitLocalizationId`) REFERENCES `benefit_localization` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `benefit_tag`;
CREATE TABLE `benefit_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `benefit_tag_localization`;
CREATE TABLE `benefit_tag_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `benefitTagId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `sort` int(11) NOT NULL DEFAULT '0',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_tag_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_tag_localization_blog_tag` (`benefitTagId`),
  CONSTRAINT `benefit_tag_localization_ibfk_3` FOREIGN KEY (`benefitTagId`) REFERENCES `benefit_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `benefit_tag_localization_ibfk_4` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `benefit_x_author`;
CREATE TABLE `benefit_x_author` (
  `benefitId` int(11) NOT NULL,
  `authorId` int(11) NOT NULL,
  PRIMARY KEY (`benefitId`,`authorId`) USING BTREE,
  KEY `FK_blog_x_author_blog` (`benefitId`) USING BTREE,
  KEY `FK_blog_x_author_author` (`authorId`) USING BTREE,
  CONSTRAINT `benefit_x_author_ibfk_1` FOREIGN KEY (`benefitId`) REFERENCES `benefit` (`id`),
  CONSTRAINT `benefit_x_author_ibfk_2` FOREIGN KEY (`authorId`) REFERENCES `author` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `benefit_x_benefit`;
CREATE TABLE `benefit_x_benefit` (
  `benefitId` int(11) NOT NULL,
  `attachedBenefitId` int(11) NOT NULL,
  PRIMARY KEY (`benefitId`,`attachedBenefitId`) USING BTREE,
  KEY `FK_blog_x_blog_blog` (`benefitId`) USING BTREE,
  KEY `FK_blog_x_blog_blog_2` (`attachedBenefitId`) USING BTREE,
  CONSTRAINT `benefit_x_benefit_ibfk_1` FOREIGN KEY (`benefitId`) REFERENCES `benefit` (`id`),
  CONSTRAINT `benefit_x_benefit_ibfk_2` FOREIGN KEY (`attachedBenefitId`) REFERENCES `benefit` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


DROP TABLE IF EXISTS `benefit_x_benefit_tag`;
CREATE TABLE `benefit_x_benefit_tag` (
  `benefitId` int(11) NOT NULL,
  `benefitTagId` int(11) NOT NULL,
  PRIMARY KEY (`benefitId`,`benefitTagId`) USING BTREE,
  KEY `FK__blog` (`benefitId`) USING BTREE,
  KEY `FK__blog_tag` (`benefitTagId`) USING BTREE,
  CONSTRAINT `benefit_x_benefit_tag_ibfk_1` FOREIGN KEY (`benefitId`) REFERENCES `benefit` (`id`),
  CONSTRAINT `benefit_x_benefit_tag_ibfk_2` FOREIGN KEY (`benefitTagId`) REFERENCES `benefit_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

