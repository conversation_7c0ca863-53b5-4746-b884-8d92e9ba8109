SET NAMES utf8mb4;

INSERT INTO `tree` (`id`, `mutationId`, `rootId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameTitle`, `description`, `keywords`, `public`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`) VALUES
(450,	1,	1,	450,	398,	2,	'1|398|',	3,	1,	'precart',	0,	'2024-03-01 11:44:30',	'2024-03-01 11:44:00',	35,	'2024-03-01 12:03:59',	':Front:Precart:default',	'common',	'2024-03-01 11:44:00',	'2124-03-01 11:44:00',	'Predkošík',	'Predkošík',	'Predkošík',	'',	'',	1,	1,	1,	0,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL),
(451,	1,	1,	451,	398,	2,	'1|398|',	4,	1,	'cart',	0,	'2024-03-01 13:44:03',	'2024-03-01 13:44:00',	35,	'2024-03-01 13:44:29',	':Front:Order:default',	'common',	'2024-03-01 13:44:00',	'2124-03-01 13:44:00',	'Košík',	'Košík',	'Košík',	'',	'',	1,	1,	1,	0,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL);

INSERT INTO `tree` (`id`, `mutationId`, `rootId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameTitle`, `description`, `keywords`, `public`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`) VALUES
(452,	1,	1,	450,	398,	2,	'1|398|',	5,	1,	'step1',	0,	'2024-03-14 09:28:31',	'2024-03-14 09:28:00',	33,	'2024-03-14 09:29:19',	':Front:Order:step1',	'common',	'2024-03-14 09:28:00',	'2124-03-14 09:28:00',	'Objednávka 1. krok',	'Objednávka 1. krok',	'Objednávka 1. krok',	'',	'',	1,	1,	1,	0,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL),
(453,	1,	1,	451,	398,	2,	'1|398|',	6,	1,	'step2',	0,	'2024-03-14 09:29:30',	'2024-03-14 09:29:00',	33,	'2024-03-14 09:30:12',	':Front:Order:step2',	'common',	'2024-03-14 09:29:00',	'2124-03-14 09:29:00',	'Objednávka 2. krok',	'Objednávka 2. krok',	'Objednávka 2. krok',	'',	'',	1,	1,	1,	0,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL),
(454,	1,	1,	452,	398,	2,	'1|398|',	7,	1,	'step3',	0,	'2024-03-14 09:30:15',	'2024-03-14 09:30:00',	33,	'2024-03-14 09:30:48',	':Front:Order:step3',	'common',	'2024-03-14 09:30:00',	'2124-03-14 09:30:00',	'Objednávka dokončena',	'Objednávka dokončena',	'Objednávka dokončena',	'',	'',	1,	1,	1,	0,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL);

INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES
('predkosik',	'tree',	450,	1),
('kosik',	'tree',	451,	1);

INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES
('objednavka-krok-1',	'tree',	452,	1),
('objednavka-krok-2',	'tree',	453,	1),
('objednavka-dokoncena',	'tree',	454,	1);
