networks:
  vutabsolvent:
  superkoders:
    external: true

volumes:
  db:
  es:

services:
  app:
    build:
      dockerfile: docker/app/Dockerfile
      context: .
    hostname: app
    container_name: vutabsolvent_app
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=superkoders"
      - "traefik.http.routers.vutabsolvent.rule=Host(`vutabsolvent.superkoders.test`)"
      - "traefik.http.routers.vutabsolvent.tls=true"
    ports:
      - "8080:80"
    networks:
      - superkoders
      - vutabsolvent
    volumes:
      - .:/var/www/html
      - ./docker/app/php-xdebug-${SUPERADMIN_XDEBUG:-off}.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    depends_on:
      - db
      - es
      - front
      - admin

  front:
    build:
      dockerfile: docker/front/Dockerfile
      context: .
    container_name: vutabsolvent_front
    restart: unless-stopped
    networks:
      - vutabsolvent
    volumes:
      - .:/app

  admin:
    build:
      dockerfile: docker/admin/Dockerfile
      context: .
    container_name: vutabsolvent_admin
    restart: unless-stopped
    networks:
      - vutabsolvent
    volumes:
      - .:/app

  adminer:
    image: adminer
    restart: unless-stopped
    networks:
      - vutabsolvent
    ports:
      - "81:8080"

  db:
      image: mariadb:10
      hostname: vutabsolvent_db
      container_name: vutabsolvent_db
      restart: unless-stopped
      networks:
          - vutabsolvent
      ports:
          - "3306:3306"
      volumes:
          - ./docker/db:/docker/db
          - ./docker/db/compare-db.sh:/docker/db/compare-db.sh
      environment:
          MARIADB_HOST: 'vutabsolvent_db'
          MARIADB_ROOT_PASSWORD: 'root'
          MARIADB_DATABASE: 'vutabsolvent'
          MARIADB_USER: 'root'
          MARIADB_PASSWORD: 'root'

  db-init:
      image: mariadb:10
      container_name: vutabsolvent_db-init
      depends_on:
          - db
      networks:
          - vutabsolvent
      volumes:
          - ./docker/db/init-db.sh:/docker/db/init-db.sh
          - ./docker/db/dump.sql:/docker/db/dump.sql
      environment:
          MARIADB_HOST: 'vutabsolvent_db'
          MARIADB_ROOT_PASSWORD: 'root'
          MARIADB_DATABASE: 'vutabsolvent'
          MARIADB_USER: 'root'
          MARIADB_PASSWORD: 'root'
      entrypoint: ["/docker/db/init-db.sh"]

  es:
    image: elasticsearch:7.17.6
    hostname: vutabsolvent_es
    container_name: vutabsolvent_es
    restart: unless-stopped
    networks:
      - vutabsolvent
    ports:
      - "9200:9200"
    volumes:
      - es:/usr/share/elasticsearch/data
    environment:
      "discovery.type": single-node

  redis:
    image: redis:latest
    hostname: vutabsolvent_redis
    container_name: vutabsolvent_redis
    restart: unless-stopped
    networks:
      - vutabsolvent
    ports:
      - "6379:6379"

  mailcatcher:
    image: dockage/mailcatcher
    hostname: vutabsolvent_mailcatcher
    container_name: vutabsolvent_mailcatcher
    restart: unless-stopped
    networks:
      - vutabsolvent
    ports:
      - "1080:1080"
