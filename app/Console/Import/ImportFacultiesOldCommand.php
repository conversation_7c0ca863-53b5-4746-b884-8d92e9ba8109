<?php

namespace App\Console\Import;

use App\Model\Import\Helpers as ImportHelpers;
use App\Model\Orm\Orm;
use App\PostType\Branch\Model\Orm\Branch;
use App\PostType\Branch\Model\Orm\BranchHistory;
use App\PostType\Branch\Model\Orm\BranchLocalization;
use App\PostType\Faculty\Model\Orm\Faculty;
use App\PostType\Faculty\Model\Orm\FacultyLocalization;
use App\PostType\Programme\Model\Orm\Programme;
use App\PostType\Programme\Model\Orm\ProgrammeHistory;
use App\PostType\Programme\Model\Orm\ProgrammeLocalization;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use RuntimeException;

class ImportFacultiesOldCommand extends Command
{
	protected static $defaultName = 'import:faculties-old';

	public function __construct(private readonly Orm $orm)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->setDescription('Import faculties, programmes, branches from given CSV file (old faculties)')
			->addArgument('filename', InputArgument::REQUIRED)
			->addOption('logs');

	}

	private function fillLocalizations(Faculty|Programme|Branch $entity, array $mutations): void
	{
		$localizationClass = $entity->getMetadata()->getProperty('localizations')->relationship->entity;

		foreach ($mutations as $mutation) {
			$entityLocalization = $entity->localizations->toCollection()->findBy(['mutation' => $mutation->id])->fetch();
			if ($entityLocalization === null) {
				/** @var FacultyLocalization|ProgrammeLocalization|BranchLocalization $entityLocalization */
				$entityLocalization = new $localizationClass;
				$entityLocalization->mutation = $mutation;
				$entity->localizations->add($entityLocalization);
			}

			/** @var FacultyLocalization|ProgrammeLocalization|BranchLocalization $entityLocalization */
			$entityLocalization->public = true;
		}
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$filename = $input->getArgument('filename');

		if (!file_exists($filename)) {
			throw new RuntimeException(sprintf('Input file "%s" not found', $filename));
		}

		$f = fopen($filename, 'r');
		if ($f === false) {
			throw new RuntimeException(sprintf('File "%s" can not be opened', $filename));
		}

		$defaultMutation = $this->orm->mutation->getDefault();
		$this->orm->setMutation($defaultMutation);
		$this->orm->setPublicOnly(false);

		$mutations = $this->orm->mutation->findBy(['langCode' => ['cs']])->fetchAll();

		$total = 0;
		while (fgetcsv(stream: $f, separator: ';') !== false) {
			$total++;
		}
		rewind($f);

		$counter = 0;
		$header = null;
		$percentLast = null;
		while (($line = fgetcsv(stream: $f, separator: ';')) !== false) {
			if (!isset($header)) {
				$header = $line;
				continue;
			}

			$item = (object)array_map(function ($item): mixed {
				return is_numeric($item) ? $item + 0 : $item;
			}, array_combine($header, $line));

			$percent = round(($counter / $total) * 100, 2);
			if (!isset($percentLast) || $percent !== $percentLast) {
				echo $percent . "% \n";
				$percentLast = $percent;
			}

			if ($input->getOption('logs')) {
				dump($item);
			}

			$counter++;

			// -----------------------------------------------------------
			// faculty

			$facultyExtId = $item->FAKULTA_ID;

			/** @var Faculty|null $faculty */
			$faculty = $this->orm->faculty->findBy(['extId' => $facultyExtId])->fetch();
			if ($faculty === null) {
				$faculty = new Faculty();
				$faculty->extId = $facultyExtId;
				$faculty->setInternalName($item->FAKULTA_NAZEV);
				self::fillLocalizations($faculty, $mutations);

				foreach ($faculty->localizations as $facultyLocalization) {
					switch ($facultyLocalization->mutation->langCode) {
						case 'cs':
							$facultyLocalization->setName($item->FAKULTA_NAZEV);
							break;
						case 'en':
							$facultyLocalization->setName($item->FAKULTA_NAZEV_EN);
							break;
					}
				}
			}

			$this->orm->faculty->persistAndFlush($faculty);

			// -----------------------------------------------------------
			// programme

			$programme = null;
			$programmeExtId = ImportHelpers::generateProgrammeId($item->FAKULTA_ID, $item->PROGRAM_NAZEV);

			/** @var Programme|null $programme */
			$programme = $this->orm->programme->findBy(['extId' => $programmeExtId])->fetch();
			if ($programme === null) {
				$programme = new Programme();
				$programme->extId = $programmeExtId;
				$programme->setInternalName($item->PROGRAM_NAZEV);
				$programme->faculty = $faculty;
				self::fillLocalizations($programme, $mutations);

				foreach ($programme->localizations as $programmeLocalization) {
					switch ($programmeLocalization->mutation->langCode) {
						case 'cs':
							$programmeLocalization->setName($item->PROGRAM_NAZEV);
							break;
						case 'en':
							$programmeLocalization->setName($item->PROGRAM_NAZEV_EN);
							break;
					}
				}

				$this->orm->programme->persistAndFlush($programme);
			}
		}

		is_resource($f) && fclose($f);

		return self::SUCCESS;
	}
}
