<?php declare(strict_types = 1);

namespace App\PostType\StoryTag\Model;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Story\Model\Orm\StoryLocalizationRepository;
use App\PostType\Story\Model\Orm\StoryRepository;
use App\PostType\StoryTag\Model\Orm\StoryTag\StoryTagLocalization;
use App\PostType\StoryTag\Model\Orm\StoryTag\StoryTagLocalizationRepository;
use stdClass;

class StoryTagModel
{

	public function __construct(
		private StoryTagLocalizationRepository $storyTagLocalizationRepository,
		private StoryLocalizationRepository $storyLocalizationRepository,
		private StoryRepository $storyRepository,
	)
	{
	}

	public function getTagsWithCount(Mutation $mutation): array
	{
		$storyTagLocalizations = $this->storyTagLocalizationRepository->findBy([
			'mutation' => $mutation
		]);
		$tagsWithCount = [];

		foreach ($storyTagLocalizations as $storyTagLocalization) {
			assert($storyTagLocalization instanceof StoryTagLocalization);
			$tagData = new stdClass();
			$tagData->tag = $storyTagLocalization;

			$storyIds = $this->storyRepository->findBy(['tags->id' => $storyTagLocalization->storyTag->id])->fetchPairs(null, 'id');

			$storyTagLocalizations = $this->storyLocalizationRepository->findBy([
				'mutation' => $storyTagLocalization->getMutation(),
				'story' => $storyIds,
			]);

			$tagData->count = $storyTagLocalizations->count();

			$tagsWithCount[] = $tagData;
		}

		usort($tagsWithCount, fn($a, $b) => $a->count <=> $b->count);
		return $tagsWithCount;
	}

}
