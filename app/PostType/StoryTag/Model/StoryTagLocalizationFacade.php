<?php declare(strict_types = 1);

namespace App\PostType\StoryTag\Model;


use App\PostType\StoryTag\Model\Orm\StoryTag\StoryTag;
use App\PostType\StoryTag\Model\Orm\StoryTag\StoryTagLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

class StoryTagLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): LocalizationEntity
	{
		$localization = new StoryTagLocalization();
		$this->orm->storyTagLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new StoryTag();
			$localization->storyTag = $localizableEntity;
		} else {
			assert($localizableEntity instanceof StoryTag);
			$localization->storyTag = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof StoryTagLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->storyTagLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->storyTag->remove($parent);
		}

		$this->orm->flush();
	}

}
