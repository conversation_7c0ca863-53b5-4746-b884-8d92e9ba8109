<?php declare(strict_types = 1);

namespace App\PostType\BenefitTag\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\BenefitTag\Model\Orm\BenefitTag\BenefitTagLocalization;
use App\PostType\BenefitTag\Model\BenefitTagLocalizationFacade;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;

final class BenefitTagPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'benefitTag';

	private BenefitTagLocalization $benefitTagLocalization;

	public function __construct(
		private DataGridFactory $dataGridFactory,
		private FormFactory $benefitTagFormFactory,
		private ShellFormFactory $shellFormFactory,
		private BenefitTagLocalizationFacade $benefitTagLocalizationFacade,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$benefitTagLocalization = $this->orm->benefitTagLocalization->getById($id);
		if ($benefitTagLocalization === null) {
			$this->redirect('default');
		}

		$this->benefitTagLocalization = $benefitTagLocalization;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->benefitTagLocalization->findAll());
	}


	protected function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->benefitTagFormFactory->create($this->benefitTagLocalizationFacade, $this->benefitTagLocalization, $userEntity);
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity:null, entityLocalizationFacade: $this->benefitTagLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
