<?php declare(strict_types = 1);

namespace App\PostType\BenefitTag\Model;


use App\PostType\BenefitTag\Model\Orm\BenefitTag\BenefitTag;
use App\PostType\BenefitTag\Model\Orm\BenefitTag\BenefitTagLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

class BenefitTagLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): LocalizationEntity
	{
		$localization = new BenefitTagLocalization();
		$this->orm->benefitTagLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new BenefitTag();
			$localization->benefitTag = $localizableEntity;
		} else {
			assert($localizableEntity instanceof BenefitTag);
			$localization->benefitTag = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof BenefitTagLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->benefitTagLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->benefitTag->remove($parent);
		}

		$this->orm->flush();
	}

}
