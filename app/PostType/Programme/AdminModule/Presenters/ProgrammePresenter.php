<?php declare(strict_types = 1);

namespace App\PostType\Programme\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\Programme\AdminModule\Components\Form\ProgrammeFormPrescription;
use App\PostType\Programme\Model\ProgrammeLocalizationFacade;
use App\PostType\Programme\Model\Orm\Programme;
use App\PostType\Programme\Model\Orm\ProgrammeLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfo;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use Nette\Utils\ArrayHash;

final class ProgrammePresenter extends BasePresenter
{

	public const ORM_REPOSITORY_NAME = 'programme';

	private ProgrammeLocalization $programmeLocalization;

	public function __construct(
		private readonly ProgrammeFormPrescription   $programmeFormPrescription,
		private readonly DataGridFactory           $dataGridFactory,
		private readonly FormFactory               $formFactory,
		private readonly ShellFormFactory          $shellFormFactory,
		private readonly ProgrammeLocalizationFacade $programmeLocalizationFacade,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$programmeLocalization = $this->orm->programmeLocalization->getById($id);
		if ($programmeLocalization === null) {
			$this->redirect('default');
		}

		$this->programmeLocalization = $programmeLocalization;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(
			baseEntityName: self::ORM_REPOSITORY_NAME,
			collection: $this->orm->programmeLocalization->findAll()
		);
	}



	public function createComponentForm(): Form
	{
		return $this->formFactory->create(
			entityLocalizationFacade: $this->programmeLocalizationFacade,
			entityLocalization: $this->programmeLocalization,
			userEntity: $this->userEntity,
			formDefinition: $this->programmeFormPrescription->get($this->programmeLocalization)
		);
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity: null, entityLocalizationFacade: $this->programmeLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->action . '.latte');
	}

}
