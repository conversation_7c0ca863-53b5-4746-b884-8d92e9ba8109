<?php declare(strict_types=1);

namespace App\PostType\Programme\AdminModule\Components\Form;

//use App\Model\FormExtender\StateExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Faculty\Model\Orm\FacultyLocalizationRepository;
use App\PostType\Faculty\Model\Orm\FacultyRepository;
use App\PostType\Programme\Model\Orm\ProgrammeLocalization;
use App\PostType\Programme\AdminModule\Components\Form\FormData\ProgrammeFormData;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use Nette\Application\UI\Form;

readonly class ProgrammeFormPrescription
{

	public function __construct(private readonly FacultyLocalizationRepository $facultyLocalizationRepository)
	{
	}

	public function get(ProgrammeLocalization $programmeLocalization) :FormDefinition
	{
		$form = new Form();
		$form->setMappedType(ProgrammeFormData::class);

		$extenders = [];

		$extenders[] = new CustomFormExtender(
			addHandler: function (Form $form) use ($programmeLocalization) {
				$faculties = $this->facultyLocalizationRepository->findBy([
					'mutation' => $programmeLocalization->getMutation(),
				])->fetchPairs('faculty->id', 'name');
				$form->addSelect('faculty', 'faculty', $faculties)
					->setDefaultValue($programmeLocalization->getParent()->faculty?->id)
					->setPrompt('---');
			},
			successHandler: function (Form $form) use ($programmeLocalization) {
				$programmeLocalization->getParent()->faculty = $form['faculty']->getValue();
			},
			templateParts: [
				new CommonTemplatePart(__DIR__ . '/parts/faculty.latte',CommonTemplatePart::TYPE_MAIN)
			],
		);

		return new FormDefinition($form, $extenders);
	}



}
