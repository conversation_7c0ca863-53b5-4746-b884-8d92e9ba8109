{default $pager = true}
{block content}
	{snippet content}
		{*THICKBOX*}
		{if $presenter->isAjax()}
			{include $templates.'/part/box/content.latte'}
		{else}
			<div class="row-main">
				<div class="u-mb-last-0 u-pt-md u-mb-md u-mb-3xl@md">
					{include $templates.'/part/box/annot.latte'}
					{* {include $templates.'/part/crossroad/std.latte'} *}
					{* {include $templates.'/part/box/content.latte'} *}

					{snippet faculties}
						{snippetArea facultiesInner}
							{if $programmeLocalizations->count() > 6}
								{include $templates.'/part/crossroad/services.latte', crossroad=>$programmeLocalizations}
							{else}
								{include $templates.'/part/crossroad/services.latte', class=>'u-mb-3xl u-mb-0@md', crossroad=>$programmeLocalizations}
							{/if}
						{/snippetArea}
					{/snippet}

					{control customContentRenderer}
				</div>
			</div>
		{/if}
	{/snippet}
{/block}
