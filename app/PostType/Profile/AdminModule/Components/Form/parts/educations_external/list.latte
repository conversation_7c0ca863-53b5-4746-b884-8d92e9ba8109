{var $contentProps = [
	listFormCointainer: $props['listFormCointainer'],
	listPlaceholder: $props['listPlaceholder'],
	listSearchUrl: $props['listSearchUrl'],
	listName: $props['listName'],
	dragdrop: (isset($props['dragdrop'])) ? $props['dragdrop'] : false,
	showAdd: (isset($props['showAdd'])) ? $props['showAdd'] : true,
	singleValue: (isset($props['singleValue'])) ? (bool) $props['singleValue'] : false,
]}

{var $props = [
	title: $translator->translate('section_' . $props['title']),
	id: $props['id'],
	icon: isset($icon) ? $templates . '/part/icons/' . $icon . '.svg' : null,
	open: (isset($props['open'])) ? $props['open'] : false,
	variant: (isset($props['variant'])) ? $props['variant'] : 'main',
	classes: (isset($props['classes'])) ? $props['classes'] : ['u-mb-xxs'],
]}


{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		{var $items = []}

		{var $containerItems = $contentProps['listFormCointainer']->components}

		{foreach $containerItems as $key => $container}
			{continueIf $key === 'newItemMarker'}

			{php $_return = (object) []}
			{include _inputs.latte container => $container, _return => $_return}
			{php $items[] = $_return->val}
		{/foreach}


		<div class="">
			{var $listProps = [
				data: [
					controller: 'List',
					List-name-value:  $contentProps['listName'],
				],
				listData: [
					List-target: 'list',
				],
				addData: [
					action: 'List#add',
				],
				add: $contentProps['showAdd'],
				dragdrop: $contentProps['dragdrop'],
				items: $items,
			]}

			{include $templates.'/part/box/list.latte',
				props: $listProps
			}
		</div>
	{/block}
{/embed}
