{*[
	placeholder: $container['program']->getCaption(),
	input: $container['program'],
	classesLabel: ['title'],
	inpRenderer: true,
	type: 'select',
],*}

{php $_return->val = [
	data: [
		controller: 'RemoveItem SuggestInp',
		removeitem-target: 'item',
	],
	inps: [
		[
			placeholder: $container['from']->getCaption(),
			input: $container['from'],
			type: 'number',
			classesLabel: ['title'],
			inpRenderer: true,
		],
		[
			placeholder: $container['to']->getCaption(),
			input: $container['to'],
			type: 'number',
			classesLabel: ['title'],
			inpRenderer: true,
		],
		[
			placeholder: $container['name']->getCaption(),
			input: $container['name'],
			classesLabel: ['title'],
			inpRenderer: true,
			type: 'text',
		],
		[
			placeholder: $container['faculty']->getCaption(),
			input: $container['faculty'],
			classesLabel: ['title'],
			inpRenderer: true,
			type: 'text',
		],
		[
			placeholder: $container['programme']->getCaption(),
			input: $container['programme'],
			classesLabel: ['title'],
			inpRenderer: true,
			type: 'text',
		],
		[
			placeholder: $container['branch']->getCaption(),
			input: $container['branch'],
			classesLabel: ['title'],
			inpRenderer: true,
			type: 'text',
		],
	],
	btnsAfter: [
		[
			icon: $templates.'/part/icons/trash.svg',
			tooltip: 'Odstranit',
			variant: 'remove',
			data: [
				action: 'RemoveItem#remove'
			]
		]
	],
	dragdrop: false
]}
