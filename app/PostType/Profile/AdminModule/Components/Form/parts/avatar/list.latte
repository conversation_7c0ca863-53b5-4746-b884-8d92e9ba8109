{var $contentProps = [
	listFormCointainer: $props['listFormCointainer'],
	listPlaceholder: $props['listPlaceholder'],
	listSearchUrl: $props['listSearchUrl'],
	listName: $props['listName'],
	dragdrop: (isset($props['dragdrop'])) ? $props['dragdrop'] : false,
	showAdd: (isset($props['showAdd'])) ? $props['showAdd'] : true,
	singleValue: (isset($props['singleValue'])) ? (bool) $props['singleValue'] : false,
]}

{var $props = [
	title: $translator->translate('section_' . $props['title']),
	id: $props['id'],
	icon: isset($icon) ? $templates . '/part/icons/' . $icon . '.svg' : null,
	open: (isset($props['open'])) ? $props['open'] : false,
	variant: (isset($props['variant'])) ? $props['variant'] : 'main',
	classes: (isset($props['classes'])) ? $props['classes'] : ['u-mb-xxs'],
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		{var $items = []}
		{var $ids = []}

		{var $containerItems = $contentProps['listFormCointainer']->components}

		{foreach $containerItems as $key => $container}
			{continueIf $key === 'newItemMarker'}

			{var $imageId = $container['imageId']->getValue()}
			{if is_int($key)}
				{var $image = $imageRepository->getBy(['id' => $key])}
				{php $img = $imageObjectFactory->getByName($image->filename, 's')}
				{php $imgSrc = $img->src}
			{else}
				{var $imgSrc = ''}
			{/if}


			{php $_return = (object) []}
			{include _inputs.latte container => $container, image => $image, imgSrc => $imgSrc, _return => $_return}

			{php $items[] = $_return->val}
			{php $ids[] = $image->id}
		{/foreach}

		{include $templates.'/part/box/imgs.latte',
			props: [
				add: true,
				data: [
					controller: 'ImageList',
					imagelist-ids-value: json_encode($ids),
				],
				dataList: [
					imagelist-target: 'list',
				],
				dataAdd: [
					controller: 'Toggle',
					action: 'Toggle#changeClass ImageList#newImg',
					toggle-target-value: '#overlay-library',
					toggle-target-class-value: 'is-visible'
				],
				dragdrop: false,
				items: $items
			]
		}
	{/block}
{/embed}
