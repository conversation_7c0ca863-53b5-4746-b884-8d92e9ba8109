<?php declare(strict_types=1);

namespace App\PostType\Profile\AdminModule\Components\DataGrid;


use App\Infrastructure\Ublaboo\DataSource\ElasticsearchDataSource;
use App\Infrastructure\Ublaboo\DataSource\DataMapper;
use App\Model\ElasticSearch\IndexModel;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;
use App\Model\Security\User;
use App\Model\Translator;
use App\PostType\Branch\Model\Orm\BranchLocalizationRepository;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use App\PostType\Faculty\Model\Orm\FacultyLocalizationRepository;
use App\PostType\Profile\Model\Orm\Profile;
use App\PostType\Profile\Model\Orm\Sections\ProfilePersonalSection;
use App\PostType\Profile\Model\Orm\Settings\ProfileInterestsSetting;
use App\PostType\Profile\Model\Orm\Settings\ProfileRatingSetting;
use App\PostType\ProfileTag\Model\Orm\ProfileTag\ProfileTagLocalizationRepository;
use App\PostType\Programme\Model\Orm\ProgrammeLocalizationRepository;
use Elasticsearch\Client as ElasticsearchClient;
use Nette\Application\UI\Control;
use Nette\Utils\Arrays;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use App\Infrastructure\Ublaboo\DataGrid\DataGrid as UblabooDataGrid;
use Ublaboo\DataGrid\Filter\FilterSelect;
use Ublaboo\DataGrid\Utils\Sorting;

class DataGrid extends Control
{

	use HasMutationColumn;

	public function __construct(
//		private readonly ICollection $collection,
		private readonly Translator $translator,
		private readonly Orm $orm,
//		private readonly IndexModel $indexModel,
		private readonly MutationsHolder $mutationsHolder,
		private readonly FacultyLocalizationRepository $facultyLocalizationRepository,
		private readonly ProgrammeLocalizationRepository $programmeLocalizationRepository,
		private readonly BranchLocalizationRepository $branchLocalizationRepository,
		private readonly ProfileTagLocalizationRepository $profileTagLocalizationRepository,
		private readonly ElasticsearchClient $esClient,
		private readonly User $user,
	)
	{

	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}

	private static function serializeItems(array $values, ?string $sep = null, ?array $titles = null): string
	{
		$values = array_filter($values);
		if ($titles !== null) {
			$values = array_map(fn(int|string $val) => $titles[$val] ?? null, $values);
		}

		return implode($sep, $values);
	}

	public static function getYearsRange(string $from = '-100years', string $to = 'now'): array
	{
		$yearMin = intval((new DateTimeImmutable($from))->format('Y'));
		$yearMax = intval((new DateTimeImmutable($to))->format('Y'));

		$yearsRange = range($yearMax, $yearMin, 1);

		return array_combine($yearsRange, $yearsRange);
	}

	public function createComponentGrid(): UblabooDataGrid
	{
		$mutation = $this->mutationsHolder->getDefault();
		$esIndex = $this->orm->esIndex->getAllLastActive($mutation);

		$dataSource = new ElasticsearchDataSource(
			client: $this->esClient, // Does not use Elastica unfortunately...
			indexName: $esIndex->esName,
			elasticQueryMappers: $this->getDataMappers(),
		);

		$grid = new UblabooDataGrid;
		$grid->setStrictSessionFilterValues(false);
		$grid->setRememberState(false);

		$grid->setDefaultSort(['nameSort' => 'asc']);
		$grid->setTranslator($this->translator);

		$baseFilter = new FilterSelect($grid, 'type', 'type', [
			'type' => 'profile',
		], 'type');

		$baseFilter = $baseFilter->setValue('profile');
		$dataSource->applyFilterSelect($baseFilter);
		$baseSort = new Sorting(['nameSort' => 'asc']);
		$dataSource->sort($baseSort);

		$grid->setDataSource($dataSource);
		$grid->setItemsPerPageList([10, 50], false);

		$allPrompt = $this->translator->translate('all');

//		$grid->addColumnText('id', 'id')->setSortable()->setFilterText()->setExactSearch();
		$grid->addColumnText('nameFilter', 'grid_label_profile_name')
			->setSortable('nameSort')->setFilterText();

		foreach (['faculty', 'programme', 'branch'] as $group) {
			$facultyPairs = match ($group) {
				'faculty' => $this->facultyLocalizationRepository->findForSelectPairs(
					mutation: $mutation,
					findBy: ['faculty->isInstitution' => false]
				),
				'programme' => $this->programmeLocalizationRepository->findForSelectPairs($mutation),
				'branch' => $this->branchLocalizationRepository->findForSelectPairs($mutation),
			};

			$grid->addColumnText($group, 'grid_label_' . $group)
				->setRenderer(fn(array $data) => self::serializeItems(
					values: $data[$group] ?? [],
					sep: ' <br> ',
					titles: Arrays::flatten(
						array: $facultyPairs,
						preserveKeys: true
					),
				))->setTemplateEscaping(false)->setFilterMultiSelect($facultyPairs)
				->setPrompt($allPrompt);
		}

		$grid->addColumnText('year', 'grid_label_year')->setSortable()
			->setTemplateEscaping(false)
			->setFilterMultiSelect(self::getYearsRange())
			->setPrompt($allPrompt);

		$segmentTitles = array_map(fn($val) => $this->translator->translate($val), Profile::segmentTitles());
		$grid->addColumnText('segment', 'grid_label_profile_segment')
			->setRenderer(fn(array $data) => $segmentTitles[$data['segment']] ?? '')
			->setFilterSelect($segmentTitles)
			->setPrompt($allPrompt);

		$grid->addColumnText('address', 'grid_label_address')->setFilterText();
		$grid->addColumnText('location', 'grid_label_location')->setFilterText();

		$grid->addColumnText('email', 'grid_label_email')->setFilterText();

		$grid->addColumnDateTime('birthdate', 'grid_label_birthdate')
			->setFilterDateRange();

		$genders = ProfilePersonalSection::genders();
		$genders = array_map(fn(string $val): string =>
			$this->translator->translate($val), array_combine($genders, $genders)
		);
		$grid->addColumnText('gender', 'grid_label_gender')
			->setRenderer(fn(array $data) => $genders[$data['gender']] ?? '')
			->setFilterSelect($genders)
			->setPrompt($allPrompt);

		$grid->addColumnText('employments', 'grid_label_employments')
			->setFilterText();

		$grid->addColumnNumber('score', 'grid_label_score')
			->setSortable()->setFilterRange();

		$blockedTitles = [
			1 => 'blocked',
			0 => 'notBlocked',
		];
		$blockedTitles = array_map(fn(string $val): string =>
			$this->translator->translate($val), $blockedTitles
		);
		$grid->addColumnText('blockedAccount', 'grid_label_blocked_account')
			->setRenderer(fn(array $data) => $blockedTitles[$data['blockedAccount']] ?? '')
			->setFilterSelect($blockedTitles)->setPrompt($allPrompt);

		$interests = ProfileInterestsSetting::keys();
		$interests = array_combine($interests, $interests);
		$interests = array_map(fn(string $val) => $this->translator->translate('interests_' . $val), $interests);
		$grid->addColumnText('interests', 'grid_label_interests')
			->setRenderer(fn(array $data) => self::serializeItems(
				values: $data['interests'] ?? [],
				sep: ' <br> ',
				titles: Arrays::flatten(array: $interests, preserveKeys: true),
			))->setTemplateEscaping(false)->setFilterMultiSelect($interests)
			->setPrompt($allPrompt);

		$verifiedTitles = [
			0 => 'not_verified',
			1 => 'verified',
		];
		$verifiedTitles = array_map(fn(string $val): string =>
			$this->translator->translate($val), $verifiedTitles
		);
		$grid->addColumnText('verified', 'grid_label_verified')
			->setRenderer(fn(array $data): string => $verifiedTitles[$data['verified']])
			->setFilterSelect($verifiedTitles)->setPrompt($allPrompt);

		$profileTagsTitles = $this->profileTagLocalizationRepository->findAll()->fetchPairs('profileTag->id', 'name');
		$grid->addColumnText('tags', 'grid_label_tags')
			->setRenderer(fn(array $data) => self::serializeItems(
				values: $data['tags'] ?? [],
				sep: ' <br> ',
				titles: $profileTagsTitles,
			))->setTemplateEscaping(false)->setFilterMultiSelect($profileTagsTitles)
			->setPrompt($allPrompt);

		$ratingTitles = ProfileRatingSetting::options();
		$ratingTitles = array_map(fn(string $val): string =>
			$this->translator->translate($val), $ratingTitles
		);
		$grid->addColumnText('rating', 'grid_label_rating')
			->setRenderer(fn(array $data) => isset($data['rating']) ? $ratingTitles[$data['rating']] : '')
			->setTemplateEscaping(false)->setFilterMultiSelect($ratingTitles)
			->setPrompt($allPrompt);

		if ($this->user->isAdmin() || $this->user->isDeveloper()) {
			$grid->addAction('edit', 'Edit', 'Profile:edit')
				->setClass('btn btn-xs btn-primary');
		}

		$grid->addExportCsv(
			text: 'CSV Export',
			csvFileName: 'profiles.csv',
			outputEncoding: 'UTF-8',
			delimiter: ';',
			includeBom: false,
			filtered: true,
			full: true,
		);

		return $grid;
	}

	private function getDataMappers(): array
	{
		$mappers = [];

		// add boost
		$template['term']['type'] = ['value' => null, 'boost' => 10];
		$mappers[] = new DataMapper(['[term][type][query]' => '[term][type][value]'], $template);

		// {"range":{"filter.publishDate":{"gte":1625349600,"lte":1654379999}}}
		// split and switch
		$mappers[] = new DataMapper([
			'[range][filter.publishDate][gte]' => '[0][range][filter.publishDate][gte]',
			'[range][filter.publishDate][lte]' => '[1][range][filter.publishDate][lte]'
		]);


		// fix old 'should' syntax for ES
		$mappers[] = new DataMapper([
			'[bool][should][0]' => '[bool][should]',
		]);

		return $mappers;
	}

}
