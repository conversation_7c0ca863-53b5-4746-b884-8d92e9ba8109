<?php declare(strict_types = 1);

namespace App\PostType\Profile\FrontModule\Components\ProfileLocalizationStructuredData;

use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\Model\Orm\Mutation\Mutation;
use Nette\Application\UI\Control;
use App\Model\TranslatorDB;

class ProfileLocalizationStructuredData extends Control
{

	public function __construct(
		private ProfileLocalization $profileLocalization,
		private Mutation $mutation,
		private TranslatorDB $translator
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->profileLocalization = $this->profileLocalization;
		$this->template->mutation = $this->mutation;
		$this->template->templates = FE_TEMPLATE_DIR;

		$this->template->content = (isset($this->profileLocalization->cf->base->annotation)) ? $this->profileLocalization->cf->base->annotation : '';
		$this->template->publisher = 'superadmin';

//		$this->template->author = $this->profileLocalization->authors->fetch();


		$this->template->render(__DIR__ . '/profileLocalizationStructuredData.latte');
	}

}
