<?php declare(strict_types = 1);

namespace App\PostType\Profile\Model;

use App\PostType\Profile\Model\Orm\Profile;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

final class ProfileLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): ProfileLocalization
	{
		$profileLocalization = new ProfileLocalization();
		$this->orm->profileLocalization->attach($profileLocalization);
		$profileLocalization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Profile();
			$profileLocalization->profile = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Profile);
			$profileLocalization->profile = $localizableEntity;
		}

		$this->orm->persistAndFlush($profileLocalization);

		return $profileLocalization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof ProfileLocalization);

		$parent = $localizableEntity->getParent();
		assert($parent instanceof Profile);
		$this->orm->profileLocalization->remove($localizableEntity);

		if ($parent->getLocalizations()->count() === 0) {
			// Note - use cascade in DB instead for deleting relations ?
			foreach ($parent->followers as $profileFollowerRelation) {
				$this->orm->profileFollowing->remove($profileFollowerRelation);
			}
			foreach ($parent->followings as $profileFollowingRelation) {
				$this->orm->profileFollowing->remove($profileFollowingRelation);
			}
			foreach ($parent->invites as $profileInvite) {
				$this->orm->profileInvite->remove($profileInvite);
			}
			foreach ($parent->educations as $profileEducation) {
				$this->orm->profileEducation->remove($profileEducation);
			}
			$this->orm->profile->remove($parent);
		}

		$this->orm->flush();
	}

}
