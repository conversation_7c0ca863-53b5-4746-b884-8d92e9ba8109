<?php declare(strict_types=1);

namespace App\PostType\Profile\Model\Orm\Settings;

/**
 * @property string $avatar
 * @property string $contact
 * @property string $socials
 * @property string $personal
 * @property string $address
 * @property string $location
 * @property string $educations
 * @property string $employments
 */
final class ProfileVisibilitySetting extends ProfileSetting
{
	// Visible to all with profiles
	public const string VISIBILITY_ALL = 'all';
	// Visible to those who profile is following
	public const string VISIBILITY_FOLLOWINGS = 'followings';
	// Visible to profile followers
	public const string VISIBILITY_FOLLOWERS = 'followers';
	// Not visible
	public const string VISIBILITY_NONE = 'none';
	//
	public const string VISIBILITY_DEFAULT = self::VISIBILITY_ALL;

	public function initDefaults(): void
	{
		foreach (self::keys() as $key) {
			$this[$key] = self::VISIBILITY_DEFAULT;
		}
	}

	public static function keys(): array
	{
		return [
			'avatar', // Avatar
			'contact', // Kontakty
			'socials', // Soci<PERSON><PERSON>í sít<PERSON>
			'personal', // Oso<PERSON><PERSON><PERSON>
			'address', // Kontaktní adresa
			'location', // Aktuální poloha
			'educations', // Vzdělání
			'employments', // Zaměstnání
		];
	}

	public static function options(): array
	{
		return [
			ProfileVisibilitySetting::VISIBILITY_NONE,
			ProfileVisibilitySetting::VISIBILITY_FOLLOWINGS,
			ProfileVisibilitySetting::VISIBILITY_ALL,
		];
	}

}
