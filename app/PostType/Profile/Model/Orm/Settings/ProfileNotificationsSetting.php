<?php declare(strict_types=1);

namespace App\PostType\Profile\Model\Orm\Settings;

/**
 * @property bool $newsletter
 * @property bool $followingActivity
 * @property bool $messages
 * @property bool $answers
 * @property bool $classmateRegistered
 * @property bool $wasAdded
 * @property bool $system
 */
final class ProfileNotificationsSetting extends ProfileSetting
{
	const NOTIFICATION_NEWSLETTER = 'newsletter';
	const NOTIFICATION_FOLLOWING_ACTIVITY = 'followingActivity';
	const NOTIFICATION_MESSAGES = 'messages';
	const NOTIFICATION_ANSWERS = 'answers';
	const NOTIFICATION_CLASSMATE_REGISTERED = 'classmateRegistered';
	const NOTIFICATION_WAS_ADDED = 'wasAdded';
	const NOTIFICATION_SYSTEM = 'system';

	/*
	 * Absolventský newsletter
		Aktivita sledovaných kontaktů (to stejné co budou notifikace)
		Soukromé zprávy
		Odpovědi na mé nabídky / poptávky
		Můj spolužák se registroval do port<PERSON>lu
		Přidání mezi sledované kontakty
		Systémové (změ<PERSON> hesla, smazání účtu), pozn.: vždy zapnuté, nelze zrušit)
	 * */

	public function initDefaults(): void
	{
		foreach (self::keys() as $key) {
			$this[$key] = false;
		}
		$this[self::NOTIFICATION_SYSTEM] = true;
	}

	public static function keys(): array
	{
		return [
			self::NOTIFICATION_NEWSLETTER,
			self::NOTIFICATION_FOLLOWING_ACTIVITY,
//			self::NOTIFICATION_MESSAGES,
//			self::NOTIFICATION_ANSWERS,
			self::NOTIFICATION_CLASSMATE_REGISTERED,
			self::NOTIFICATION_WAS_ADDED,
		];
	}
}
