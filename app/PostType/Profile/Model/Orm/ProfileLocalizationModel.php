<?php declare(strict_types = 1);

namespace App\PostType\Profile\Model\Orm;

use Jaybizzle\CrawlerDetect\CrawlerDetect;

class ProfileLocalizationModel
{

	public function __construct(
		private readonly ProfileLocalizationRepository $profileLocalizationRepository,
		private readonly ProfileLocalizationTreeRepository $profileLocalizationTreeRepository,
	)
	{}


	public function increaseViews(ProfileLocalization $profileLocalization): bool
	{
		$crawlerDetect = new CrawlerDetect();
		if ($crawlerDetect->isCrawler()) {
			return false;
		}

		$profileLocalization->viewsNumber++;
		$this->profileLocalizationRepository->persistAndFlush($profileLocalization);
		return true;
	}

	/**
	 * @param array<int, int> $treeIds
	 */
	public function setCategoriesByIds(ProfileLocalization $profileLocalization, array $treeIds): void
	{
		$currentProfileLocalizationTrees = $this->profileLocalizationTreeRepository->findBy(['profileLocalization' => $profileLocalization])->fetchPairs('tree->id');
		$sort = 0;
		foreach ($treeIds as $treeId) {

			if (isset($currentProfileLocalizationTrees[$treeId])) {
				// update sort
				$profileLocalizationTree = $currentProfileLocalizationTrees[$treeId];
				assert($profileLocalizationTree instanceof ProfileLocalizationTree);
				$profileLocalizationTree->sort = $sort;
				// unset array
				unset($currentProfileLocalizationTrees[$treeId]);
			} else {
				// create new
				$profileLocalizationTree = new ProfileLocalizationTree();
				$profileLocalizationTree->profileLocalization = $profileLocalization;
				$profileLocalizationTree->tree = $treeId;
				$profileLocalizationTree->sort = $sort;
			}
			$sort++;
		}

		// remove old
		if ($currentProfileLocalizationTrees !== []) {
			foreach ($currentProfileLocalizationTrees as $currentProfileLocalizationTree) {
				$this->profileLocalizationTreeRepository->remove($currentProfileLocalizationTree);
			}
		}
	}

}
