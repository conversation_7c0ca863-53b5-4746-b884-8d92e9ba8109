<?php

namespace App\PostType\Profile\Model\Orm;

use App\Model\Orm\NestedArray;
use App\PostType\Profile\Model\Orm\Settings\ProfileInterestsSetting;
use App\PostType\Profile\Model\Orm\Settings\ProfileNotificationsSetting;
use App\PostType\Profile\Model\Orm\Settings\ProfileRatingSetting;
use App\PostType\Profile\Model\Orm\Settings\ProfileVisibilitySetting;

/**
 * @property ProfileVisibilitySetting $visibility
 * @property ProfileInterestsSetting $interests
 * @property ProfileNotificationsSetting $notifications
 * @property ProfileRatingSetting $rating
 */
class ProfileSettings extends NestedArray
{
	public function initDefaults(): void
	{
		$this->visibility->initDefaults();
		$this->notifications->initDefaults();
		$this->interests->initDefaults();
	}

	public function get(mixed $offset, ?string $nestedClass = null): mixed
	{
		$nestedSectionClass = __NAMESPACE__ . sprintf('\\Settings\\Profile%sSetting', ucfirst($offset));
		return parent::get($offset, class_exists($nestedSectionClass) ? $nestedSectionClass : null);
	}
}
