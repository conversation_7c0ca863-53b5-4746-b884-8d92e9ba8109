<?php declare(strict_types=1);

namespace App\PostType\Profile\Components\Form;

use App\Model\CustomField\SuggestUrls;
use App\Model\Image\ImageObjectFactory;
use App\Model\Orm\LibraryImage\LibraryImageModel;
use App\Model\Orm\LibraryImage\LibraryImageRepository;
use App\Model\Orm\LibraryTree\LibraryTreeModel;
use App\Model\Orm\LibraryTree\LibraryTreeRepository;
use App\Model\Orm\NestedArray;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\State\StateRepository;
use App\PostType\Branch\Model\Orm\Branch;
use App\PostType\Branch\Model\Orm\BranchLocalizationRepository;
use App\PostType\Branch\Model\Orm\BranchRepository;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\FormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use App\PostType\Faculty\Model\Orm\Faculty;
use App\PostType\Faculty\Model\Orm\FacultyLocalizationRepository;
use App\PostType\Faculty\Model\Orm\FacultyRepository;
use App\PostType\Profile\AdminModule\Components\Form\FormData\ProfileFormData;
use App\PostType\Profile\Components\Form\Controls\GroupSelectBox;
use App\PostType\Profile\Model\Orm\Profile;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\PostType\Profile\Model\Orm\ProfileLocalizationModel;
use App\PostType\Profile\Model\Orm\ProfileModel;
use App\PostType\Profile\Model\Orm\Sections\ProfileEducationsInfoSection;
use App\PostType\Profile\Model\Orm\Sections\ProfileNameSection;
use App\PostType\Profile\Model\Orm\Sections\ProfilePersonalSection;
use App\PostType\Profile\Model\Orm\Settings\ProfileInterestsSetting;
use App\PostType\Profile\Model\Orm\Settings\ProfileNotificationsSetting;
use App\PostType\Profile\Model\Orm\Settings\ProfileVisibilitySetting;
use App\PostType\Programme\Model\Orm\Programme;
use App\PostType\Programme\Model\Orm\ProgrammeLocalizationRepository;
use App\PostType\Programme\Model\Orm\ProgrammeRepository;
use Nette\Application\UI\Form;
use Nette\Forms\Container;
use Nette\Forms\Container as FormContainer;
use Nette\Forms\Controls\ChoiceControl;
use Nette\Http\Request;
use Nette\Utils\Strings;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Entity\ToArrayConverter;

/**
 * Note:
 * Using AdminModule classes here in generic class (originally from Superadmin project)
 * For simplicity of using in both in Front and Admin modules
 */
abstract class ProfileFormPrescription
{
	public function __construct(
		protected readonly ProfileModel $profileModel,
		protected readonly ProfileLocalizationModel $profileLocalizationModel,
		protected readonly ParameterRepository $parameterRepository,
		protected readonly FacultyRepository $facultyRepository,
		protected readonly FacultyLocalizationRepository $facultyLocalizationRepository,
		protected readonly ProgrammeRepository $programmeRepository,
		protected readonly ProgrammeLocalizationRepository $programmeLocalizationRepository,
		protected readonly BranchRepository $branchRepository,
		protected readonly BranchLocalizationRepository $branchLocalizationRepository,
		protected readonly StateRepository $stateRepository,
		protected readonly RelationInfoFactory $relationInfoFactory,
		protected readonly Request $request,
		protected readonly Builder $coreBuilder,
		protected readonly Handler $coreHandler,
		protected readonly SuggestUrls $urls,
		protected readonly LibraryImageRepository $imageRepository,
		protected readonly ImageObjectFactory $imageObjectFactory,
		protected readonly LibraryImageModel $libraryImageModel,
		protected readonly LibraryTreeModel $libraryTreeModel,
		protected readonly LibraryTreeRepository $libraryTreeRepository,
		protected readonly string $coreFormPath,
	)
	{
	}

//	private const PHONE_REGEX = '^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{3}$';

	/** @internal */
	private array $stateDictionary;

	protected bool $validationsEnabled = true;

	private string $validationMessage = 'fill_all_required';

	public function getBaseDefinition(ProfileLocalization $profileLocalization): FormDefinition
	{
		$form = new Form();
		$form->setMappedType(ProfileFormData::class);

		return new FormDefinition(
			form: $form,
			extenders: [],
		);
	}

	// -----------------------------------------------------------------------------
	// helpers

	protected static function hasRequiredKeys(object $item, array $keys): bool
	{
		foreach ($keys as $key) {
			if (empty($item->{$key})) {
				return false;
			}
		}

		return true;
	}

	/**
	 * Returns data for given section - takes them from controls model
	 * Used for processing sent data (onSuccess hook on form)
	 *
	 * @param Form $form
	 * @param string $section
	 * @param bool $toList
	 * @return array
	 */
	protected static function getFormSectionItems(Form $form, string $section, bool $toList = false): array
	{
		$items = (array)self::getFormSectionHttpData($form, $section);
		return $toList ? array_values($items) : $items;
	}

	/**
	 * Return POST data for given section - used for building controls for multiforms (onAnchor hook on form)
	 *
	 * This method is important because it is used as potential structure source in multiforms.
	 * Typically, if it returns non-null value it is used instead of data from model for instance.
	 * Example of usage:
	 * <code>
	 * $data = self::getFormSectionHttpData($form, $section) ?? $profile->$section
	 * </code>
	 *
	 * @param Form $form
	 * @param string $section
	 * @return array|null NULL when no form data submitted - no POST or submit state was reset manually
	 */
	protected static function getFormSectionHttpData(Form $form, string $section): ?array
	{
		if (!$form->isSubmitted()) {
			return null;
		}

		$data = $form->getHttpData();
		$dataSection = $data[$section] ?? [];
		unset($dataSection['newItemMarker']);

		return $dataSection;
	}

	protected function sectionHydrateIn(Container $sectionContainer, object $section): void
	{
		foreach ($sectionContainer->getControls() as $key => $control) {
			if (isset($section->$key)) {
				$sectionKey = $section->$key;
				if ($control instanceof ChoiceControl && !array_key_exists($sectionKey, $control->getItems())) {
					$sectionKey = null;
				}
				$control->setDefaultValue($sectionKey);
			}
		}
	}

	protected function sectionHydrateOut(iterable $sectionFormData, NestedArray $section): void
	{
		$section->add($sectionFormData);
	}

	/**
	 * Default section extender - uses nested array $profile->section->$section as data source
	 *
	 * @param string $section
	 * @param ProfileLocalization $profileLocalization
	 * @param callable(string $section, Form $form, ProfileLocalization $profileLocalization): void $addHandler
	 * @param callable(string $section, Form $form, ProfileLocalization $profileLocalization): void|null $successHandler
	 * @return FormExtender
	 */
	protected function createSectionFormExtender(string $section, ProfileLocalization $profileLocalization, callable $addHandler, callable $successHandler = null): FormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($addHandler, $section, $profileLocalization) {
				$form->onAnchor[] = function (Form $form) use ($addHandler, $section, $profileLocalization) {
					($addHandler)($section, $form, $profileLocalization);
				};
			},
			successHandler: function (Form $form, object $formData) use ($section, $successHandler, $profileLocalization) {
				if (!isset($successHandler)) {
					$successHandler = function (string $section, Form $form, object $formData, ProfileLocalization $profileLocalization): void {
						$profile = $profileLocalization->getParent();
						// TODO so far everything is stored in profile (non localized)
						$this->sectionHydrateOut($formData->$section ?? [], $profile->sections->$section);
					};
				}
				($successHandler)($section, $form, $formData, $profileLocalization);
			},
			templateParts: $this->createSectionTemplateParts($section),
			name: $section,
		);
	}

	// -----------------------------------------------------------------------------
	// template parts (extend these in admin module)

	protected function createSectionTemplateParts(string $section): array
	{
		return [];
	}

	protected function createVisibilityTemplateParts(): array
	{
		return [];
	}

	protected function createInterestsTemplateParts(): array
	{
		return [];
	}

	protected function createNotificationsTemplateParts(): array
	{
		return [];
	}

	protected function createEducationsTemplateParts(string $section, string $infoSection, Profile $profile): array
	{
		return [];
	}

	protected function createEducationsExternalTemplateParts(string $section, ProfileLocalization $profileLocalization): array
	{
		return [];
	}

	protected function createSocialsTemplateParts(string $section, ProfileLocalization $profileLocalization): array
	{
		return [];
	}

	protected function createEmploymentsTemplateParts(string $section, ProfileLocalization $profileLocalization): array
	{
		return [];
	}

	// -----------------------------------------------------------------------------
	// dictionaries

	protected function getStateDictionary(): array
	{
		if (!isset($this->stateDictionary)) {
			$allDictionary = $this->stateRepository->getCodeNamePairs();
			$commonUsedDictionary = [];

			$commonUsedCodes = ['CZ', 'SK'];
			foreach ($commonUsedCodes as $commonUsedCode) {
				$commonUsedDictionary[$commonUsedCode] = $allDictionary[$commonUsedCode];
				unset($allDictionary[$commonUsedCode]);
			}

			$this->stateDictionary = [
				'common_used' => $commonUsedDictionary,
				'all' => $allDictionary,
			];
		}

		return $this->stateDictionary;
	}

	protected function getSocialsDictionary(): array
	{
		return [
			'facebook' => 'Facebook',
			'twitter' => 'Twitter / X',
			'linkedin' => 'LinkedIn',
			'instagram' => 'Instagram',
			'whatsapp' => 'WhatsApp',
			'snapchat' => 'Snapchat',
			'github' => 'Github',
			'personal' => 'Personal web',
		];
	}

	protected function getInterestsDictionary(string $section): array
	{
		$interestsOptions = ProfileInterestsSetting::keys();
		$interestsOptions = array_map(
			callback: fn(string $val): string => $section . '_' . $val,
			array: array_combine($interestsOptions, $interestsOptions)
		);

		return $interestsOptions;
	}

	// -----------------------------------------------------------------------------
	// sections extenders

	/**
	 * @param Profile $profile
	 * @param array|null $whitelist Only these visibility keys
	 * @param array|null $blacklist Not these visibility keys
	 * @return FormExtender
	 */
	public function addVisibility(Profile $profile, array $whitelist = null, array $blacklist = null): FormExtender
	{
		$section = 'visibility';
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($profile, $section, $whitelist, $blacklist) {
				$form->onAnchor[] = function (Form $form) use ($profile, $section, $whitelist, $blacklist) {
					$visibilityOptions = ProfileVisibilitySetting::options();
					$visibilityOptions = array_map(
						callback: fn(string $val): string => $section . '_' . $val,
						array: array_combine($visibilityOptions, $visibilityOptions)
					);

					$settingsSection = $profile->settings->$section;
					$container = $form->addContainer($section);

					$keys = ProfileVisibilitySetting::keys();
					if ($whitelist !== null) {
						$keys = array_intersect($keys, $whitelist);
					}
					if ($blacklist !== null) {
						$keys = array_diff($keys, $blacklist);
					}

					foreach ($keys as $key) {
						$container->addSelect($key, $key, $visibilityOptions)
							->setDefaultValue(
								isset($settingsSection->$key) && isset($visibilityOptions[$settingsSection->$key]) ?
									$settingsSection->$key : ProfileVisibilitySetting::VISIBILITY_NONE
							);
					}
				};
			},
			successHandler: function (Form $form) use ($profile, $section) {
				$profile->settings->$section->add($form[$section]->getValues());
			},
			templateParts: $this->createVisibilityTemplateParts(),
			name: $section,
		);
	}

	public function addInterests(Profile $profile): FormExtender
	{
		$section = 'interests';
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($profile, $section) {
				$form->onAnchor[] = function (Form $form) use ($profile, $section) {
					$interestsOptions = $this->getInterestsDictionary($section);

					$container = $form->addContainer($section);
					foreach ($interestsOptions as $key => $caption) {
						$container->addCheckbox($key, $caption)
							->setDefaultValue($profile->settings->$section->$key ?? null);
					}
				};
			},
			successHandler: function (Form $form) use ($profile, $section) {
				$profile->settings->$section->add($form[$section]->getValues());
			},
			templateParts: $this->createInterestsTemplateParts(),
			name: $section,
		);
	}

	public function addNotifications(Profile $profile): FormExtender
	{
		$section = 'notifications';
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($profile, $section) {
				$form->onAnchor[] = function (Form $form) use ($profile, $section) {
					$notificationsOptions = ProfileNotificationsSetting::keys();
					$notificationsOptions = array_map(
						callback: fn(string $val): string => $section . '_' . $val,
						array: array_combine($notificationsOptions, $notificationsOptions)
					);

					$container = $form->addContainer($section);
					foreach ($notificationsOptions as $key => $caption) {
						$container->addCheckbox($key, $caption)
							->setDefaultValue($profile->settings->$section->$key ?? null);
					}
				};
			},
			successHandler: function (Form $form) use ($profile, $section) {
				$profile->settings->$section->add($form[$section]->getValues());
			},
			templateParts: $this->createNotificationsTemplateParts(),
			name: $section,
		);
	}

	public function addName(ProfileLocalization $profileLocalization): FormExtender
	{
		$section = 'name';
		return $this->createSectionFormExtender(
			section: $section,
			profileLocalization: $profileLocalization,
			addHandler: function (string $section, Form $form) use ($profileLocalization): void {
				$container = $form->addContainer($section);
				$container->addText('firstname', 'form_label_firstname')
					->setRequired($this->validationsEnabled ? $this->validationMessage : false);
				$container->addText('middlename', 'form_label_middlename');
				$container->addText('lastname', 'form_label_lastname')
					->setRequired($this->validationsEnabled ? $this->validationMessage : false);
				$container->addText('titlesBefore', 'form_label_titles_before');
				$container->addText('titlesAfter', 'form_label_titles_after');
				$container->addText('lastnameStudy', 'form_label_lastname_study');
				$container->addCheckbox('lastnameSame', 'form_label_lastname_same');

				$this->sectionHydrateIn($container, $profileLocalization->sectionsTotal->$section);
			},
		);
	}

	public function addContact(ProfileLocalization $profileLocalization): FormExtender
	{
		$section = 'contact';
		return $this->createSectionFormExtender(
			section: $section,
			profileLocalization: $profileLocalization,
			addHandler: function (string $section, Form $form) use ($profileLocalization): void {
				$container = $form->addContainer($section);
				$container->addText('email', 'form_label_email');
				$container->addText('email2', 'form_label_email_secondary');
				$container->addText('phone', 'form_label_phone');

				if ($this->validationsEnabled) {
					$container['email']->setRequired(true)
						->addRule(Form::Email);
					$container['email2']->addRule(Form::Email);
//					$container['phone']->addRule(validator: Form::PATTERN, errorMessage: 'Invalid phone', arg: self::PHONE_REGEX);
				}

				$this->sectionHydrateIn($container, $profileLocalization->sectionsTotal->$section);
			},
		);
	}

	public function addSocials(ProfileLocalization $profileLocalization): FormExtender
	{
		$section = 'socials';
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($profileLocalization, $section) {
				$populateContainer = function (FormContainer $container, ?object $social = null) {
					// TODO socials types - parameter?
					$types = $this->getSocialsDictionary();

					$container->addSelect('type', 'form_label_type', $types)
						->setPrompt('---')
						->setDefaultValue(isset($social->type) && isset($types[$social->type]) ? $social->type : null);
					$container->addText('url', 'form_label_url')
						/** @phpstan-ignore-next-line */
						->setDefaultValue($social?->url);
				};

				$form->onAnchor[] = function (Form $form) use ($populateContainer, $profileLocalization, $section) {
					// Data source is either sent form data (POST) or stored data in model relations
					$formDataSection = self::getFormSectionHttpData($form, $section);

					if ($formDataSection === null) {
						$formDataSection = [];
						foreach ($profileLocalization->sectionsTotal->$section as $type => $url) {
							$formDataSection[] = [
								'type' => $type,
								'url' => $url,
							];
						}
					}
					$formDataSection = array_filter($formDataSection, function (array $item): bool {
						return !empty($item['type']) && !empty($item['url']);
					});

					$container = $form->addContainer($section);
					foreach ($formDataSection as $itemKey => $item) {
						$populateContainer($container->addContainer($itemKey), (object)$item);
					}
					$populateContainer($container->addContainer('newItemMarker'));
				};
				/*$form->onValidate[] = function (Form $form) use ($section) {

				};*/
			},
			successHandler: function (Form $form) use ($profileLocalization, $section) {
				$profile = $profileLocalization->getParent();
				$profile->sections->$section->clear();

				assert($form[$section] instanceof Container);
				$items = self::getFormSectionItems($form, $section);
				foreach ($items as $item) {
					if (!empty($item['type']) && !empty($item['url'])) {
						$profile->sections->$section->{$item['type']} = $item['url'];
					}
				}
			},
			templateParts: $this->createSocialsTemplateParts($section, $profileLocalization),
			name: $section,
		);
	}

	public function addPersonal(ProfileLocalization $profileLocalization): FormExtender
	{
		$section = 'personal';
		return $this->createSectionFormExtender(
			section: $section,
			profileLocalization: $profileLocalization,
			addHandler: function (string $section, Form $form) use ($profileLocalization): void {
				$container = $form->addContainer($section);
				$genders = ProfilePersonalSection::genders();
				$genders = array_map('ucfirst', array_combine($genders, $genders));
				$container->addSelect('gender', 'form_label_gender', $genders)
					->setPrompt('---');
				$container->addText('birthdate', 'form_label_birthdate')
					->setHtmlType('date');
				$container->addText('city', 'form_label_city_born');
				$container->addSelect('state', 'form_label_state_born', $this->getStateDictionary())
					->setPrompt('---');

				$sectionValuesIn = $profileLocalization->sectionsTotal->$section->toValues();
				$sectionValuesIn->birthdate = isset($sectionValuesIn->birthdate) ?
					$sectionValuesIn->birthdate->format('Y-m-d') : null;
				$this->sectionHydrateIn($container, $sectionValuesIn);
			},
		);
	}

	public function addAddress(ProfileLocalization $profileLocalization): FormExtender
	{
		$section = 'address';
		return $this->createSectionFormExtender(
			section: $section,
			profileLocalization: $profileLocalization,
			addHandler: function (string $section, Form $form) use ($profileLocalization): void {
				$container = $form->addContainer($section);

				$container->addText('street', 'form_label_street');
				$container->addText('city', 'form_label_city')
					->setRequired($this->validationsEnabled ? $this->validationMessage : false);
				$container->addText('zip', 'form_label_zip')
					->setRequired($this->validationsEnabled ? $this->validationMessage : false);
				$container->addSelect('state', 'form_label_state', $this->getStateDictionary())
					->setPrompt('---')
					->setRequired($this->validationsEnabled ? $this->validationMessage : false);

				$this->sectionHydrateIn($container, $profileLocalization->sectionsTotal->$section);
			},
		);
	}

	public function addLocation(ProfileLocalization $profileLocalization): FormExtender
	{
		$section = 'location';
		return $this->createSectionFormExtender(
			section: $section,
			profileLocalization: $profileLocalization,
			addHandler: function (string $section, Form $form) use ($profileLocalization): void {
				$container = $form->addContainer($section);

				$container->addCheckbox('sameAsContact', 'form_label_location_same');
				$container->addText('city', 'form_label_city');
				$container->addSelect('state', 'form_label_state', $this->getStateDictionary())
					->setPrompt('---');

				$this->sectionHydrateIn($container, $profileLocalization->sectionsTotal->$section);
			},
		);
	}

	public function addEducations(ProfileLocalization $profileLocalization, bool $addItemOnEmpty = false): FormExtender
	{
		$profile = $profileLocalization->getParent();
		$mutation = $profileLocalization->getMutation();

		$section = 'educations';
		$infoSection = $section . 'Info';
		$processedItems = [];
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($profile, $mutation, $section, $infoSection, &$processedItems, $addItemOnEmpty) {
				$populateContainer = function (FormContainer $container, ?object $education = null) use ($mutation) {
					if ($education instanceof Entity) {
						$education = (object)$education->toArray(ToArrayConverter::RELATIONSHIP_AS_ID);
					}
					$education = (object)array_map(
						callback: fn($val) => $val instanceof \DateTimeInterface ? $val->format('Y') : strval($val),
						array: (array)$education,
					);

					$container->addHidden('id');
					$container->addText('from', 'form_label_from')
						->setHtmlType('number');
					$container->addText('to', 'form_label_to')
						->setHtmlType('number');

					foreach (['faculty' => null, 'programme' => 'faculty', 'branch' => 'programme'] as $group => $groupBy) {
						$groupSelect = new GroupSelectBox(
							label:'form_label_' . $group,
							itemsList: match ($group) {
								'faculty' => $this->facultyLocalizationRepository->findForSelectList(
									mutation: $mutation,
									findBy: ['faculty->isInstitution' => false]
								),
								'programme' => $this->programmeLocalizationRepository->findForSelectList($mutation),
								'branch' => $this->branchLocalizationRepository->findForSelectList($mutation),
							},
							group: $group,
							groupBy: $groupBy,
						);
						$groupSelect->setPrompt('---');
						$container->addComponent($groupSelect, $group);
					}

					if ($education !== null) {
						$this->sectionHydrateIn($container, $education);
					}
				};

				$form->onAnchor[] = function (Form $form) use ($populateContainer, $profile, $section, $infoSection, $addItemOnEmpty) {
					$containerInfo = $form->addContainer($infoSection);

					$levelOptions = ProfileEducationsInfoSection::levels();
					$levelOptions = array_map(
						callback: fn(string $val): string => $section . '_level_' . $val,
						array: array_combine($levelOptions, $levelOptions)
					);
					$containerInfo->addSelect('level', 'form_label_level', $levelOptions)
						->setRequired($this->validationsEnabled ? $this->validationMessage : false)
						->setPrompt('---');

					$this->sectionHydrateIn($containerInfo, $profile->sections->$infoSection);

					// Data source is either sent form data (POST) or stored data in model relations
					$container = $form->addContainer($section);
					foreach (self::getFormSectionHttpData($form, $section) ?? $profile->$section as $itemKey => $item) {
						$populateContainer($container->addContainer($itemKey), (object)$item);
					}
					$populateContainer($container->addContainer('newItemMarker'));

					if ($addItemOnEmpty && count($profile->$section) === 0 && !isset($container['new'])) {
						$populateContainer($container->addContainer('new'));
					}
				};
				$form->onValidate[] = function (Form $form) use ($section, &$processedItems) {
					// Normalization of data here (plus optional validation)
					// Final processed data then passed onto success handler...
					assert($form[$section] instanceof Container);
					$items = self::getFormSectionItems($form, $section);
					if ($this->validationsEnabled && !$items) {
						$form->addError(sprintf('%s_required', $section));
					}
					foreach ($items as $index => &$item) {
						foreach ($item as $key => $value) {
							// TODO Issue with dynamically added items from client side - they don't have server representation
							// in Nette component model yet... so adding error on whole form as only solution :-/
							$control = $form[$section][$index][$key] ?? $form;
							switch ($key) {
								case 'id':
									$value = is_numeric($value) ? intval($value) : null;
									break;
								case 'from':
								case 'to':
									// Number is taken as start of the year...
									if (is_numeric($value)) {
										$value = date_create_immutable(sprintf('%04d', $value) . '-01');
									} elseif (!empty($value)) {
										$value = date_create_immutable($value) ?: null;
									} else {
										$value = null;
									}
									if ($value === null) {
										if ($this->validationsEnabled) {
											$control->addError(sprintf('%s_%s_bad_value', $section, $key));
										} else {
											$item = null;
											break(2);
										}
									}
									break;
								case 'faculty':
								case 'programme':
								case 'branch':
									// Faculty, programme is required in each item...
									$value = is_numeric($value) ? intval($value) : null;
									if ($value !== null) {
										// --> to entity
										$value = $this->{$key . 'Repository'}->getById($value);
									}
									if (in_array($key, ['faculty', 'programme'], true) && $value === null) {
										if ($this->validationsEnabled) {
											$control->addError(sprintf('%s_%s_required', $section, $key));
										} else {
											$item = null;
											break(2);
										}
									}
									break;
								default:
									break;
							}
							$item[$key] = $value;
						}
						if ($this->validationsEnabled && isset($item)) {
							// from-to correct range
							$controlFrom = $form[$section][$index]['from'] ?? $form;
							$controlTo = $form[$section][$index]['to'] ?? $form;

							if ($item['from'] && $item['to'] && $item['from'] > $item['to']) {
								$fromToErrorMsg = sprintf('%s_from_to_error', $section);
								$controlFrom->addError($fromToErrorMsg);
								$controlTo->addError($fromToErrorMsg);
							}

							// faculty-programme-branch correct binding
							$controlFaculty = $form[$section][$index]['faculty'] ?? $form;
							$controlProgramme = $form[$section][$index]['programme'] ?? $form;
							$controlBranch = $form[$section][$index]['branch'] ?? $form;

							/** @var Faculty|null $faculty */
							$faculty = $item['faculty'];
							/** @var Programme|null $programme */
							$programme = $item['programme'];
							/** @var Branch|null $branch */
							$branch = $item['branch'];

							if ($faculty !== null && $programme !== null) {
								if (!$faculty->programmes->toCollection()->getById($programme->id)) {
									$facultyProgrammeErrorMsg = sprintf('%s_faculty_programme_binding_error', $section);
									$controlFaculty->addError($facultyProgrammeErrorMsg);
									$controlProgramme->addError($facultyProgrammeErrorMsg);
								}
								if ($branch !== null && !$programme->branches->toCollection()->getById($branch->id)) {
									$programmeBranchErrorMsg = sprintf('%s_programme_branch_binding_error', $section);
									$controlProgramme->addError($programmeBranchErrorMsg);
									$controlBranch->addError($programmeBranchErrorMsg);
								}
							}
						}
					}
					$processedItems = array_filter($items);
				};
			},
			successHandler: function (Form $form) use ($profile, $infoSection, &$processedItems) {
				assert($form[$infoSection] instanceof Container);
				$profile->sections->$infoSection->set(self::getFormSectionItems($form, $infoSection));

				$this->profileModel->setEducationsByItems($profile, $processedItems);
			},
			templateParts: $this->createEducationsTemplateParts($section, $infoSection, $profile),
			name: $section,
		);
	}

	public function addEducationsExternal(ProfileLocalization $profileLocalization): FormExtender
	{
		$section = 'educations';
		$formSection = $section . '_external';
		$processedItems = [];
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($profileLocalization, $section, $formSection, &$processedItems) {
				$populateContainer = function (FormContainer $container, ?object $education = null) {
					$container->addText('from', 'form_label_from')
						->setHtmlType('number');
					$container->addText('to', 'form_label_to')
						->setHtmlType('number');
					$container->addText('name', 'form_label_name');
					$container->addText('faculty', 'form_label_faculty');
					$container->addText('programme', 'form_label_programme');
					$container->addText('branch', 'form_label_branch');

					if ($education !== null) {
						$this->sectionHydrateIn($container, $education);
					}
				};

				$form->onAnchor[] = function (Form $form) use ($populateContainer, $profileLocalization, $formSection, $section) {
					// Data source is either sent form data (POST) or stored data in model relations
					$container = $form->addContainer($formSection);

					$profile = $profileLocalization->getParent();
					$sectionData = self::getFormSectionHttpData($form, $formSection);

					foreach ($sectionData ?? $profile->sections->$section as $itemKey => $item) {
						$populateContainer($container->addContainer($itemKey), (object)$item);
					}
					$populateContainer($container->addContainer('newItemMarker'));
				};
				$form->onValidate[] = function (Form $form) use ($section, $formSection, &$processedItems) {
					// Normalization of data here (plus optional validation)
					// Final processed data then passed onto success handler...
					assert($form[$formSection] instanceof Container);
					$items = self::getFormSectionItems(
						form: $form,
						section: $formSection,
						toList: true
					);

					foreach ($items as $index => &$item) {
						foreach ($item as $key => $value) {
							// in Nette component model yet... so adding error on whole form as only solution :-/
							$control = $form[$formSection][$index][$key] ?? $form;

							if (in_array($key, ['faculty', 'from', 'to', /*'programme'*/], true) && empty($value)) {
								if ($this->validationsEnabled) {
									$control->addError(sprintf('%s_%s_required', $section, $key));
									return;
								} else {
									$item = null;
									break;
								}
							}

							if (in_array($key, ['from', 'to'], true)) {
								if (!empty($value) && !is_numeric($value)) {
									if ($this->validationsEnabled) {
										$control->addError(sprintf('%s_%s_bad_value', $section, $key));
										return;
									} else {
										$item = null;
										break;
									}
								}
								$value = !empty($value) ? intval($value) : null;
							}

							$item[$key] = $value;
						}
					}

					$processedItems = array_filter($items);
				};
			},
			successHandler: function (Form $form) use ($profileLocalization, $section, &$processedItems) {
				$profile = $profileLocalization->getParent();
				$profile->sections->$section->set($processedItems);
			},
			templateParts: $this->createEducationsExternalTemplateParts($formSection, $profileLocalization),
			name: $formSection,
		);

	}

	public function addEmployments(ProfileLocalization $profileLocalization, bool $addItemOnEmpty = false): FormExtender
	{
		$section = 'employments';
		$processedItems = [];
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($profileLocalization, $section, &$processedItems, $addItemOnEmpty) {
				$populateContainer = function (FormContainer $container, ?object $employment = null) {
					$container->addText('employer', 'form_label_employer');
					$container->addText('position', 'form_label_position');
					$container->addText('note', 'form_label_note');
					$container->addText('from', 'form_label_from')
						->setHtmlType('number');
					$container->addText('to', 'form_label_to')
						->setHtmlType('number');
					$container->addCheckbox('untilNow', 'form_label_until_now');

					if ($employment !== null) {
						$this->sectionHydrateIn($container, $employment);
					}
				};

				$form->onAnchor[] = function (Form $form) use ($populateContainer, $profileLocalization, $section, $addItemOnEmpty) {
					// Data source is either sent form data (POST) or stored data in model relations
					$container = $form->addContainer($section);

					$profile = $profileLocalization->getParent();
					foreach (self::getFormSectionHttpData($form, $section) ?? $profile->sections->$section as $itemKey => $item) {
						$populateContainer($container->addContainer($itemKey), (object)$item);
					}
					$populateContainer($container->addContainer('newItemMarker'));

					if ($addItemOnEmpty && count($profileLocalization->sectionsTotal->$section) === 0 && !isset($container['new'])) {
						$populateContainer($container->addContainer('new'));
					}
				};
				$form->onValidate[] = function (Form $form) use ($section, &$processedItems) {
					// Normalization of data here (plus optional validation)
					// Final processed data then passed onto success handler...
					assert($form[$section] instanceof Container);
					$items = self::getFormSectionItems(
						form: $form,
						section: $section,
						toList: true
					);
					/*if ($this->validationsEnabled && !$items) {
						$form->addError(sprintf('%s_required', $section));
						return;
					}*/
					foreach ($items as $index => &$item) {
						if (array_filter($item) === []) {
							$item = null;
							continue;
						}

						foreach ($item as $key => $value) {
							// in Nette component model yet... so adding error on whole form as only solution :-/
							$control = $form[$section][$index][$key] ?? $form;

							/*if (in_array($key, ['employer'], true) && empty($value)) {
								if ($this->validationsEnabled) {
									$control->addError(sprintf('%s_%s_required', $section, $key));
									return;
								} else {
									$item = null;
									break(2);
								}
							}*/

							if (in_array($key, ['from', 'to'], true)) {
								if (!empty($value) && !is_numeric($value)) {
									if ($this->validationsEnabled) {
										$control->addError(sprintf('%s_%s_bad_value', $section, $key));
										return;
									} else {
										$item = null;
										break;
									}
								}
								$value = !empty($value) ? intval($value) : null;
							}

							$item[$key] = $value;
						}
					}
					$processedItems = array_filter($items);
				};
			},
			successHandler: function (Form $form) use ($profileLocalization, $section, &$processedItems) {
				$profile = $profileLocalization->getParent();
				$profile->sections->$section->set($processedItems);
			},
			templateParts: $this->createEmploymentsTemplateParts($section, $profileLocalization),
			name: $section,
		);
	}
}
