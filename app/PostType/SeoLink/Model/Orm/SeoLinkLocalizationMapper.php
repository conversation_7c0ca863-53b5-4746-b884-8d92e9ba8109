<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class SeoLinkLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'seolink_localization';

	public function findAllIds(?int $limit): Result
	{
		$builder = $this->builder()->select('sll.id')
			->from($this->tableName, 'sll')
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$builder = $this->builder()->select('sll.id')
			->from($this->tableName, 'sll')
			->andWhere('sll.mutationId = %i', $mutation->id)
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}
}
