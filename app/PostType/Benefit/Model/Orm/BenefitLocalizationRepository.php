<?php declare(strict_types = 1);

namespace App\PostType\Benefit\Model\Orm;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Searchable;
use App\Model\Orm\Traits\HasPublicParameter;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalizationMapper;
use App\PostType\Page\Model\Orm\CommonTree;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method BenefitLocalization getById($id)
 * @method BenefitLocalization[]|ICollection findByIdInPathString(CommonTree $commonTree)
 * @method BenefitLocalization[]|ICollection findFiltered(array $ids)
 * @method array findAllIds(?int $limit)
 * @method BenefitLocalization[]|ICollection findByExactOrder(array $ids)
 */
final class BenefitLocalizationRepository extends Repository implements QueryForIdsByMutation, Searchable, CollectionById
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [BenefitLocalization::class];
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof BenefitLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getMapper(): BenefitLocalizationMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof BenefitLocalizationMapper);
		return $mapper;
	}


	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

}
