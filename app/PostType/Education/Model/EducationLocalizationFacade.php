<?php declare(strict_types = 1);

namespace App\PostType\Education\Model;

use App\PostType\Education\Model\Orm\Education;
use App\PostType\Education\Model\Orm\EducationLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

final class EducationLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): EducationLocalization
	{
		$educationLocalization = new EducationLocalization();
		$this->orm->educationLocalization->attach($educationLocalization);
		$educationLocalization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Education();
			$educationLocalization->education = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Education);
			$educationLocalization->education = $localizableEntity;
		}

		$this->orm->persistAndFlush($educationLocalization);

		return $educationLocalization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof EducationLocalization);

		$parent = $localizableEntity->getParent();
		assert($parent instanceof Education);
		$this->orm->educationLocalization->remove($localizableEntity);

		if ($parent->getLocalizations()->count() === 0) {
			$this->orm->education->remove($parent);
		}

		$this->orm->flush();
	}

}
