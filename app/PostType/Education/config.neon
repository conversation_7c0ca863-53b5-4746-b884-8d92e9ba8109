parameters:
	config:
		education:
			paging: 12 #temp low number - to testing

	postTypeRoutes:
		Education: education

cf:
	templates:
		educationLocalization:
			base:
				extends: @cf.base
			settings:
				type: group
				label: "Parametry"
				items:
					author:
						type: text
						label: "Autor"
					date:
						type: dateTime
						label: "Datum a č<PERSON> vložení"
					contact_person:
						type: text
						label: "Kontaktní osoba"
					lectors:
						type: text
						label: "Lektoři"
					length:
						type: text
						label: "<PERSON><PERSON><PERSON><PERSON> (rozsah)"
					from:
						type: dateTime
						label: "Začátek"
					to:
						type: dateTime
						label: "Konec"
					place:
						type: text
						label: "Místo konání"
					people_count:
						type: text
						label: "Počet účastníků"
					price:
						type: text
						label: "Cena"
					price_members:
						type: text
						label: "Cena pro členy"
					discount:
						type: text
						label: "Sleva"
					organiser:
						type: text
						label: "Pořadatel"
					external_link:
						type: text
						label: "Externí URL"





cc:
	templates:
		# ":Education:Front:Education:detail": []

application:
	mapping:
		Education: App\PostType\Education\*Module\Presenters\*Presenter

services:
	- App\PostType\Education\Model\Orm\EducationLocalizationModel
	- App\PostType\Education\AdminModule\Components\Form\EducationFormPrescription(coreFormPath: %appDir%/PostType/Core/AdminModule/Components/Form)
	- App\PostType\Education\Model\EducationLocalizationFacade
	- App\PostType\Education\AdminModule\Components\DataGrid\EducationDataGridPrescription
	- App\PostType\Education\FrontModule\Components\EducationLocalizationStructuredData\EducationLocalizationStructuredDataFactory

	-
		implement: App\PostType\Education\FrontModule\Components\Attached\AttachedEducationsFactory
		inject: true
