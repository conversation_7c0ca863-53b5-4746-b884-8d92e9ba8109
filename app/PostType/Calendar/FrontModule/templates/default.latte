{block content}
	{snippet content}
		<div class="u-mb-last-0 u-mb-md u-mb-xl@md">
			{embed $templates.'/part/box/intro.latte', object: $object}
				{block content}
					{snippet catalogFilter}
						{snippetArea filterArea}
							{embed $filterTemplate, filter: $filter, object: $object, linkSeo: $linkSeo}
								{block next}
									{define #calendarFilter}
										{var $selected = $filter->selectedParameters}
										<div class="grid__cell size--6-12@md size--4-12@lg size--3-12@xl">
											<p class="inp u-mb-0">
												<span class="inp__fix inp__fix--icon-after">
													<label for="{$name}" class="inp__label inp__label--inside">{_'form_label_find_' . $name}</label>
													<input type="text" name="filter[{$name}]" id="filter[{$name}]" class="inp__text inp__text--bold"
														data-controller="datepicker autosubmit"
														{if $name == "from"}
															data-datepicker-options-value='{
																"maxDate": "{$selected["to"] ?? "false"}"
															}'
														{elseif $name == "to"}
															data-datepicker-options-value='{
																"minDate": "{$selected["from"] ?? "false"}"
															}'
														{/if}
														data-action="change->autosubmit#submitForm"
														placeholder="{_'form_label_find_' . $name}"
														n:attr="value => $selected[$name] ?? null">
													{('calendar')|icon, 'inp__icon'}
												</span>
											</p>
										</div>
									{/define}

									{include #calendarFilter name: from}
									{include #calendarFilter name: to}
								{/block}
							{/embed}
						{/snippetArea}
					{/snippet}
				{/block}
			{/embed}
			{include $templates.'/part/box/content.latte'}

			{snippet items}
				{snippetArea itemsInner}
					{include $templates.'/part/crossroad/events.latte', class: 'u-mb-lg u-mb-xl@md', items: $calendarLocalizations}
				{/snippetArea}
			{/snippet}
		</div>
	{/snippet}
{/block}
