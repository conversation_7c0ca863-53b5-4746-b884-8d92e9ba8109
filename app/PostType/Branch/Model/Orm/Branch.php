<?php declare(strict_types = 1);

namespace App\PostType\Branch\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Profile\Model\Orm\ProfileEducation;
use App\PostType\Programme\Model\Orm\Programme;
use App\PostType\Programme\Model\Orm\ProgrammeHistory;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\JsonContainer;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 *
 * @property string|null $extId
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property BranchLocalization[]|OneHasMany $localizations {1:M BranchLocalization::$branch}
 *
 * @property Programme|null $programme {M:1 Programme::$branches}
 *
 * @property ProfileEducation[]|OneHasMany $educations {1:m ProfileEducation::$branch}
 *
 * @property BranchHistory[]|OneHasMany $history {1:m BranchHistory::$branch}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class Branch extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	public function getLocalization(Mutation $mutation): BranchLocalization
	{
		$localization = $this->getLocalizations()->getBy(['mutation' => $mutation]);
		assert($localization instanceof BranchLocalization || $localization === null);
		return $localization;
	}

}
