<?php declare(strict_types = 1);

namespace App\PostType\EducationTag\Model\Orm\EducationTag;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Searchable;
use App\PostType\Author\Model\Orm\AuthorMapper;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method EducationTag getById($id)
 * @method EducationTag[]|ICollection findByExactOrder(array $ids)
 */
final class EducationTagRepository extends Repository implements CollectionById, Searchable
{

	public static function getEntityClassNames(): array
	{
		return [EducationTag::class];
	}

	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getMapper(): EducationTagMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof EducationTagMapper);
		return $mapper;
	}

}
