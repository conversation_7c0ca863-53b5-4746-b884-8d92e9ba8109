<?php declare(strict_types = 1);

namespace App\PostType\EducationTag\Model;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Education\Model\Orm\EducationLocalizationRepository;
use App\PostType\Education\Model\Orm\EducationRepository;
use App\PostType\EducationTag\Model\Orm\EducationTag\EducationTagLocalization;
use App\PostType\EducationTag\Model\Orm\EducationTag\EducationTagLocalizationRepository;
use stdClass;

class EducationTagModel
{

	public function __construct(
		private EducationTagLocalizationRepository $educationTagLocalizationRepository,
		private EducationLocalizationRepository $educationLocalizationRepository,
		private EducationRepository $educationRepository,
	)
	{
	}

	public function getTagsWithCount(Mutation $mutation): array
	{
		$educationTagLocalizations = $this->educationTagLocalizationRepository->findBy([
			'mutation' => $mutation
		]);
		$tagsWithCount = [];

		foreach ($educationTagLocalizations as $educationTagLocalization) {
			assert($educationTagLocalization instanceof EducationTagLocalization);
			$tagData = new stdClass();
			$tagData->tag = $educationTagLocalization;

			$educationIds = $this->educationRepository->findBy(['tags->id' => $educationTagLocalization->educationTag->id])->fetchPairs(null, 'id');

			$educationTagLocalizations = $this->educationLocalizationRepository->findBy([
				'mutation' => $educationTagLocalization->getMutation(),
				'education' => $educationIds,
			]);

			$tagData->count = $educationTagLocalizations->count();

			$tagsWithCount[] = $tagData;
		}

		usort($tagsWithCount, fn($a, $b) => $a->count <=> $b->count);
		return $tagsWithCount;
	}

}
