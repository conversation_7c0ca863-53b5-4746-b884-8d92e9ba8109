<?php declare(strict_types = 1);

namespace App\PostType\ProfileTag\Model;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Profile\Model\Orm\ProfileLocalizationRepository;
use App\PostType\Profile\Model\Orm\ProfileRepository;
use App\PostType\ProfileTag\Model\Orm\ProfileTag\ProfileTagLocalization;
use App\PostType\ProfileTag\Model\Orm\ProfileTag\ProfileTagLocalizationRepository;
use stdClass;

class ProfileTagModel
{

	public function __construct(
		private ProfileTagLocalizationRepository $profileTagLocalizationRepository,
		private ProfileLocalizationRepository $profileLocalizationRepository,
		private ProfileRepository $profileRepository,
	)
	{
	}

	public function getTagsWithCount(Mutation $mutation): array
	{
		$profileTagLocalizations = $this->profileTagLocalizationRepository->findBy([
			'mutation' => $mutation
		]);
		$tagsWithCount = [];

		foreach ($profileTagLocalizations as $profileTagLocalization) {
			assert($profileTagLocalization instanceof ProfileTagLocalization);
			$tagData = new stdClass();
			$tagData->tag = $profileTagLocalization;

			$profileIds = $this->profileRepository->findBy(['tags->id' => $profileTagLocalization->profileTag->id])->fetchPairs(null, 'id');

			$profileTagLocalizations = $this->profileLocalizationRepository->findBy([
				'mutation' => $profileTagLocalization->getMutation(),
				'profile' => $profileIds,
			]);

			$tagData->count = $profileTagLocalizations->count();

			$tagsWithCount[] = $tagData;
		}

		usort($tagsWithCount, fn($a, $b) => $a->count <=> $b->count);
		return $tagsWithCount;
	}

}
