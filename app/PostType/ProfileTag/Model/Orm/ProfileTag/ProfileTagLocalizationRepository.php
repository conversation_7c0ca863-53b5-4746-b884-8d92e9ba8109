<?php declare(strict_types=1);

namespace App\PostType\ProfileTag\Model\Orm\ProfileTag;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Traits\HasPublicParameter;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProfileTagLocalization getById($id)
 * @method ProfileTagLocalization[]|ICollection searchByName(string $q, array $excluded)
 * @method array findAllIds(?int $limit)
 */
final class ProfileTagLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [ProfileTagLocalization::class];
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof ProfileTagLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

	public function findForSelectList(?Mutation $mutation = null): array
	{
		$ret = [];
		$items = $this->findBy(array_filter(['mutation' => $mutation]))->orderBy('name');
		foreach ($items as $item) {
			assert($item instanceof ProfileTagLocalization);
			$ret[] = (object)[
				'id' => $item->getParent()->id,
				'name' => $item->name,
			];
		}

		return $ret;
	}
}
