<?php declare(strict_types = 1);

namespace App\PostType\ProfileTag\Model\Orm\ProfileTag;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Profile\Model\Orm\Profile;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property ProfileTagLocalization[]|OneHasMany $localizations {1:M ProfileTagLocalization::$profileTag}
 * @property Profile[]|ManyHasMany $profiles {m:m Profile::$tags}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class ProfileTag extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public const string VUT_NAME = 'vut';

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): ProfileTagLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof ProfileTagLocalization);
		return $localization;
	}

}
