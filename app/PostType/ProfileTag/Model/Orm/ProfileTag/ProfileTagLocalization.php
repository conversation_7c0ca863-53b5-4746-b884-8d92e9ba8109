<?php declare(strict_types = 1);

namespace App\PostType\ProfileTag\Model\Orm\ProfileTag;

use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\PostType\Profile\Model\Orm\ProfileLocalizationRepository;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\HasPublishable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;

/**
 * @property int $id {primary}
 * @property string $name  {default ''}
 * @property bool $public {default false}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 * @property int $sort {default 0}
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property ProfileTag $profileTag {M:1 ProfileTag::$localizations}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 *
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read  ProfileLocalization[]|ICollection $profilesPublic {virtual}
 * @property-read string $template {virtual}
 */
class ProfileTagLocalization extends RoutableEntity implements LocalizationEntity, Publishable, Editable
{

	use HasCustomFields;
	use HasCustomContent;
	use HasPublishable;

	private ProfileLocalizationRepository $profileLocalizationRepository;

	public function injectProfileRepository(ProfileLocalizationRepository $profileLocalizationRepository): void
	{
		$this->profileLocalizationRepository = $profileLocalizationRepository;
	}


	protected function getterTemplate(): string
	{
		return ':ProfileTag:Front:ProfileTag:detail';
	}


	protected function getterPath(): array
	{
		$profilePage = $this->mutation->pages->absolvents;
		$path = $profilePage->path;
		$path[] = $profilePage->id;
		return $path;
	}


	protected function getterProfilesPublic(): ICollection
	{
		$profileIds = $this->profileTag->profiles->toCollection()->fetchPairs(null, 'id');
		return $this->profileLocalizationRepository
			->findBy([
					'profile' => $profileIds,
					'mutation' => $this->mutation,
				])
			->findBy($this->profileLocalizationRepository->getPublicOnlyWhereParams());
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): ProfileTag
	{
		return $this->profileTag;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof ProfileTag);
		$this->profileTag = $parentEntity;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}
}
