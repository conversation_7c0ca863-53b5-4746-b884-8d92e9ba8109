<?php declare(strict_types=1);

namespace App\PostType\ProfileTag\Model\Orm\ProfileTag;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Searchable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProfileTag getById($id)
 * @method ProfileTag[]|ICollection findByExactOrder(array $ids)
 */
final class ProfileTagRepository extends Repository implements CollectionById, Searchable
{

	public static function getEntityClassNames(): array
	{
		return [ProfileTag::class];
	}

	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getMapper(): ProfileTagMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof ProfileTagMapper);
		return $mapper;
	}

}
