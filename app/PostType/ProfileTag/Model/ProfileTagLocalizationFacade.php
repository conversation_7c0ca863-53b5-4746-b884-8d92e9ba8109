<?php declare(strict_types = 1);

namespace App\PostType\ProfileTag\Model;


use App\PostType\ProfileTag\Model\Orm\ProfileTag\ProfileTag;
use App\PostType\ProfileTag\Model\Orm\ProfileTag\ProfileTagLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

class ProfileTagLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): LocalizationEntity
	{
		$localization = new ProfileTagLocalization();
		$this->orm->profileTagLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new ProfileTag();
			$localization->profileTag = $localizableEntity;
		} else {
			assert($localizableEntity instanceof ProfileTag);
			$localization->profileTag = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof ProfileTagLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->profileTagLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->profileTag->remove($parent);
		}

		$this->orm->flush();
	}

}
