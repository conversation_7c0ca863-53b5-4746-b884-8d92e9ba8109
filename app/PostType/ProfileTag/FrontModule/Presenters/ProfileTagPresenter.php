<?php declare(strict_types = 1);

namespace App\PostType\ProfileTag\FrontModule\Presenters;

use App\FrontModule\Presenters\BasePresenter;
use App\PostType\ProfileTag\Model\Orm\ProfileTag\ProfileTag;
use App\PostType\ProfileTag\Model\Orm\ProfileTag\ProfileTagLocalization;

/**
 * @method ProfileTag getObject()
 */
final class ProfileTagPresenter extends BasePresenter
{

	private ProfileTagLocalization $profileTagLocalization;

	public function startup(): void
	{
		parent::startup();
	}


	public function actionDetail(ProfileTagLocalization $object): void
	{
		$this->setObject($object);
		$this->profileTagLocalization = $object;
	}


	public function renderDetail(): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $this->profileTagLocalization;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = 1;
		$allPublicProfiles = $this->profileTagLocalization->profilesPublic;
		$paginator->itemCount = $allPublicProfiles->count();

		$this->template->profileTag = $this->profileTagLocalization;
		$this->template->profiles = $allPublicProfiles->limitBy($paginator->itemsPerPage, $paginator->offset);

		if ($this->isAjax()) {
			$this->redrawControl('articles');
		}
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
