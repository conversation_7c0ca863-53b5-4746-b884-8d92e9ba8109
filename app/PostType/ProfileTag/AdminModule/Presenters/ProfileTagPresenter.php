<?php declare(strict_types = 1);

namespace App\PostType\ProfileTag\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\ProfileTag\Model\Orm\ProfileTag\ProfileTagLocalization;
use App\PostType\ProfileTag\Model\ProfileTagLocalizationFacade;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;

final class ProfileTagPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'profileTag';

	private ProfileTagLocalization $profileTagLocalization;

	public function __construct(
		private DataGridFactory $dataGridFactory,
		private FormFactory $profileTagFormFactory,
		private ShellFormFactory $shellFormFactory,
		private ProfileTagLocalizationFacade $profileTagLocalizationFacade,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$profileTagLocalization = $this->orm->profileTagLocalization->getById($id);
		if ($profileTagLocalization === null) {
			$this->redirect('default');
		}

		$this->profileTagLocalization = $profileTagLocalization;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->profileTagLocalization->findAll());
	}


	protected function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->profileTagFormFactory->create($this->profileTagLocalizationFacade, $this->profileTagLocalization, $userEntity);
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity:null, entityLocalizationFacade: $this->profileTagLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
