<?php declare(strict_types = 1);

namespace App\PostType\Offer\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use App\PostType\Page\Model\Orm\CommonTree;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class OfferLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'offer_localization';

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);

		return $conventions;
	}


	/**
	 * @return ICollection|OfferLocalization[]
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $q);

		if ($excluded !== []) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection|OfferLocalization[]
	 */
	public function findByIdInPathString(CommonTree $commonTree): ICollection
	{
		$builder = $this->builder()
			->andWhere('pathString LIKE %_like_', '|' . $commonTree->id . '|');

		return $this->toCollection($builder);
	}


	public function findFiltered(array $ids): ICollection
	{
		$builder = $this->builder()->select('p.*')->from($this->tableName, 'p')
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}


	public function findAllIds(?int $limit): Result
	{
		$builder = $this->builder()->select('bl.id')
			->from($this->tableName, 'bl')
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit): Result
	{
		$builder = $this->builder()->select('bl.id')
			->from($this->tableName, 'bl')
			->andWhere('bl.mutationId = %i', $mutation->id)
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}


	public function findByExactOrder(array $ids): ICollection
	{
		$builder = $this->builder()
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}

}
