<?php declare(strict_types = 1);

namespace App\PostType\Offer\Model\Orm;

use App\Model\Orm\Searchable;
use App\PostType\OfferTag\Model\Orm\OfferTag\OfferTagMapper;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Offer getById($id)
 * @method Offer[]|ICollection searchByName(string $q, array $excluded = [])
 */
final class OfferRepository extends Repository implements Searchable
{

	public static function getEntityClassNames(): array
	{
		return [Offer::class];
	}

	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}


	public function getMapper(): OfferMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof OfferMapper);
		return $mapper;
	}

}
