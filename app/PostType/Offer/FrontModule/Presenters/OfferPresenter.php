<?php declare(strict_types = 1);

namespace App\PostType\Offer\FrontModule\Presenters;

use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\FrontModule\Presenters\HasFilterTrait;
use App\Model\BucketFilter\BucketFilterFactory;
use App\Model\BucketFilter\SetupCreator\Offer\BasicElasticItemListFactory;
use App\Model\BucketFilter\SetupCreator\Offer\BoxListFactory;
use App\Model\BucketFilter\SetupCreator\Offer\ElasticItemListFactory;
use App\Model\BucketFilter\SortCreator;
use App\Model\ElasticSearch\Common\ElasticCommon;
use App\Model\ElasticSearch\Common\ResultReader;
use App\Model\Link\LinkSeo;
use App\PostType\Offer\FrontModule\Components\Attached\AttachedOffers;
use App\PostType\Offer\Model\Orm\Offer;
use App\PostType\Offer\Model\Orm\OfferLocalization;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Offer\FrontModule\Components\OfferLocalizationStructuredData\OfferLocalizationStructuredData;

/**
 * @method OfferLocalization getObject()
 */
final class OfferPresenter extends BasePresenter
{

	use HasCustomContentRenderer;
	use HasFilterTrait;

	private OfferLocalization $offerLocalization;

	public function __construct(
		private readonly BucketFilterFactory $bucketFilterFactory,
		private readonly SortCreator $sortCreator,
		private readonly BasicElasticItemListFactory $basicElasticItemListFactory,
		private readonly ElasticItemListFactory $elasticItemListFactory,
		private readonly BoxListFactory $boxListFactory,
		private readonly ResultReader $resultReader,
		private readonly LinkSeo $linkSeo,
//		private readonly string $appDir,
//		private AttachedOffersFactory $attachedOffersFactory,
//		private OfferLocalizationModel $offerLocalizationModel,
//		private OfferLocalizationStructuredDataFactory $offerLocalizationStructuredDataFactory,
//		private OfferTagModel $offerTagModel,
	)
	{
		parent::__construct();
	}

	public function startup(): void
	{
		parent::startup();
		$this->setupCleanFilterParam();
	}


	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
	}


	public function renderDefault(CommonTree $object, string $order = 'sort'): void
	{
//		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
//		$this['pager']->object = $object;
//		$this['pager']->special = true;
//
//		$paginator = $this['pager']->getPaginator();
//		$paginator->itemsPerPage = $this->configService->get('offer', 'paging');
//
//		$possibleOfferLocalizationIds = $this->orm->offerLocalizationTree->findBy(['tree->id' => $object->id])->fetchPairs(null, 'offerLocalization->id');
//		$offerLocalizations = $this->orm->offerLocalization->findBy(['id' => $possibleOfferLocalizationIds])->orderBy('publicFrom');
//
//		$totalCount = $offerLocalizations->countStored();
//		$paginator->itemCount = $totalCount;
//
//		$this->template->tagsWithCount = $this->offerTagModel->getTagsWithCount($this->mutation);
//		$this->template->totalCount = $totalCount;
//		$this->template->offerLocalizations = $offerLocalizations->limitBy($paginator->itemsPerPage, $paginator->offset);
//
//		if ($this->isAjax()) {
//			if ($this['pager']->getParameter('more')) {
//				$this->redrawControl('articlesInner');
//				$this->redrawControl('articlesPagerBottom');
//				$this->redrawControl('articleList');
//			} else {
//				if (!$this->getSignal()) {
//					$this->redrawControl();
//				}
//			}
//		}

		$searchString = $this->getFilterParam('q', '');

		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $object;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->get('offer', 'paging');

		$this->template->cleanFilterParamForCrossroad = $this->cleanFilterParam;
		$this->template->cleanFilterParam = $this->cleanFilterParam;

		$basicElasticItemListGenerator = $this->basicElasticItemListFactory->create($searchString);
		$elasticItemListGenerator = $this->elasticItemListFactory->create(
			$this->cleanFilterParam,
		);
		$boxListGenerator = $this->boxListFactory->create(
			$this->mutation
		);

		$bucketFilter = $this->bucketFilterFactory->create(
			$basicElasticItemListGenerator,
			$elasticItemListGenerator,
			$boxListGenerator,
			resultReader: $this->resultReader,
			esIndex: $this->orm->esIndex->getCommonLastActive($this->mutation),
			commonType: ElasticCommon::TYPE_OFFER
		);

		$filter = $bucketFilter->getFilter($this->cleanFilterParam);

		$itemsObject = $bucketFilter->getItems(
			limit: $paginator->itemsPerPage,
			offset: $paginator->offset,
			sort: $this->sortCreator->create($order)
		);
		$paginator->itemCount = $itemsObject->totalCount;

		$this->template->appDir = APP_DIR;
		$this->template->filter = $filter;
		$this->template->catalogOrder = $order;
		$this->template->categoriesProductCount = $filter->categories ?? [];
		$this->template->offerLocalizations = $itemsObject->items;
		$this->template->linkSeo = $this->linkSeo;

		if ($this->isAjax()) {
			if (isset($this->cleanFilterParam)) {
				$this->payload->newUrl = $this->getFilterUrl();
			}

			if (isset($this->params['more'])) {
				$this->redrawControl('itemsInner');
				$this->redrawControl('itemsPagerBottom');
				$this->redrawControl('itemsList');
			} else {
				if (!$this->getSignal()) {
					$this->redrawControl();
				}
			}
		}

	}

	public function actionDetail(OfferLocalization $object): void
	{
		$this->processExternalRedirect($object);
		$this->setObject($object);
		$this->offerLocalization = $object;
	}


	public function renderDetail(): void
	{
//		$this->offerLocalizationModel->increaseViews($this->offerLocalization);

		$this->template->offerLocalization = $this->offerLocalization;
//		$this->template->tagsWithCount = $this->offerTagModel->getTagsWithCount($this->mutation);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}





//	protected function createComponentAttachedOffers(): AttachedOffers
//	{
//		return $this->attachedOffersFactory->create($this->offerLocalization->offer);
//	}

//	protected function createComponentOfferLocalizationStructuredData(): OfferLocalizationStructuredData
//	{
//		return $this->offerLocalizationStructuredDataFactory->create($this->offerLocalization, $this->mutation);
//	}

}
