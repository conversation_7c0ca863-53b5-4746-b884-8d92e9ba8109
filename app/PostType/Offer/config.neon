parameters:
	config:
		offer:
			paging: 12 #temp low number - to testing

	postTypeRoutes:
		Offer: offer

cf:
	templates:
		offerLocalization:
			base:
				extends: @cf.base
			settings:
				type: group
				label: "Parametry"
				items:
					contact_person:
						type: text
						label: "Kontaktní osoba"
					link:
						type: text
						label: "Externí URL (bez přesměrování)"
					external_link:
						type: text
						label: "Externí URL"


cc:
	templates:
		# ":Offer:Front:Offer:detail": []

application:
	mapping:
		Offer: App\PostType\Offer\*Module\Presenters\*Presenter

services:
	- App\PostType\Offer\Model\Orm\OfferLocalizationModel
	- App\PostType\Offer\AdminModule\Components\Form\OfferFormPrescription(coreFormPath: %appDir%/PostType/Core/AdminModule/Components/Form)
	- App\PostType\Offer\Model\OfferLocalizationFacade
	- App\PostType\Offer\AdminModule\Components\DataGrid\OfferDataGridPrescription
	- App\PostType\Offer\FrontModule\Components\OfferLocalizationStructuredData\OfferLocalizationStructuredDataFactory

	-
		implement: App\PostType\Offer\FrontModule\Components\Attached\AttachedOffersFactory
		inject: true
