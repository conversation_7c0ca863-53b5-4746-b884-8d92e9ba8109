<?php declare(strict_types = 1);

namespace App\PostType\CalendarTag\Model;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Calendar\Model\Orm\CalendarLocalizationRepository;
use App\PostType\Calendar\Model\Orm\CalendarRepository;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalizationRepository;
use stdClass;

class CalendarTagModel
{

	public function __construct(
		private CalendarTagLocalizationRepository $calendarTagLocalizationRepository,
		private CalendarLocalizationRepository $calendarLocalizationRepository,
		private CalendarRepository $calendarRepository,
	)
	{
	}

	public function getTagsWithCount(Mutation $mutation): array
	{
		$calendarTagLocalizations = $this->calendarTagLocalizationRepository->findBy([
			'mutation' => $mutation
		]);
		$tagsWithCount = [];

		foreach ($calendarTagLocalizations as $calendarTagLocalization) {
			assert($calendarTagLocalization instanceof CalendarTagLocalization);
			$tagData = new stdClass();
			$tagData->tag = $calendarTagLocalization;

			$calendarIds = $this->calendarRepository->findBy(['tags->id' => $calendarTagLocalization->calendarTag->id])->fetchPairs(null, 'id');

			$calendarTagLocalizations = $this->calendarLocalizationRepository->findBy([
				'mutation' => $calendarTagLocalization->getMutation(),
				'calendar' => $calendarIds,
			]);

			$tagData->count = $calendarTagLocalizations->count();

			$tagsWithCount[] = $tagData;
		}

		usort($tagsWithCount, fn($a, $b) => $a->count <=> $b->count);
		return $tagsWithCount;
	}

}
