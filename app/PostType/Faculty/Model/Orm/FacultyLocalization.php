<?php declare(strict_types = 1);

namespace App\PostType\Faculty\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Routable;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\HasPublishable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use App\Model\Orm\JsonContainer;

/**
 * @property int $id {primary}
 * @property string $name  {default ''}
 * @property bool $public {default false}
 * @property DateTimeImmutable|null $publicFrom {default now}
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 *
 * @property ArrayHash $customContentJson {container JsonContainer}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 * @property Faculty $faculty {M:1 Faculty::$localizations}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read string $template {virtual}
 * @property-read LibraryImage|null $firstImage {virtual}
 */
class FacultyLocalization extends BaseEntity implements LocalizationEntity, Publishable, Validatable, Editable
{
	use HasCustomFields;
	use HasCustomContent;
	use HasPublishable;

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): Faculty
	{
		return $this->faculty;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Faculty);
		$this->faculty = $parentEntity;
	}

	public function getPublicFrom(): DateTimeImmutable|null
	{
		return $this->publicFrom;
	}

	public function getPublicTo(): DateTimeImmutable|null
	{
		return $this->publicTo;
	}

	public function setPublicFrom(?DateTimeImmutable $publicFrom): void
	{
		$this->publicFrom = $publicFrom;
	}

	public function setPublicTo(?DateTimeImmutable $publicTo): void
	{
		$this->publicTo = $publicTo;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}

	protected function getterFirstImage(): ?LibraryImage
	{
		if (!isset($this->cache['firstImage'])) {
			$this->cache['firstImage'] = isset($this->getParent()->cf->faculty_base->image) ? $this->getParent()->cf->faculty_base->image->getEntity() : null;
		}

		return $this->cache['firstImage'];
	}

	public function getAnnotation(): ?string
	{
		return isset($this->cf->faculty_localization_base->annotation) ? $this->cf->faculty_localization_base->annotation : '';
	}


	public function getRoutable(): ?Routable
	{
		if (!isset($this->cache['routable'])) {
			$this->cache['routable'] = isset($this->cf->faculty_localization_base->link->page) ? $this->cf->faculty_localization_base->link->page->getEntity() : null;
		}
		return $this->cache['routable'];
	}

	public function getLink(): string
	{
		if ( ($alias = $this->getRoutable()?->getAlias()) !== null) {
			return $alias;
		} elseif (isset($this->cf->faculty_localization_base->link->external) && $this->cf->faculty_localization_base->link->external !== '') {
			return $this->cf->faculty_localization_base->link->external;
		}

		return '';
	}

	public function getLinkName(): string
	{
		if (isset($this->cf->faculty_localization_base->link->text) && $this->cf->faculty_localization_base->link->text) {
			return $this->cf->faculty_localization_base->link->text;
		} elseif ($this->getRoutable() !== null) {
			return $this->getRoutable()->getNameAnchor();
		} elseif (isset($this->cf->faculty_localization_base->link->external) && $this->cf->faculty_localization_base->link->external !== '') {
			return $this->cf->faculty_localization_base->link->external;
		}

		return '';
	}
}
