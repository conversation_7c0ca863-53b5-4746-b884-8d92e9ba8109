<?php declare(strict_types = 1);

namespace App\PostType\Newsletter\FrontModule\Components\Attached;

use App\Model\TranslatorDB;
use App\PostType\Newsletter\Model\Orm\Newsletter;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

class AttachedNewsletters extends Control
{

	public function __construct(
		private Newsletter $object,
		private TranslatorDB $translator
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->object = $this->object;
		$this->template->newsletters = $this->getNewsletters();
		$this->template->templates = FE_TEMPLATE_DIR;

		$this->template->render(__DIR__ . '/attachedNewsletters.latte');
	}


	protected function getNewsletters(): ICollection
	{
//		TODO FIX @vojta

//		$withoutNewsletterIds = [$this->object->id];
//
//		$newsletters = $this->object->attachedNewsletters->toCollection()->findBy([])->limitBy($this->limit)->fetchPairs('id');
//		$withoutNewsletterIds = array_merge($withoutNewsletterIds, array_keys($newsletters));
//
//
//		if (count($newsletters) < $this->limit) { // ostatni clanky tagu
//			foreach ($this->object->newsletterTagsPublic as $tag) {
//				$newsletters += $tag->newslettersPublic->findBy(['id!=' => $withoutNewsletterIds])->fetchPairs('id');
//			}
//		}
//
//		if (count($newsletters) < $this->limit) { // ostatni clanky kategorii
//			foreach ($this->object->categoriesPublic as $category) {
//				$newsletters += $category->newslettersPublic->findBy(['id!=' => $withoutNewsletterIds])->fetchPairs('id');
//			}
//		}
//
//		$newsletters = array_slice($newsletters, 0, $this->limit, true);
//		return new ArrayIterator($newsletters);
		return new EmptyCollection();
	}

}
