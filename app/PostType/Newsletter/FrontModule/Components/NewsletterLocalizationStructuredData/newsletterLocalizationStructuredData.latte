{varType App\PostType\Newsletter\Model\Orm\NewsletterLocalization $newsletterLocalization}

{if isset($newsletterLocalization->firstImage)}
	{php $img = $newsletterLocalization->firstImage->getSize('md')}
	{php $stDataImage = $mutation->getBaseUrl().$img->src}
{else}
	{php $stDataImage = NULL}
{/if}


<script type="application/ld+json">
	{
		"@context": "http://schema.org/",
		"@type": "NewsArticle",
		"mainEntityOfPage": {$mutation->getBaseUrlWithPrefix()."/".$newsletterLocalization->alias},
		"headline": {$newsletterLocalization->name},
		"datePublished": {$newsletterLocalization->publicFrom->format('Y-m-d')},
		"dateModified": {if $newsletterLocalization->editedTime}{$newsletterLocalization->editedTime->format('Y-m-d')}{else}{$newsletterLocalization->createdTimeOrder->format('Y-m-d')}{/if},
		"description": {$newsletterLocalization->annotation},
		{if $stDataImage}
			"image": {
				"@type": "ImageObject",
				"height": {$stDataImageW},
				"width": {$stDataImageH},
				"url": {$stDataImage}
			},
		{/if}
		{if $author !== null}
			"author": {
				"@type": "Person",
				"name": {$author->name}
			},
		{/if}
		"publisher": {
			"@type": "Organization",
			"logo": {
				"@type": "ImageObject",
				"url": {$mutation->getBaseUrl()."/static/img/logo.png"}
			},
			"name": {$publisher}
		},
		"articleBody": {$content}
	}
	</script>
