<?php

declare(strict_types=1);

namespace App\FrontModule\Components\GoogleLogin;

use App\Model\Google\GoogleProviderFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Security\Acl;
use App\Model\TranslatorDB;
use League\OAuth2\Client\Provider\Google;
use League\OAuth2\Client\Provider\GoogleUser;
use League\OAuth2\Client\Token\AccessToken;
use Nette\Application\Attributes\CrossOrigin;
use Nette\Application\UI\Control;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Security\SimpleIdentity;
use function assert;

/**
 * @method void onLogin()
 * @method void onEmailTaken()
 * @method void onError()
 */
final class GoogleLogin extends Control
{

	public array $onLogin = [];

	public array $onEmailTaken = [];

	public array $onError = [];

	private readonly SessionSection $session;

	public function __construct(
		private readonly bool $isEnabled,
		private readonly Mutation $mutation,
		private readonly TranslatorDB $translator,
		private readonly GoogleProviderFactory $googleProviderFactory,
		private readonly Orm $orm,
		private readonly UserModel $userModel,
		Session $session,
	)
	{
		$this->session = $session->getSection(Google::class);
	}

	public function handleLogin(): void
	{
		if ( ! $this->isEnabled) {
			return;
		}

		$google = $this->googleProviderFactory->create($this->mutation);

		$authorizationUrl = $google->getAuthorizationUrl([
			'redirect_uri' => $this->link('//response!'),
		]);

		$this->session->state = $google->getState();
		$this->presenter->redirectUrl($authorizationUrl);
	}

	#[CrossOrigin]
	public function handleResponse(): void
	{
		$error = $this->presenter->getParameter('error');
		if ($error !== null) {
			$this->onError();
			return;
		}

		$state = $this->presenter->getParameter('state');
		if ($state === null || $this->session->state === null || ! hash_equals($this->session->state, $state)) {
			$this->onError();
			return;
		}

		unset($this->session->state);

		$google = $this->googleProviderFactory->create($this->mutation);

		$accessToken = $google->getAccessToken('authorization_code', [
			'code' => $this->presenter->getParameter('code'),
			'redirect_uri' => $this->link('//response!'),
		]);

		assert($accessToken instanceof AccessToken);

		try {
			$googleUser = $google->getResourceOwner($accessToken);
			assert($googleUser instanceof GoogleUser);
		} catch (\Throwable) {
			$this->onError();
			return;
		}

		$googleId = $googleUser->getId();
		$userById = $this->orm->user->getBy([
			'googleId' => $googleId,
			'mutations->id' => $this->mutation->id,
		]);

		if ($userById !== null) {
			$this->presenter->getUser()->login(new SimpleIdentity($userById->id, $userById->role));

			$this->onLogin();
			return;
		}

		$googleEmail = $googleUser->getEmail();
		$userByEmail = $this->orm->user->getByEmail($googleEmail, $this->mutation);

		// nobody matches google ID, but we're logged in -> connect account
		if ($this->presenter->user->loggedIn) {
			// but not if google email matches a different user!
			$currentUser = $this->orm->user->getByIdChecked($this->presenter->getUser()->identity->getId());
			if ($userByEmail !== null && $userByEmail->id !== $currentUser->id) {
				$this->onEmailTaken();
				return;
			}

			$currentUser->googleId = $googleId;
			$this->orm->user->persistAndFlush($currentUser);

			$this->onLogin();
			return;
		}

		// google email is registered, instruct the user to login and connect account
		if ($userByEmail !== null) {
			$this->onEmailTaken();
			return;
		}

		// register new user and sign them in
		$user = new User();
		$this->orm->user->attach($user);
		$user->role = Acl::ROLE_USER;
		$user->mutations->add($this->mutation);

		$user = $this->userModel->save($user, [
			'email' => $googleEmail,
			'googleId' => $googleId,
		]);

		$this->presenter->getUser()->login(new SimpleIdentity($user->id, $user->role));
		$this->onLogin();
	}

	public function render(): void
	{
		if ( ! $this->isEnabled) {
			return;
		}

		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/GoogleLogin.latte');
	}

}
