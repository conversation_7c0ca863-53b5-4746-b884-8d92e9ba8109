<?php declare(strict_types = 1);

namespace App\FrontModule\Components\SignInForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\HasAntispamInput;
use App\Model\AdminEmails;
use App\Model\Form\FormThrottler;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Http\Response;
use Nette\Security\AuthenticationException;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property-read DefaultTemplate $template
 */
final class SignInForm extends UI\Control
{
	use HasAntispamInput;

	public const COOKIE_NAME_LOGIN_EMAIL_AUTH_ERROR = 'loginEmailAuthError';

	public function __construct(

		private readonly Mutation $mutation,
		private readonly object $object,
		private readonly TranslatorDB $translator,
		private readonly Response $httpResponse,
		private readonly Orm $orm,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly AdminEmails $adminEmails,
		private readonly FormThrottler $formThrottler,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->render(__DIR__ . '/signInForm.latte');
	}


	/**
	 * Sign-in form factory.
	 *
	 * @return UI\Form
	 * @throws UI\InvalidLinkException
	 */
	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$this->attachAntispamTo($form);

		$form->addEmail('username', 'form_label_email')
			->setRequired('form_enter_username');

		$params = [];
		if (isset($this->presenter->params['p'])) {
			$form->addHidden('p', $this->presenter->params['p']);
			$params['p'] = $this->presenter->params['p'];
		}

		$form->addPassword('password', 'form_label_password')
			->setRequired('form_enter_password');

		$form->addCheckbox('remember', 'form_label_sign_remember');

		$form->addSubmit('send', 'Sign in');

		$url = $this->presenter->link($this->mutation->pages->userLogin, $params);
		$form->setAction($url);

		if (isset($_GET['email'])) {
			$form->setDefaults(['username' => $_GET['email']]);
		}

		$form->onValidate[] = function (UI\Form $form, ArrayHash $values) {
			if ( ! $this->adminEmails->isDeveloperEmail($values->username) && ! $values->password) {
				$form['password']->addError('form_enter_password');
			}
		};

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		$this->formThrottler->throttleFormSubmissions($form, formType: 'Front:SignIn', discriminatorName: 'username');

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		if ($this->adminEmails->isDeveloperEmail($values->username)) {
			if ($this->presenter->isAjax()) {
				$this->flashMessage($this->translator->translate('message_login_developer_use_admin'), 'error');
				$this->redrawControl();
				return;
			} else {
				$this->presenter->redirect(':Admin:Sign:default');
			}
		}

		if ($values->remember) {
			$this->presenter->getUser()->setExpiration('14 days', false);
		} else {
			$this->presenter->getUser()->setExpiration('20 minutes', true);
		}

		try {
			$this->presenter->getUser()->login($values->username, $values->password);

			// last login time
			$userEntity = $this->orm->user->getById($this->presenter->getUser()->id);

			$userEntity->lastLogin = new DateTimeImmutable();
			$this->orm->user->persistAndFlush($userEntity);

			if ($this->presenter->isAjax()) {
				$this->flashMessage($this->translator->translate('message_ok_login'), 'ok');
				$this->redrawControl();
			} else {
				$this->presenter->redirect('this');
			}
		} catch (AuthenticationException $e) {
			$form['password']->addError('message_bad_login');
			$form['username']->addError('message_bad_login');
			$this->httpResponse->setCookie(self::COOKIE_NAME_LOGIN_EMAIL_AUTH_ERROR, $values->username, 0);
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
