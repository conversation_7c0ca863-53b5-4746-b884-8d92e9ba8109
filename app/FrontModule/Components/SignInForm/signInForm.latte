{snippet form}
	<h1 class="b-login__title h2">{$object->name}</h1>
	<p n:if="$object->annotation ?? false">{$object->annotation|texy|noescape}</p>
	{form form class=>'f-login block-loader', data-naja=>'', novalidate=>'novalidate'}

		{control messageForForm, $flashes, $form}

		{include '../inp.latte', form=>$form, name=>username, labelInside: false, validate: false, hintLang: 'hint_login_username'}
		{include '../inp.latte', form=>$form, name=>password, labelInside: false, validate: false, hintLang: 'hint_login_password'}

		<p class="f-login__btn">
			<button type="submit" class="btn btn--block">
				<span class="btn__text">
					{_btn_login}
				</span>
			</button>
		</p>

		<p class="f-login__tool u-mb-last-0 u-mb-0">
			<a href="{plink $pages->lostPassword}" class="u-c-secondary">{_link_lost_password}</a>
			<a href="{plink $pages->registration}" class="u-c-secondary">{_link_registration}</a>
		</p>

		{*ANTISPAM*}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
		{/if}
		{*/ANTISPAM*}

		<div class="block-loader__loader"></div>

		{if count($flashes) > 0 && $flashes[0]->type == 'ok'}
			<script>
				location.reload();
			</script>
		{/if}
	{/form}
{/snippet}
