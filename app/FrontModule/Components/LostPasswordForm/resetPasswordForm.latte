{snippet formReset}
	<h1 class="b-login__title h2">{$object->name}</h1>
	<p n:if="$object->annotation ?? false">{$object->annotation|texy|noescape}</p>
	{form formReset class=>'f-login block-loader', novalidate=>'novalidate'}
		{control messageForForm, $flashes, $form}

		{include '../inp.latte', form=>$form, name=>password, labelInside: false, validate: true}
		{include '../inp.latte', form=>$form, name=>passwordVerify, labelInside: false, validate: true}

		<p class="f-login__btn">
			<button type="submit" class="btn btn--block">
				<span class="btn__text">
					{_btn_reset}
				</span>
			</button>
		</p>

		<p class="f-login__tool u-mb-last-0 u-mb-0">
			<a href="{plink $pages->userLogin}" class="u-c-secondary">{_btn_login}</a>
		</p>

		<div class="block-loader__loader"></div>
	{/form}
{/snippet}
