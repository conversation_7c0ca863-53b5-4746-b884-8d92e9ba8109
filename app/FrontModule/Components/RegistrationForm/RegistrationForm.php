<?php declare(strict_types=1);

/** @noinspection PhpRedundantCatchClauseInspection */

/** @noinspection PhpUnusedParameterInspection */

namespace App\FrontModule\Components\RegistrationForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\HasAntispamInput;
use App\Model\Email\CommonFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\NewsletterEmail\NewsletterEmailModel;
use App\Model\Orm\Orm;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Security\Acl;
use App\Model\TranslatorDB;
use App\PostType\Profile\FrontModule\Components\Form\ProfileFormPrescription;
use App\PostType\Profile\Model\Orm\Profile;
use App\PostType\Profile\Model\Orm\ProfileInvite;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\PostType\Profile\Model\Orm\ProfileModel;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\InvalidStateException;
use Nette\Security\AuthenticationException;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 */
final class RegistrationForm extends UI\Control
{
	use HasAntispamInput;

	public const string RENDER_MODE_MANUAL = 'manual';
	public const string RENDER_MODE_API = 'api';

	private ProfileLocalization $profileLocalization;
	private FormDefinition $registrationDefinition;
	private FormDefinition $verificationDefinition;

	private Mutation $mutation;

	private ?ProfileInvite $profileInvite;

	public string $renderMode;

	public function __construct(
		private readonly Tree $object,
		private readonly UserModel $userModel,
		private readonly ProfileModel $profileModel,
		private readonly CommonFactory $commonEmailFactory,
		private readonly NewsletterEmailModel $newsletterEmailModel,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		MutationHolder $mutationHolder,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly ProfileFormPrescription $profileFormPrescription,
		private readonly Builder $builder,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}

	public function getProfileLocalization(): ProfileLocalization
	{
		if (!isset($this->profileLocalization)) {
			$profile = new Profile();
			$this->profileLocalization = new ProfileLocalization();
			$this->profileLocalization->setMutation($this->mutation);
			$profile->localizations->add($this->profileLocalization);
		}

		return $this->profileLocalization;
	}

	private function getRegistrationDefinition(): FormDefinition
	{
		return $this->registrationDefinition ??= $this->profileFormPrescription->getRegistrationDefinition(
			$this->getProfileLocalization()
		);
	}

	private function getVerificationDefinition(): FormDefinition
	{
		return $this->verificationDefinition ??= $this->profileFormPrescription->getVerificationDefinition(
			$this->getProfileLocalization()
		);
	}

	private function getFormDefinition(): FormDefinition
	{
		if ($this->renderMode === self::RENDER_MODE_MANUAL) {
			$definition = $this->getRegistrationDefinition();
		} elseif ($this->renderMode === self::RENDER_MODE_API) {
			$definition = $this->getVerificationDefinition();
		} else {
			throw new InvalidStateException('$renderMode not set');
		}

		return $definition;
	}

	public function setProfileInvite(?ProfileInvite $profileInvite): self
	{
		$this->profileInvite = $profileInvite;
		return $this;
	}

	public function getProfileInvite(): ?ProfileInvite
	{
		return $this->profileInvite;
	}

	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);
		$form->setAction($this->presenter->link('this', ['tab' => $this->renderMode]));

		$this->attachAntispamTo($form);

		$this->builder->addExtenders($form, $this->getFormDefinition()->extenders);

		$form->addEmail('email', 'form_label_email')
			->setRequired('E-mail is required');
		$form->addPassword('password', 'form_label_password')
			->setRequired();
		$form->addPassword('passwordVerify', 'form_label_password2')
			->setRequired()
			->addRule(UI\Form::EQUAL, 'form_password_not_same', $form['password']);
		$form->addCheckbox('agree')->setRequired();
		$form->addCheckbox('isNewsletter', $this->translator->translate('form_label_newsletter'))->setTranslator(null)
			->setDefaultValue(1);

		$form->addSubmit('save', 'btnRegister');

		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onValidate[] = $this->formValidate(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formValidate(UI\Form $form, ArrayHash $values): void
	{
		$user = $this->userModel->getByEmail($values->email, $this->mutation);

		if ($user) {
			$link1 = $this->presenter->link($this->mutation->pages->userLogin, ['email' => $values->email]);
			$link2 = $this->presenter->link($this->mutation->pages->lostPassword, ['email' => $values->email]);
			$strTranslated = $this->translator->translate('mail_exist_register');

			if (strpos($strTranslated, '%link1%') !== false) {
				$strTranslated = str_replace('%link1%', $link1, $strTranslated);
			}

			if (strpos($strTranslated, '%link2%') !== false) {
				$strTranslated = str_replace('%link2%', $link2, $strTranslated);
			}

			$form->addError($strTranslated, false);
		}
	}

	private function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		foreach ($this->getFormDefinition()->extenders as $extender) {
			($extender->getSuccessHandlerFunction())($form, $values);
		}

		$profileLocalization = $this->getProfileLocalization();
		$profile = $profileLocalization->getParent();

		$user = new User();
		$this->orm->user->attach($user);
		$user->role = Acl::ROLE_USER;
		$user->mutations->add($this->mutation);

		$user = $this->userModel->save($user, [
			'email' => $values->email,
			'password' => $values->password,
		]);

		// Binds profile to user...
		$this->profileModel->update($profile);

		$profile->sections->contact->email = $values->email;

		$profileLocalization->syncNameFromSections();

		$user->profile = $profile;

		$this->userModel->activate(user: $user, publishProfile: true);
		$this->userModel->update($user);

		// Bind to not-used invite if it was set...
		if (isset($this->profileInvite) && $this->profileInvite->registered === null) {
			$this->profileInvite->registered = $profile;
			$this->orm->profileInvite->persistAndFlush($this->profileInvite);
		}

		try {
			$this->commonEmailFactory->create()
				->send('', $user->email, 'login', ['email' => $values->email]);

			if (!empty($values->isNewsletter)) {
				$this->newsletterEmailModel->subscribeUser($user, $this->mutation);
				$this->orm->flush();
			}

			$this->presenter->getUser()->login($values->email, $values->password);

			$this->flashMessage('form_register_ok', 'ok');
			$this->presenter->redirect($this->mutation->pages->userSection);

		} catch (AuthenticationException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	public function render(): void
	{
		if (!isset($this->renderMode)) {
			throw new InvalidStateException('$renderMode not set');
		}

		$this->template->renderMode = $this->renderMode;
		$this->template->setTranslator($this->translator);
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->render(__DIR__ . '/registrationForm.latte');
	}

}
