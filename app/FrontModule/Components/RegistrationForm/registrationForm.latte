{snippet form}
	{default $hideValidationMsgs = true}

	{form form autocomplete=>'off', class=>'f-login block-loader', novalidate=>"novalidate"}

		{control messageForForm, $flashes, $form}

		{switch $renderMode}
			{case manual}
				{formContainer name}
					{include '../inp.latte', form=>$form[name], name=>titlesBefore, labelInside: false}
					{include '../inp.latte', form=>$form[name], name=>firstname, labelInside: false}
					{include '../inp.latte', form=>$form[name], name=>lastname, labelInside: false}
					{include '../inp.latte', form=>$form[name], name=>titlesAfter, labelInside: false}
					{include '../inp.latte', form=>$form[name], name=>lastnameStudy, labelInside: false}
				{/formContainer}
				<hr>
				{formContainer educationsInfo}
					{include '../inp.latte', form=>$form[educationsInfo], name=>level, labelInside: false}
				{/formContainer}
				{formContainer educations-new}
					{include '../inp.latte', form=>$form['educations']['new'], name=>from, labelInside: false, required: true}
					{include '../inp.latte', form=>$form['educations']['new'], name=>to, labelInside: false, required: true}
					<div data-controller="faculty" data-faculty-choices-outlet=".inp-faculty, .inp-programme, .inp-branch">
						{include '../inp.latte', form=>$form['educations']['new'], name=>faculty, labelInside: false, facultyFilter: true, required: true}
						{include '../inp.latte', form=>$form['educations']['new'], name=>programme, labelInside: false, groupBy: faculty, facultyFilter: true, required: true}
						{include '../inp.latte', form=>$form['educations']['new'], name=>branch, labelInside: false, groupBy: programme, facultyFilter: true}
					</div>
				{/formContainer}
			{case api}
				{formContainer verification}
					{include '../inp.latte', form=>$form[verification], name=>verification, labelInside: false}
					{include '../inp.latte', form=>$form[verification], name=>lastnameCheck, labelInside: false}
				{/formContainer}
		{/switch}

		<hr>

		{include '../inp.latte', form=>$form, name=>email, labelInside: false}
		{include '../inp.latte', form=>$form, name=>password, labelInside: false}
		{include '../inp.latte', form=>$form, name=>passwordVerify, labelInside: false}

		{include '../inp.latte', form=>$form, name=>isNewsletter, labelInside: false}
		{include '../inp.latte', form=>$form, name=>agree, agreeLabel=>true, labelInside: false}

		<p class="f-login__btn u-mb-0">
			<button type="submit" class="btn btn--block">
				<span class="btn__text">
					{_btn_register}
				</span>
			</button>
		</p>

		{*ANTISPAM*}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
		{/if}
		{*/ANTISPAM*}

		<div class="block-loader__loader"></div>
	{/form}
{/snippet}
