<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer;

use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Routable;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\StaticPage\StaticPage;
use Nextras\Orm\Entity\IEntity;

interface CustomContentRendererFactory
{
	public function create(
		Routable|StaticPage $object,
		Mutation $mutation,
		PriceLevel $priceLevel,
		EsIndex $esIndex,
		?User $user,
		State $state,
	): CustomContentRenderer;
}
