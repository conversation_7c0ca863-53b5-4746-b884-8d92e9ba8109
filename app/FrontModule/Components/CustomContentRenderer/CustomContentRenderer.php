<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer;

use App\FrontModule\Components\NewsletterForm\NewsletterFormControl;
use App\FrontModule\Components\NewsletterForm\NewsletterFormControlFactory;
use App\FrontModule\Components\ProfileFollow\HasProfileFollow;
use App\FrontModule\Components\ProfileFollow\ProfileFollow;
use App\FrontModule\Presenters\BasePresenter;
use App\Infrastructure\Latte\Filters;
use App\Model\BucketFilter\BucketFilterFactory;
use App\Model\BucketFilter\Result;
use App\Model\BucketFilter\SetupCreator\BasicElasticItemListGenerator;
use App\Model\BucketFilter\SetupCreator\Blog\BasicElasticItemListFactory as BlogBasicElasticItemListFactory;
use App\Model\BucketFilter\SetupCreator\Calendar\BasicElasticItemListFactory as CalendarBasicElasticItemListFactory;
use App\Model\BucketFilter\SortCreator;
use App\Model\ConfigService;
use App\Model\ElasticSearch\Common\ElasticCommon;
use App\Model\ElasticSearch\Common\ResultReader;
use App\Model\Orm\BaseEntity;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Routable;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\Core\Model\Publishable;
use App\PostType\Profile\Model\Orm\Profile;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use Nette\Application\UI;
use Nette\Application\UI\Multiplier;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\DI\Attributes\Inject;
use Nextras\Orm\Entity\IEntity;
use App\Model\CustomContent\CustomContent;

/**
 * @property-read DefaultTemplate $template
 */
final class CustomContentRenderer extends UI\Control
{
	use HasProfileFollow;

	#[Inject]
	public NewsletterFormControlFactory $newsletterFormFactory;

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly Mutation $mutation,
		private readonly PriceLevel $priceLevel,
		private readonly EsIndex $esIndex,
		private readonly ?User $user,
		private readonly State $state,
		private readonly TranslatorDB $translator,
		private readonly ConfigService $configService,
		private readonly CustomContent $customContent,
		private readonly BucketFilterFactory $bucketFilterFactory,
		private readonly BlogBasicElasticItemListFactory $blogEsItemListFactory,
		private readonly CalendarBasicElasticItemListFactory $calendarEsItemListFactory,
		private readonly ResultReader $resultReader,
		private readonly SortCreator $sortCreator,
	)
	{
	}

	protected function createComponentNewsletterForms(): Multiplier
	{
		return new Multiplier(fn(): NewsletterFormControl => $this->newsletterFormFactory->create());
	}

	public function getObject(): Routable|StaticPage
	{
		return $this->object;
	}

	protected function getUserProfileLocalization(): ?ProfileLocalization
	{
		return $this->user?->profile?->getLocalization($this->mutation);
	}

	private function getBucketFilterItems(
		BasicElasticItemListGenerator $basicElasticItemGenerator,
		string $commonType,
		string $order,
		int $limit = 1000,
		int $offset = 0,
	): iterable
	{
		$bucketFilter = $this->bucketFilterFactory->create(
			basicElasticItemListGenerator: $basicElasticItemGenerator,
			elasticItemListGenerator: null,
			boxListGenerator: null,
			resultReader: $this->resultReader,
			esIndex: $this->esIndex,
			commonType: $commonType
		);

		// TODO - lepší řešení filtrovat v DB...
		$items = $bucketFilter->getItems(
			limit: $limit,
			offset: $offset,
			sort: $this->sortCreator->create($order)
		);
		return array_filter(
			iterator_to_array($items),
			fn(IEntity $item): bool => $item instanceof Publishable ? $item->isPublic() : true,
		);
	}

	public function render(array $props = []): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->templates = FE_TEMPLATE_DIR;
		$template->defaultTemplateDirectory = FE_TEMPLATE_DIR . '/part/customContent';
		$template->isDev = $this->configService->get('isDev');
		$template->props = $props;
		$template->object = $this->object;
		$template->mutation = $this->mutation;
		$template->pages = $this->mutation->pages;
		$template->priceLevel = $this->priceLevel;
		$template->state = $this->state;
		assert(method_exists($this->object, 'getCcModules'));
		if ($this->object->getCcModules()) {
			assert(isset($this->object->cc));
			$template->defaultObjectCC = $this->object->cc ?? [];
		} else {
			$template->defaultObjectCC = [];
		}

		$isDemo = (isset($this->object->parent->uid) && $this->object->parent->uid == "ccDemo") || (isset($this->object->uid) && $this->object->uid == 'ccDemo');
		$this->template->isDemo = $isDemo;
		if ($isDemo) {
			$this->template->allCustomComponentsLabels = $this->customContent->getAllCustomComponentsLabels((array) $this->object->getCcModules());
		} else {
			$this->template->allCustomComponentsLabels = [];

		}

		$template->addFilter('parseVideoId', Filters::parseVideoId(...));

		$template->userEntity = $this->user;
		$template->userProfile = $this->user?->profile;
		$template->userProfileLocalization = $this->getUserProfileLocalization();

		// TODO - move permission logic elsewhere ?
		$presenter = $this->getPresenter();
		assert($presenter instanceof BasePresenter);
		$template->addFilter('sectionVisible', fn(Profile $profile, string $section): bool => $presenter->isProfileSectionVisible($profile, $section));

		$template->blogEsItemListFactory = $this->blogEsItemListFactory;
		$template->calendarEsItemListFactory = $this->calendarEsItemListFactory;

		$template->addFilter('bucketFilterItems', $this->getBucketFilterItems(...));

		$template->render(__DIR__ . '/customContentRenderer.latte');
	}

}
