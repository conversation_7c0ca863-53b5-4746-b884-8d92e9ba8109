{* {snippet info}
	<p n:if="$userFound??->profile ?? false">
		<b><PERSON><PERSON><PERSON><PERSON> profil:</b> {include #profileLink profile: $userFound->profile}
	</p>
{/snippet} *}

{snippet form}
	{$control['form']|actionAddFormId}
	{form form class: 'block-loader u-mb-md u-mb-xl@md', data-naja: '', novalidate: "novalidate"}
		<h3 class="u-mb-sm">{_'invite_to_portal'}</h3>

		{control messageForForm, $flashes, $form}

		<p n:if="$userFound??->profile ?? false" class="message message--ok">
			<b>{_"found_profile"}</b> {include #profileLink profile: $userFound->profile}
		</p>

		<div class="grid grid--y-0 grid--x-sm">
			<div class="grid__cell size--6-12">
				{include '../inp.latte', form: $form, name: name, error: true}
			</div>
			<div class="grid__cell size--6-12">
				{include '../inp.latte', form: $form, name: email, error: true}
			</div>
		</div>

		<p class="u-mb-0">
			<button type="submit" class="btn">
				<span class="btn__text">
					{_"btn_send"}
				</span>
			</button>
		</p>

		<div class="block-loader__loader"></div>
	{/form}
{/snippet}

<div class="u-mb-last-0">
	<h3 class="u-mb-sm">{_'invite_to_portal_send'}</h3>
	{snippet invites}
		{if count($profile->invites)}
			<table>
				 <colgroup>
					<col style="width:13.5rem;">
					<col>
					<col>
					<col>
					<col>
				</colgroup>
				<thead>
					<tr>
						<th>{_'invite_name'}</th>
						<th>{_'invite_email'}</th>
						<th>{_'invite_sent'}</th>
						<th>{_'invite_visited'}</th>
						<th>{_'invite_registered'}</th>
					</tr>
				</thead>
				<tbody>
					<tr n:foreach="$profile->invites as $invite">
						<td>{$invite->name}</td>
						<td><a href="mailto:{$invite->email}">{$invite->email}</a></td>
						<td>{$invite?->sent|date:'d.m.Y H:i'}</td>
						<td>{$invite?->visited|date:'d.m.Y H:i'}</td>
						<td>
							{if $invite->registered !== null && $invite->registered->user !== null}
								{$invite->registered->user->createdTime|date:'d.m.Y H:i'}
								| {include #profileLink profile: $invite->registered}
							{/if}
						</td>
					</tr>
				</tbody>
			</table>
		{else}
			<p>
				{_"invited_persons_empty"}
			</p>
		{/if}
	{/snippet}
</div>

{define #profileLink}
	{var $profileLocalization = $profile->getLocalization($mutation)}
	<a n:tag-if="$profileLocalization->isPublic()" href="{plink $profileLocalization}">
		{$profileLocalization->getSectionsFullName()}
	</a>
{/define}
