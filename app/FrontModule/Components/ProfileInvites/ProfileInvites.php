<?php declare(strict_types=1);

namespace App\FrontModule\Components\ProfileInvites;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Form\CommonFormFactory;
use App\Model\Image\ImageObjectFactory;
use App\Model\Link\LinkFactory;
use App\Model\MenuService;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Routable;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserRepository;
use App\Model\Pages;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\Model\Email\CommonFactory as EmailCommonFactory;
use App\PostType\Profile\Model\Orm\ProfileInvite;
use App\PostType\Profile\Model\Orm\ProfileInviteRepository;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use Nette\Application\UI;
use Nette\Application\UI\Template;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use Nette\Forms\Form;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Tracy\Debugger;
use Throwable;

/**
 * @property-read DefaultTemplate $template
 */
final class ProfileInvites extends UI\Control
{
	private const string EMAIL_TEMPLATE_UID = 'invite';

	private ?User $userFound = null;

	private readonly Mutation $mutation;

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly ProfileLocalization $profileLocalization,
		private readonly CommonFormFactory $formFactory,
		private readonly EmailCommonFactory $emailCommonFactory,
		private readonly LinkFactory $linkFactory,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly TranslatorDB $translator,
		private readonly UserRepository $userRepository,
		private readonly ProfileInviteRepository $profileInviteRepository,
		MutationHolder $mutationHolder,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}


	protected function createComponentForm(): UI\Form
	{
		$form = $this->formFactory->create();
		$form->addText('name', 'form_label_invite_name')->setRequired();
		$form->addEmail('email', 'form_label_invite_email')->setRequired()
			->addRule(Form::Email);

		$form->addSubmit('save', 'btnSave');

		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	/**
	 * Send invite e-mail and store invite
	 *
	 * @param UI\Form $form
	 * @param ArrayHash $values
	 * @return void
	 */
	private function sendInvite(UI\Form $form, ArrayHash $values): void
	{
		$profile = $this->profileLocalization->getParent();

		$profileInvite = new ProfileInvite();
		$profileInvite->email = $values->email;
		$profileInvite->name = $values->name;
		$profile->invites->add($profileInvite);
		$this->profileInviteRepository->persistAndFlush($profileInvite);

		$inviteHashArgs = [
			'invite' => $profileInvite->hash,
		];

		try {
			$this->emailCommonFactory->create()->send(
				from: '',
				to: $values->email,
				dbTemplate: self::EMAIL_TEMPLATE_UID,
				data: [
					'NAME' => $values->name,
					'PROFILE-NAME' => $this->profileLocalization->getSectionsShortName(),
					'PROFILE-LINK' => $this->profileLocalization->isPublic() ?
						$this->linkFactory->linkTranslateToNette($this->profileLocalization, $inviteHashArgs) : '',
					'HP-LINK' => $this->linkFactory->linkTranslateToNette($this->mutation->pages->title, $inviteHashArgs),
					'REG-LINK' => $this->linkFactory->linkTranslateToNette($this->mutation->pages->registration, $inviteHashArgs),
				],
			);
		} catch (Throwable $error) {
			Debugger::log($error);
			$form->addError('Operation failed');
			return;
		}

		$profileInvite->sent = new DateTimeImmutable();
		$this->profileInviteRepository->persistAndFlush($profileInvite);
	}

	private function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$inviteMsg = 'invite_ok';

		// Search if there is already user with e-mail for invite...
		$this->userFound = $this->userRepository->getByEmail($values->email);
		if ($this->userFound === null) {
			// Also search already sent invites
			$profile = $this->profileLocalization->getParent();
			$profileInvite = $profile->invites->toCollection()
				->findBy(['email' => $values->email])
				->fetch();
			if ($profileInvite === null) {
				$this->sendInvite($form, $values);
			} else {
				$inviteMsg = 'invite_already_sent';
			}
		} else {
			$inviteMsg = 'invite_user_exists';
		}

		$this->flashMessage($inviteMsg, 'ok');

		if ($this->presenter->isAjax()) {
			$valuesArr = (array)$values;

			$form->setValues(array_fill_keys(array_keys($valuesArr), null));
			$this->redrawControl();
		} else {
			$this->presenter->redirect('this');
		}
	}


	private function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	protected function createTemplate(): Template
	{
		/** @var DefaultTemplate $template */
		$template = parent::createTemplate();
		$template->setTranslator($this->translator);

		$profile = $this->profileLocalization->getParent();

		$template->object = $this->object;
		$template->profile = $profile;
		$template->profileLocalization = $this->profileLocalization;
		$template->mutation = $this->mutation;
//		$template->profile = $this->profileLocalization->getParent();
		$template->templates = FE_TEMPLATE_DIR;

		$template->userFound = $this->userFound;

		return $template;
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	public function render(): void
	{
		$this->template->render(__DIR__ . '/profileInvites.latte');
	}
}
