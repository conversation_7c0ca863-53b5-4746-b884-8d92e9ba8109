<?php

declare(strict_types=1);

namespace App\FrontModule\Components\PodcastTipForm;

use App\Model\Email\CommonFactory;
use App\Model\Form\CommonFormFactory;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\Model\Orm\User\User;
use App\PostType\Profile\Model\Orm\Profile;
use App\PostType\Profile\Model\Orm\ProfileRepository;
use Nette\Application\UI\Form as UIForm;
use Nette\Forms\Form;
use Nette\Utils\ArrayHash;
use Throwable;
use Tracy\Debugger;

final readonly class PodcastTipFormFactory
{
	private const string EMAIL_TEMPLATE_UID = 'podcastTip';

	public function __construct(
		private readonly CommonFormFactory $formFactory,
		private readonly LinkFactory $linkFactory,
		private readonly CommonFactory $commonEmailFactory,
	)
	{
	}

	public function create(
		Routable $object,
		Mutation $mutation,
		?User $user = null,
		bool $standalone = false,
	): Form
	{
		$form = $this->formFactory->create($standalone ? Form::class : UIForm::class);

		$name = $user?->profile !== null ?
			$user->profile->getLocalization($mutation)->getSectionsShortName() : $user?->name;
		$form->addText('name', 'form_label_your_name')->setRequired()
			->setDefaultValue($name);
		$form->addText('url', 'form_label_tip_url')->setRequired();
		$form->addTextArea('tip', 'form_label_podcast_tip_note')->setRequired();

		$form->addSubmit('send');

		$form->onSuccess[] = function (Form $form, ArrayHash $values) use ($user, $mutation): void {
			try {
				$profile = $user?->profile;

				// Is authenticated user AND has socials profile...
				if ($user !== null && $profile !== null) {
					$nameLink = $this->linkFactory->linkTranslateToNette(
						$user->profile->getLocalization($mutation)
					);
				}

				$url = $values->url;
				if (!str_starts_with($url, 'https://') && !str_starts_with($url, 'http://')) {
					$url = 'https://' . $url;
				}

				$this->commonEmailFactory->create()->send(
					from: '',
					to: $mutation->getRealAdminEmail(),
					dbTemplate: self::EMAIL_TEMPLATE_UID,
					data: [
						'NAME' => $values->name,
						'NAME-LINK' => $nameLink ?? '',
						'URL' => $url,
						'TIP' => $values->tip,
					],
				);
			} catch (Throwable $error) {
				Debugger::log($error);
				$form->addError('Operation failed');
			}
		};

		return $form;
	}
}
