{default $snippetSuffix = ""}

{snippet form}
	{form form class: 'f-contact block-loader', data-naja: '', novalidate: "novalidate"}

		{control messageForForm, $flashes, $form}

		<div class="grid grid--y-0">
			<div class="grid__cell size--6-12">
				{include '../inp.latte', form: $form, name: name, error: true}
			</div>
			<div class="grid__cell size--6-12">
				{include '../inp.latte', form: $form, name: url, error: true}
			</div>
			<div class="grid__cell">
				{include '../inp.latte', form: $form, name: tip,  error: true, rows: 4}
			</div>
		</div>

		<p class="f-contact__btn u-mb-0">
			<button type="submit" class="btn">
				<span class="btn__text">
					{_"btn_send"}
				</span>
			</button>
		</p>

		{*ANTISPAM*}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
		{/if}
		{*/ANTISPAM*}

		<div class="block-loader__loader"></div>

	{/form}

{/snippet}
