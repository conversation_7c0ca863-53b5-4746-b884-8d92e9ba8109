{default $class = false}

<nav n:if="count($breadcrumbs) > 0" n:class="m-breadcrumb, header__breadcrumb, $class" data-controller="scrollable">
	<strong class="u-vhide">{_breadcrumb_title}</strong>
	<a href="#" class="m-breadcrumb__nav m-breadcrumb__nav--prev" data-scrollable-target="prev" data-action="scrollable#scrollLeft">{('angle-l')|icon, 'm-breadcrumb__nav-icon'}</a>
	<div class="m-breadcrumb__wrap" data-scrollable-target="wrap">
		<ol class="m-breadcrumb__list">
			{foreach $breadcrumbs as $key=>$i}
				<li class="m-breadcrumb__item">
					{if !$iterator->first}
						{('angle-r')|icon, 'm-breadcrumb__separator'}
					{/if}
					{if $iterator->last}
						<span class="m-breadcrumb__link" aria-current="page">{$i->nameAnchor}{if isset($_GET[search]) && $iterator->isLast() && $object->uid == 'search'}: {$_GET[search]}{/if}</span>
					{else}
						<a href="{plink $i, category: null}" class="m-breadcrumb__link">{$i->nameAnchor}</a>
					{/if}
				</li>
			{/foreach}
		</ol>
	</div>
	<a href="#" class="m-breadcrumb__nav m-breadcrumb__nav--next" data-scrollable-target="next" data-action="scrollable#scrollRight">{('angle-r')|icon, 'm-breadcrumb__nav-icon'}</a>
</nav>