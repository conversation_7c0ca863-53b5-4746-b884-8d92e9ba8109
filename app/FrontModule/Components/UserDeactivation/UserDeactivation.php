<?php

declare(strict_types=1);

namespace App\FrontModule\Components\UserDeactivation;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Email\CommonFactory as EmailCommonFactory;
use App\Model\Form\CommonFormFactory;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\Routable;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserHash\UserHashModel;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use Nette\Application\UI;
use Nette\Application\UI\Template;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\InvalidStateException;
use Nette\Utils\ArrayHash;
use Tracy\Debugger;
use Throwable;

/**
 * @property-read DefaultTemplate $template
 */
final class UserDeactivation extends UI\Control
{
	private const string EMAIL_TEMPLATE_USER_UID = 'deactivated';
	private const string EMAIL_TEMPLATE_ADMIN_UID = 'deactivatedForAdmin';
	private const string PARAMETER_REASONS_UID = 'deactivateReasons';

	private readonly Mutation $mutation;

	private array $reasonsDictionary;

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly User $user,
		private readonly UserModel $userModel,
		private readonly UserHashModel $userHashModel,
		private readonly ParameterRepository $parameterRepository,
		private readonly CommonFormFactory $formFactory,
		private readonly EmailCommonFactory $emailCommonFactory,
		private readonly LinkFactory $linkFactory,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly TranslatorDB $translator,
		MutationHolder $mutationHolder,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}

	private function getProfileLocalization(): ProfileLocalization
	{
		return $this->user->profile->getLocalization($this->mutation);
	}

	private function getReasonsDictionary(): array
	{
		if (!isset($this->reasonsDictionary)) {
			/** @var Parameter|null $parameter */
			$parameter = $this->parameterRepository->findBy(['uid' => self::PARAMETER_REASONS_UID])->fetch();
			if ($parameter === null) {
				throw new InvalidStateException(
					sprintf('Parameter with UID "%s" is required, please create it', self::PARAMETER_REASONS_UID)
				);
			}

			$this->reasonsDictionary = $parameter->optionsValuePairs;
		}

		return $this->reasonsDictionary;
	}

	protected function createComponentForm(): UI\Form
	{
		$form = $this->formFactory->create();
		$form->addCheckboxList('reasons', 'form_label_deactivate_reasons', $this->getReasonsDictionary());
		$form->addTextArea('reasonOther', 'form_label_deactivate_reason_other');

		$form->addHidden('userId', $this->user->id);

		$form->addSubmit('save', 'btnSave');

		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	/**
	 * Send notification e-mails about de-activation to cancelled user and admin
	 *
	 * @param UI\Form $form
	 * @param ArrayHash $values
	 * @param string $userEmail
	 * @return bool
	 */
	private function sendNotifications(UI\Form $form, ArrayHash $values, string $userEmail): bool
	{
		$userHash = $this->userHashModel->generateHashForUser(
			userEntity: $this->user,
			type: UserHash::TYPE_REACTIVATION,
			data: [$userEmail],
			daysToinvalidate: 1000,
		);

		try {
			$this->emailCommonFactory->create()->send(
				from: '',
				to: $this->user->email,
				dbTemplate: self::EMAIL_TEMPLATE_USER_UID,
				data: [
					'REACTIVATE-LINK' => $this->linkFactory->linkTranslateToNette(
						$this->mutation->pages->reactivation, ['hashToken' => $userHash->hash],
					),
				],
			);

			$reasonsDictionary = $this->getReasonsDictionary();
			$reasons = [];
			foreach ($values->reasons as $reason) {
				$reasons[] = $reasonsDictionary[$reason] ?? null;
			}
			$reasons[] = $values->reasonOther;
			$reasons = array_filter($reasons);

			$this->emailCommonFactory->create()->send(
				from: '',
				to: $this->mutation->getRealAdminEmail(),
				dbTemplate: self::EMAIL_TEMPLATE_ADMIN_UID,
				data: [
					'NAME' => $this->getProfileLocalization()->getSectionsShortName(),
					'EMAIL' => $userEmail,
					'REASONS' => implode('<br>', $reasons),
					'ADMIN-USER' => $this->linkFactory->link(
						$this->mutation, 'Admin:User:edit', ['id' => $this->user->id]
					),
					'ADMIN-PROFILE' => $this->linkFactory->link(
						$this->mutation, 'Profile:Admin:Profile:edit', ['id' => $this->user->profile->id]
					),
				],
			);
		} catch (Throwable $error) {
			Debugger::log($error);
			$form->addError('Operation failed');
			return false;
		}

		return true;
	}

	private function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$userEmailOrig = $this->user->email;
		$notifySent = $this->sendNotifications($form, $values, $userEmailOrig);

		if ($notifySent) {
			$this->userModel->deactivate($this->user);
			$this->userModel->update($this->user);

			$this->flashMessage('ok', 'ok');

			$this->presenter->user->logout(true);
			$this->presenter->redirect($this->mutation->pages->title);
		}
	}


	private function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	protected function createTemplate(): Template
	{
		/** @var DefaultTemplate $template */
		$template = parent::createTemplate();
		$template->setTranslator($this->translator);

		$template->object = $this->object;

		$template->pages = $this->mutation->pages;

		return $template;
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}


	public function render(): void
	{
		$this->template->render(__DIR__ . '/userDeactivation.latte');
	}
}
