{varType App\Model\Pages $pages}

{snippet form}
	{form form class: 'f-deactivate block-loader', data-naja: '', novalidate: "novalidate", data-naja-force-redirect: ''}
		{control messageForForm, $flashes, $form}

		<div class="u-mb-last-0 u-mb-sm">
			{include '../inp.latte', form: $form, name: reasons, type: checkboxList, error: true}

			<p>
				<label class="inp-item inp-item--checkbox">
					<input type="checkbox" class="inp-item__inp" data-controller="toggle-class" data-action="toggle-class#toggleInp" data-toggle-class="u-js-hide" data-toggle-content=".f-deactivate__other">
					<span class="inp-item__text">
						{_"other"}
					</span>
				</label>
			</p>

			{include '../inp.latte', form: $form, name: reasonOther, class: 'f-deactivate__other u-js-hide', error: true}
		</div>

		<p class="grid grid--x-sm grid--y-sm">
			<span class="grid__cell size--auto">
				<button type="button" class="btn btn--secondary js-modal-close">
					<span class="btn__text">{_'user_keep_account'}</span>
				</button>
				{* <a href="{plink $pages->userSection}" class="btn btn--secondary" type="submit">
					<span class="btn__text">{_'user_keep_account'}</span>
				</a> *}
			</span>
			<span class="grid__cell size--auto">
				<button type="submit" class="btn">
					<span class="btn__text">
						{_"btn_confirm_deactivate"}
					</span>
				</button>
			</span>
		</p>
	{/form}
{/snippet}
