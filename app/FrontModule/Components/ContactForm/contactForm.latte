{default $snippetSuffix = ""}

{snippet form}
	{$control['form']|actionAddFormId}
	{form form class: 'f-contact block-loader u-mb-lg', data-naja: '', novalidate: "novalidate"}

		{control messageForForm, $flashes, $form}

		<div class="grid grid--y-0">
			<div class="grid__cell size--6-12">
				{include '../inp.latte', form: $form, name: name, validate: true, error: true}
			</div>
			<div class="grid__cell size--6-12">
				{include '../inp.latte', form: $form, name: surname, labelLang: 'form_label_surname', validate: true, error: true}
			</div>
			<div class="grid__cell size--6-12">
				{include '../inp.latte', form: $form, name: email, labelLang: 'form_label_email', validate: true, error: true}
			</div>
			<div class="grid__cell size--6-12">
				{include '../inp.latte', form: $form, name: phone, labelLang: 'form_label_phone', type: 'tel', validate: true, error: true}
			</div>
			<div class="grid__cell size--12-12">
				{include '../inp.latte', form: $form, name: text, labelLang: 'form_label_text', cols: 40, rows: 5, validate: true, error: true}
			</div>
		</div>

		{include '../inp.latte', form: $form, name: file, labelLang: 'form_label_file', type: "file"}
		{include '../inp.latte', form: $form, name: agree, agreeLabel: true, type: 'checkbox', validate: true}

		<p class="f-contact__btn u-mb-0">
			<button type="submit" class="btn">
				<span class="btn__text">
					{_"btn_send"}
				</span>
			</button>
		</p>

		{*ANTISPAM*}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
		{/if}
		{*/ANTISPAM*}

		<div class="block-loader__loader"></div>

	{/form}

{/snippet}
