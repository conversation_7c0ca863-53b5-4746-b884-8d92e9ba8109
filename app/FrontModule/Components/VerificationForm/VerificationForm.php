<?php declare(strict_types=1);

/** @noinspection PhpRedundantCatchClauseInspection */

/** @noinspection PhpUnusedParameterInspection */

namespace App\FrontModule\Components\VerificationForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Email\CommonFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Routable;
use App\Model\StaticPage\StaticPage;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\Model\TranslatorDB;
use App\PostType\Profile\FrontModule\Components\Form\ProfileFormPrescription;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\PostType\Profile\Model\Orm\ProfileModel;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 */
final class VerificationForm extends UI\Control
{
	private FormDefinition $formDefinition;

	private Mutation $mutation;

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly ProfileLocalization $profileLocalization,
		private readonly ProfileModel $profileModel,
//		private readonly CommonFactory $commonEmailFactory,
		private readonly TranslatorDB $translator,
		MutationHolder $mutationHolder,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly ProfileFormPrescription $profileFormPrescription,
		private readonly Builder $builder,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}

	private function getFormDefinition(): FormDefinition
	{
		return $this->formDefinition ??= $this->profileFormPrescription->getVerificationDefinition(
			$this->profileLocalization
		);
	}

	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$this->builder->addExtenders($form, $this->getFormDefinition()->extenders);

		$form->addSubmit('save', 'btnVerify');

		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	private function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		foreach ($this->getFormDefinition()->extenders as $extender) {
			($extender->getSuccessHandlerFunction())($form, $values);
		}

		$this->profileModel->update($this->profileLocalization->getParent());

		$this->presenter->flashMessage('verify_ok');
		$this->presenter->redirect($this->mutation->pages->userSection);
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	public function render(): void
	{
		$this->template->form = $this->getComponent('form');
		$this->template->setTranslator($this->translator);
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->render(__DIR__ . '/verificationForm.latte');
	}

}
