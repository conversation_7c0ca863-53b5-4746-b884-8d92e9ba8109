<?php declare(strict_types = 1);

namespace App\FrontModule\Components\CartUserDetail;

use App\Exceptions\LogicException;
use App\FrontModule\Components\Cart\Cart;
use App\FrontModule\Components\FormMessage\FormMessage;
use App\FrontModule\Components\FormMessage\FormMessageFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User as UserEntity;
use App\Model\Orm\User\UserModel;
use App\Model\Pages;
use App\Model\Security\Acl;
use App\Model\Security\User;
use App\Model\ShoppingCart\ShoppingCart;
use App\Model\ShoppingCart\ShoppingCartChangedException;
use App\Model\TranslatorDB;
use App\Utils\DateTime;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Forms\Control;
use Nette\Forms\Controls\SubmitButton;
use Nette\Forms\Form;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Throwable;
use Tracy\Debugger;
use Tracy\Dumper;


/**
 * @property-read DefaultTemplate $template
 * @property-read BasePresenter $presenter
 * @property-read UI\Control $parent
 */
final class CartUserDetail extends UI\Control
{

	public const CA_ADDRESS_REQUIRED_FIELDS = [
		'inv_firstname',
		'inv_lastname',
		'inv_street',
		'inv_city',
		'inv_zip',
		'inv_phone',
	];

	public const DELIVERY_ADDRESS_REQUIRED_FIELDS = [
		'del_firstname',
		'del_lastname',
		'del_street',
		'del_city',
		'del_zip',
	];

	private Pages $pages;

	private Mutation $mutation;

	private State $state;

	private SessionSection $session;

	private ?UserEntity $userEntity = null;

	private ?array $userAddresses = null;

	private array $cartChanges = [];

	public function __construct(
		private readonly TranslatorDB $translator,
		private readonly Session $sessionContainer,
		private readonly ShoppingCart $shoppingCart,
		private readonly Orm $orm,
		private readonly User $user,
		private readonly UserModel $userModel,
		private readonly FormMessageFactory $formMessageFactory,
	) {
		$this->onAnchor[] = $this->init(...);
		$this->onAnchor[] = $this->initUserAddress(...);
	}

	public function render(): void
	{
		$this->beforeRender();

		$this->template->render(__DIR__ . '/cartUserDetail.latte');
	}

	private function beforeRender(): void
	{
		$this->template->userEntity         = $this->userEntity;
		$this->template->userSession        = $this->session;
		$this->template->userStatus         = $this->session->get('status');
		$this->template->isEditEmailAllowed = $this->isEditEmailAllowed();
		$this->template->userAddresses      = $this->userAddresses;
	}

	protected function isEditEmailAllowed(): bool
	{
		return $this->session->get('status') === UserStatus::WithoutRegistration;
	}

	public function formEmailSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$user = $this->orm->user->getByEmail($values->email, $this->mutation);

		if ($user) {
			$this->session->set('status', UserStatus::Known);// = 'known';
			$this->session->set('id', $user->id);
			$this->session->set('email', $user->email);
		} else {
			$this->session->set('status', UserStatus::Unknown);// = 'known';
			$this->session->set('id', null);
			$this->session->set('email', $values->email);
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect('this');
		}

	}

	// ********************** email ********************************************

	public function formRegistrationValidate(UI\Form $form, ArrayHash $values): void
	{
		$user = $this->orm->user->getByEmail($values->email, $this->mutation);

		if ($user) {
			$form->addError('mail_exist_register');
		}

		$submitBy = $form->isSubmitted();
		assert($submitBy instanceof SubmitButton);

		if ($submitBy->getName() === 'save' && $values->password === null) {
			$form['password']->addError("form_enter_password");
			$form->addError('form_enter_password');
		}
	}

	public function formRegistrationSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = (array) $form->getHttpData();

		if (isset($valuesAll['save'])) { // $form->isSubmitted()->name == 'save'
			$user = new UserEntity();
			$this->orm->user->attach($user);
			$user->role       = Acl::ROLE_USER;
			$user->mutations->add($this->mutation);
			//$user->isActive = true; // pri obj verime a je rovnou aktivni
			//$user->activatedTime = new DateTimeImmutable();

			$user = $this->userModel->save($user, (array) $values);


			$this->session->set('status', UserStatus::Registered);
			$this->session->set('id', $user->id);
			$this->session->set('email', $user->email);

		} else {
			$this->session->set('status', UserStatus::WithoutRegistration);
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect('this');
		}

	}

	// ********************** registration ********************************************

	public function formValidate(UI\Form $form, ArrayHash $values): void
	{
		if ($this->shoppingCart->getTotalProducts() <= 0 || ! $this->shoppingCart->hasDelivery() || ! $this->shoppingCart->hasPayment()) {
			$this->presenter->redirect($this->pages->cart);
		}

		if ($this->cartChanges !== []) {
			foreach ($this->cartChanges as $flash) {
				$form->addError($flash->message);
			}
		}

		$valuesAll = (array) $form->getHttpData();

		// overeni, jeslti nedoslo ke snizeni poctu skladem
		assert($this->parent instanceof Cart);
		$this->parent->refreshCart();
		if ($this->parent->template->flashes !== []) {
			foreach ($this->parent->template->flashes as $flash) {
				$form->addError($flash->message);
			}
		}

		if ($this->shoppingCart->isEmpty()) {
			$this->parent->flashMessage('step2_error_supply_removed_all_items', 'error');
			//$this->presenter->redirect($this->pages->cart);
		}

		// sady adres pro prihlaseneho
		if ($this->user->isLoggedIn() && $this->userAddresses) {

			if ( ! isset($valuesAll['customAddress'])) { // nevybral zadnou
				$form->addError('form_error');
				$form['customAddress']->addError('form_error');
			}

		}
	}


	// ********************** address ********************************************

	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$originalValues            = clone $values;
		$originalValues->inv_state = $originalValues->del_state = $this->state->id;

		$orderValues    = clone $originalValues;
		$deliveryValues = clone $originalValues;

		try {
			// User
			$orderValues->email = $originalValues->email ?? $this->session->get('email');
			$orderValues->user  = $this->processUser();

			// Process, create and save adresses
			$addresses = $this->processDeliveryAddress((array) $form->getHttpData() + [
					'inv_state' => $originalValues->inv_state,
					'del_state' => $originalValues->del_state
				]);

			// Create order values
			$orderValues = $this->addAddressToOrderValue($orderValues, $addresses);
			// Create order_delivery_information values
			$deliveryValues = $this->addAddressToDeliveryInformationValue($deliveryValues, $addresses);
			// pred ulozenim kontrola ci sa nieco nezmenilo (sklady, ceny a etc.)
			$changed = $this->shoppingCart->placeOrder($orderValues, $deliveryValues);
			if ($changed !== []) {
				throw new ShoppingCartChangedException($changed);
			}

			// Load placed Order entity
			$order = $this->orm->order->getByIdChecked($this->shoppingCart->getOrderId());

			// create thank you page url
			$url = $this->presenter->link($this->pages->step3, ['orderId' => $order->id, 'orderHash' => $order->hash]);

			// remove session of cart user delivery
			$this->session->remove();

			// generate payment gateway link or etc
			$paymentUrl = $order->payment->information->redirectUrl();

			$this->presenter->redirectUrl($paymentUrl ?? $url);

		} catch (AbortException $e) {
			throw $e;
		} catch (ShoppingCartChangedException $e) {
			assert($this->parent instanceof Cart);
			foreach ($e->changed as $changedItem) {
				$this->flashMessage($this->parent->translate($changedItem->message->text, $this->parent->translateParams($changedItem->message->params)), $changedItem->message->type);
			}
		} catch (Throwable $e) {
			Debugger::log($e, Debugger::EXCEPTION);
			$this->flashMessage('Error', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}

	}

	public function handleChangeEmail(): void
	{
		$this->session->remove();
		if (!$this->presenter->isAjax()) {
			$this->redirect('this');
		}
		$this->redrawControl();
	}

	public function handleSetStatus(string $status): void
	{
		$this->session->set("status", UserStatus::from($status));
		if (!$this->presenter->isAjax()) {
			$this->redirect('this');
		}
		$this->redrawControl();
	}

	protected function processUser(): ?int
	{
		if ($this->user->isLoggedIn()) {
			$userId = $this->userEntity->id;
		} elseif ($this->session->get('status') === UserStatus::WithoutRegistration) {
			$userId = null;
		} else {
			$this->userEntity = $this->orm->user->getById($this->session->get('id'));
			$userId           = $this->userEntity->id;
		}

		return $userId;
	}

	/**
	 * Zpracuje sady adres
	 * @throws LogicException
	 */
	protected function processDeliveryAddress(array $valuesAll): array
	{
		$orderAddressSet = null;
		$newAddresses    = [];

		if ($this->user->isLoggedIn() && $this->userEntity->customAddress) {
			if ($this->userEntity->customAddress !== null) { // zde iteruju vsechny CA adresy, z form prijdou jen ty $this->userAddresses, ktere se needituji
				foreach ($this->userEntity->customAddress as $k => $i) { // ulozeni zmen jiz existujicich sad
					$i = (array) $i;

					if (isset($valuesAll['inv_firstname_' . $k])) {
						$newAddresses[] = $addressSet = self::getAddressLine($valuesAll, '_' . $k, $i,
							$this->shoppingCart->useDeliveryAddress());
					} else {
						$newAddresses[] = $addressSet = $i; // preskocena adresa ktera se nedituje
					}

					if (isset($valuesAll['customAddress']) && $valuesAll['customAddress'] == $k) { // user si zvolil danou sadu
						$orderAddressSet = $addressSet;
					}
				}
			}

			if ( ! empty($valuesAll['customAddress']) && $valuesAll['customAddress'] == 'XXX') { // nova sada u prihlaseneho
				$newAddresses[] = $orderAddressSet = self::getAddressLine($valuesAll, '', null,
					$this->shoppingCart->useDeliveryAddress());
			}
		} else {
			// neprihlaseny ma defaultni sadu vzdy
			$newAddresses[] = $orderAddressSet = self::getAddressLine($valuesAll, '', null,
				$this->shoppingCart->useDeliveryAddress());
		}

		if (empty($orderAddressSet)) {
			$newAddresses[] = $orderAddressSet = self::getAddressLine($valuesAll, '', null,
				$this->shoppingCart->useDeliveryAddress()); // fallback - situace, kdy

			Debugger::log('Unknown invoice address for order $orderAddressSet', 'order_step2_address_fallback');
			Debugger::log(Dumper::toText($valuesAll), 'order_step2_address_fallback');
			Debugger::log(Dumper::toText($orderAddressSet), 'order_step2_address_fallback');
		}


		if ($orderAddressSet['invState'] != $this->shoppingCart->getCountry()->id || (isset($orderAddressSet['delState']) && $orderAddressSet['delState'] != $this->shoppingCart->getCountry()->id)) {
			$stateAddressMsg = sprintf('State of address is different from selected state | basketState: %s -> invState: %s | delState: %s | useDeliveryAddressForm: %s',
				$this->shoppingCart->getCountry()->id,
				$orderAddressSet['invState'],
				$orderAddressSet['delState'] ?? 'not set',
				$this->shoppingCart->useDeliveryAddress() ? 'true' : 'false',
			);

			Debugger::log($stateAddressMsg, 'order_step2_address_different_state');
			Debugger::log(Dumper::toText($valuesAll), 'order_step2_address_different_state');
			Debugger::log(Dumper::toText($orderAddressSet), 'order_step2_address_different_state');

			throw new LogicException($stateAddressMsg);
		}


		// ulozim CA adresy, pokud se nejdena o obj bez registrace
		if ($this->userEntity) {
			$data = ['customAddress' => $newAddresses];

			if (isset($newAddresses[0])) {
				foreach ($newAddresses[0] as $key => $value) {
					if (str_starts_with($key, 'inv')) {
						$data[Strings::firstLower(str_replace('inv', '', $key))] = $value;
					}
				}
			}

			$this->userModel->save($this->userEntity, $data, $this->userEntity->id);
		}

		// dodaci adresu pobocky menim az po ulozeni CA usera
		/*if ($this->basket->isTransportPickupPointsPicker() && $place = $this->basket->getPlace()) {
			$values['dFirstname'] = $values['firstname'];
			$values['dLastname'] = $values['lastname'];
			$values['dCompany'] = sprintf('%s: %s', $this->translator->translate('delivery_place_type_' . $place->placeType->key) , $place->name);
			$values['dStreet'] = $place->street;
			$values['dCity'] = $place->city;
			$values['dZip'] = $place->zip;
			$values['dState'] = null;
			$values['dPhone'] = '';
		}*/

		return $orderAddressSet;
	}

	public static function getAddressLine(
		array $valuesAll,
		string $nameSuffix = '',
		?array $originAddress = null,
		bool $useDeliveryAddress = true
	): array {
		$newAddressLine = [];

		// fakturacni
		$newAddressLine['invFirstname'] = $valuesAll['inv_firstname' . $nameSuffix];
		$newAddressLine['invLastname']  = $valuesAll['inv_lastname' . $nameSuffix];
		$newAddressLine['invPhone']     = $valuesAll['inv_phone' . $nameSuffix];
		$newAddressLine['invStreet']    = $valuesAll['inv_street' . $nameSuffix];
		$newAddressLine['invCity']      = $valuesAll['inv_city' . $nameSuffix];
		$newAddressLine['invZip']       = $valuesAll['inv_zip' . $nameSuffix];
		$newAddressLine['invState']     = $originAddress ? (int) $originAddress['invState'] : (int) $valuesAll['inv_state' . $nameSuffix];

		if (isset($valuesAll['inv_countrycode' . $nameSuffix])) {
			$newAddressLine['invCountrycode'] = $valuesAll['inv_countrycode' . $nameSuffix];
		}

		// firemni
		$useCompanyAddress = ! empty($valuesAll['companyTab' . $nameSuffix]); // nechce firmu, pokud tam zustaly vyplnene hodnoty, nesmime je ulozit

		$newAddressLine['invCompany'] = $useCompanyAddress ? $valuesAll['inv_company' . $nameSuffix] : null;
		$newAddressLine['invIc']      = $useCompanyAddress ? $valuesAll['inv_ic' . $nameSuffix] : null;
		$newAddressLine['invDic']     = $useCompanyAddress ? $valuesAll['inv_dic' . $nameSuffix] : null;

		if ($useDeliveryAddress) {
			$useDeliveryAddress = ! empty($valuesAll['deliveryTab' . $nameSuffix]); // nechce dodaci, pokud tam zustaly vyplnene hodnoty, nesmime je ulozit
		}

		// dodaci
		$newAddressLine['delFirstname'] = $useDeliveryAddress ? $valuesAll['del_firstname' . $nameSuffix] : null;
		$newAddressLine['delLastname']  = $useDeliveryAddress ? $valuesAll['del_lastname' . $nameSuffix] : null;
		$newAddressLine['delCompany']   = $useDeliveryAddress ? $valuesAll['del_company' . $nameSuffix] : null;
		$newAddressLine['delPhone']     = $useDeliveryAddress ? $valuesAll['del_phone' . $nameSuffix] : null;
		$newAddressLine['delStreet']    = $useDeliveryAddress ? $valuesAll['del_street' . $nameSuffix] : null;
		$newAddressLine['delCity']      = $useDeliveryAddress ? $valuesAll['del_city' . $nameSuffix] : null;
		$newAddressLine['delZip']       = $useDeliveryAddress ? $valuesAll['del_zip' . $nameSuffix] : null;
		$newAddressLine['delState']     = $useDeliveryAddress ? ($originAddress ? (int) $originAddress['delState'] : (int) $valuesAll['del_state' . $nameSuffix]) : null;

		if ($useDeliveryAddress && isset($valuesAll['del_countrycode' . $nameSuffix])) {
			$newAddressLine['delCountrycode'] = $valuesAll['del_countrycode' . $nameSuffix];
		}

		return $newAddressLine;
	}

	protected function addAddressToOrderValue(ArrayHash $values, array $addressSet): ArrayHash
	{

		$values['name']      = trim($addressSet['invFirstname'] . ' ' . $addressSet['invLastname']);
		$values['street']    = $addressSet['invStreet'];
		$values['city']      = $addressSet['invCity'];
		$values['zip']       = $addressSet['invZip'];
		$values['country'] = $addressSet['invState'];
		$values['phone']     = $addressSet['invPhone'];

		$values['companyName']       = $addressSet['invCompany'];
		$values['companyIdentifier'] = $addressSet['invIc'];
		$values['vatNumber']         = $addressSet['invDic'];

		$values['note'] = $values['infotext'];

		return $this->cleanOrderValues($values);
	}

	private function cleanOrderValues(ArrayHash $values): ArrayHash
	{
		// a odeberu bordel
		foreach ($values as $k => $v) {
			if (
				str_starts_with($k, 'del_') ||
				str_starts_with($k, 'inv_') ||
				str_starts_with($k, 'companyTab') ||
				str_starts_with($k, 'deliveryTab') ||
				str_starts_with($k, 'customAddress')
			) {
				unset($values[$k]);
			}
		}
		unset($values['infotext']);

		unset($values['agrees']); // TODO: agrees

		return $values;
	}

	private function addAddressToDeliveryInformationValue(ArrayHash $values, array $addressSet): ArrayHash
	{
		$name                  = trim($addressSet['delFirstname'] . ' ' . $addressSet['delLastname']);
		$values['name']        = strlen($name) ? $name : $addressSet['invFirstname'] . ' ' . $addressSet['invLastname'];
		$values['company']     = $addressSet['delCompany'] ?? $addressSet['invCompany'];
		$values['street']      = $addressSet['delStreet'] ?? $addressSet['invStreet'];
		$values['city']        = $addressSet['delCity'] ?? $addressSet['invCity'];
		$values['zip']         = $addressSet['delZip'] ?? $addressSet['invZip'];
		$values['country']   = ! empty($addressSet['delCity']) ? $addressSet['delState'] : $addressSet['invState'];
		$values['phoneNumber'] = $addressSet['delPhone'] ?? $addressSet['invPhone'];

		return $this->cleanOrderValues($values);
	}

	public function formSignInValidate(UI\Form $form, ArrayHash $values): void
	{
		assert($form->isSubmitted() instanceof SubmitButton);

		if ($form->isSubmitted()->getName() === 'save' && $values->password === null) {
			$form['password']->addError("form_enter_password");
			$form->addError('form_enter_password');
		}
	}

	public function formSignInSucceeded(UI\Form $form, ArrayHash $values): void
	{
		assert($form->isSubmitted() instanceof SubmitButton);

		if ($form->isSubmitted()->getName() === 'save') { // $form->isSubmitted()->name == 'save'

			try {
				$this->presenter->getUser()->login($values->email, $values->password);

				// last login time
				$userEntity            = $this->orm->user->getById($this->presenter->getUser()->id);
				$userEntity->lastLogin = new DateTime();
				$this->orm->user->persistAndFlush($userEntity);

				$this->userEntity = $userEntity;

				$this->session->set('status', UserStatus::LoggedIn);
				$this->session->set('id', $userEntity->id);
				$this->session->set('email', $userEntity->email);

				$this->initUserAddress();
				$this->template->userAddresses      = $this->userAddresses;

			} catch (\Nette\Security\AuthenticationException $e) {
				$form->addError("message_bad_login");
			}

		} else {
			$this->session->set('status', UserStatus::WithoutSignIn);
		}

		if ($this->presenter->isAjax()) {
			$this->presenter->redrawControl();
		} elseif ( ! $form->hasErrors()) {
			$this->presenter->redirect('this');
		}
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	// ********************** login ********************************************

	public function createComponentFormMessage(): FormMessage
	{
		return $this->formMessageFactory->create();
	}

	protected function createComponentEmailForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$form->addEmail('email', 'form_label_email')->setRequired('form_enter_username');
		$form->addSubmit('save', 'btn_step2_email_confirm');

		//$form->onValidate[] = [$this, 'formValidate'];
		$form->onError[]   = [$this, 'formError'];
		$form->onSuccess[] = [$this, 'formEmailSucceeded'];

		return $form;
	}

	protected function createComponentRegistrationForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$form->addEmail('email', 'form_label_email')
		     ->setRequired('form_enter_username');
		$form->addPassword('password', 'form_label_password')->setNullable();

		if (($userEmail = $this->session->get('email')) !== null) {
			$form['email']->setDefaultValue($userEmail);
		}

		$form->addSubmit('save', 'btn_registration');
		$form->addSubmit('next', 'btn_step2_continue_without_registration');

		$form->onValidate[] = [$this, 'formRegistrationValidate'];
		$form->onError[]    = [$this, 'formError'];
		$form->onSuccess[]  = [$this, 'formRegistrationSucceeded'];

		return $form;
	}


	protected function createComponentAddressForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		if ($this->isEditEmailAllowed()) {

			$form->addEmail('email', 'form_label_email')
			     ->setRequired('form_enter_username')
			     ->setDefaultValue($this->session->get('email'));
		}

		// section checkboxes
		$form->addCheckbox('companyTab');
		$form->addCheckbox('deliveryTab');

		// fakturacni
		$form->addText('inv_firstname', 'form_label_firstname');
		$form->addText('inv_lastname', 'form_label_lastname');
		$form->addText('inv_phone', 'form_label_phone')->setHtmlAttribute('data-baseCountry', $this->state->code);
		$form->addText('inv_street', 'form_label_street');
		$form->addText('inv_city', 'form_label_city');
		$form->addText('inv_zip', 'form_label_zip');


		// firemni
		$form->addText('inv_company', 'form_label_company')->setNullable();
		$form->addText('inv_ic', 'form_label_ic')->setNullable();
		$form->addText('inv_dic', 'form_label_dic')->setNullable();

		// dodaci
		if ($this->shoppingCart->useDeliveryAddress()) {
			$form->addText('del_firstname', 'form_label_firstname');
			$form->addText('del_lastname', 'form_label_lastname');
			$form->addText('del_phone', 'form_label_phone')->setHtmlAttribute('data-baseCountry', $this->state->code);
			$form->addText('del_street', 'form_label_street');
			$form->addText('del_city', 'form_label_city');
			$form->addText('del_zip', 'form_label_zip');
			$form->addText('del_company', 'form_label_company');

		}

		$form->addTextArea('infotext', 'note');
		$this->addAgrees($form);

		if ($this->userEntity) {
			$caKeys        = $this->userEntity->customAddress ? array_keys($this->userEntity->customAddress) : [];
			$caKeys['XXX'] = '';
			$form->addRadioList('customAddress', null, $caKeys)->setDefaultValue(array_key_first($caKeys));

			if ($this->userAddresses) {
				/** @var Control $customAddress */
				$customAddress = $form['customAddress'];

				foreach ($this->userAddresses as $k => $i) {

					// fakturacni
					$form->addText('inv_firstname_' . $k, 'form_label_firstname')->setDefaultValue($i->invFirstname)
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->setRequired();

					$form->addText('inv_lastname_' . $k, 'form_label_lastname')->setDefaultValue($i->invLastname)
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->setRequired();

					$form->addText('inv_phone_' . $k,
						'form_label_phone')->setDefaultValue($i->invPhone)->setHtmlAttribute('data-baseCountry',
						$this->state->code)
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->setRequired();

					$form->addText('inv_street_' . $k, 'form_label_street')->setDefaultValue($i->invStreet)
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->setRequired();

					$form->addText('inv_city_' . $k, 'form_label_city')->setDefaultValue($i->invCity)
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->setRequired();

					$form->addText('inv_zip_' . $k, 'form_label_zip')->setDefaultValue($i->invZip)
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->setRequired();

					// firma
					$form->addCheckbox('companyTab_' . $k);
					if ($i->invCompany) { // ma firmu = musim rozbalit checkbox
						$form['companyTab_' . $k]->setDefaultValue(true);
					}
					/** @var Control $companyTabControl */
					$companyTabControl = $form['companyTab_' . $k];

					$form->addText('inv_company_' . $k,
						'form_label_company')->setDefaultValue($i->invCompany ?? null)->setNullable()
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->addConditionOn($companyTabControl, Form::FILLED)
					     ->setRequired();

					$form->addText('inv_ic_' . $k, 'form_label_ic')->setDefaultValue($i->invIc ?? null)->setNullable()
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->addConditionOn($companyTabControl, Form::FILLED)
					     ->setRequired();

					$form->addText('inv_dic_' . $k,
						'form_label_dic')->setDefaultValue($i->invDic ?? null)->setNullable();

					// dodaci
					$form->addCheckbox('deliveryTab_' . $k);
					if ( ! empty($i->delCity)) { // ma dodaci = musim rozbalit checkbox
						$form['deliveryTab_' . $k]->setDefaultValue(true);
					}
					/** @var Control $deliveryTabControl */
					$deliveryTabControl = $form['deliveryTab_' . $k];

					$form->addText('del_firstname_' . $k,
						'form_label_firstname')->setDefaultValue($i->delFirstname ?? '')
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->addConditionOn($deliveryTabControl, Form::FILLED)
					     ->setRequired();

					$form->addText('del_lastname_' . $k, 'form_label_lastname')->setDefaultValue($i->delLastname ?? '')
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->addConditionOn($deliveryTabControl, Form::FILLED)
					     ->setRequired();

					$form->addText('del_phone_' . $k,
						'form_label_phone')->setDefaultValue($i->delPhone ?? '')->setHtmlAttribute('data-baseCountry',
						$this->state->code);

					$form->addText('del_street_' . $k, 'form_label_street')->setDefaultValue($i->delStreet ?? '')
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->addConditionOn($deliveryTabControl, Form::FILLED)
					     ->setRequired();

					$form->addText('del_city_' . $k, 'form_label_city')->setDefaultValue($i->delCity ?? '')
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->addConditionOn($deliveryTabControl, Form::FILLED)
					     ->setRequired();

					$form->addText('del_zip_' . $k, 'form_label_zip')
					     ->setDefaultValue($i->delZip ?? '')
					     ->addConditionOn($customAddress, Form::EQUAL, $k)
					     ->addConditionOn($deliveryTabControl, Form::FILLED)
					     ->setRequired();

					$form->addText('del_company_' . $k, 'form_label_company')->setDefaultValue($i->delCompany ?? '');
				}

				// rules
				foreach (self::CA_ADDRESS_REQUIRED_FIELDS as $key) {
					$form[$key]->addConditionOn($customAddress, Form::EQUAL, 'XXX')
					           ->setRequired();
				}


				if ($this->shoppingCart->useDeliveryAddress()) {
					foreach (self::DELIVERY_ADDRESS_REQUIRED_FIELDS as $key) {
						$form[$key]->addConditionOn($customAddress, Form::EQUAL, 'XXX')
						           ->addConditionOn($form['deliveryTab'], Form::FILLED)
						           ->setRequired();
					}
				}

				foreach (['inv_company', 'inv_ic'] as $key) {
					$form[$key]->addConditionOn($customAddress, Form::EQUAL, 'XXX')
					           ->addConditionOn($form['companyTab'], Form::FILLED)
					           ->setRequired();
				}
			}
		}

		if ( ! $this->userEntity || ! $this->userAddresses) { // validace pro samostatnou sadu u neprihlaseneho nebo kdyz logged user jeste nema ani jednu sadu
			// rules
			// nelze hned na definici inputu, protoze to u logged usera muza byt podmineno XXX radio "vlozit nove udaje"
			foreach (self::CA_ADDRESS_REQUIRED_FIELDS as $key) {
				$form[$key]->setRequired();
			}

			if ($this->shoppingCart->useDeliveryAddress()) {
				foreach (self::DELIVERY_ADDRESS_REQUIRED_FIELDS as $key) {
					$form[$key]->addConditionOn($form['deliveryTab'], Form::FILLED)->setRequired();
				}
			}

			foreach (['inv_company', 'inv_ic'] as $key) {
				$form[$key]->addConditionOn($form['companyTab'], Form::FILLED)->setRequired();
			}
		}

		$form->addSubmit('next', 'Next');

		$form->onValidate[] = [$this, 'formValidate'];
		$form->onError[]    = [$this, 'formError'];
		$form->onSuccess[]  = [$this, 'formSucceeded'];

		return $form;
	}

	/**
	 * Souhlasy podle typu produktu
	 */
	protected function addAgrees(UI\Form $form): void
	{
		$agrees = [];

		$agrees[] = ['key' => 'terms', 'name' => 'agree_with_terms', 'required' => true];

		$container = $form->addContainer('agrees');

		foreach ($agrees as $agree) {
			$checkbox = $container->addCheckbox($agree['key'], $agree['name']);
			$checkbox->setTranslator(null);
			// @phpstan-ignore-next-line
			if (isset($agree['required']) && $agree['required']) {
				$checkbox->setRequired('msg_check_all_agrees');
			}
		}
	}

	protected function createComponentSignInForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$form->addEmail('email', 'form_label_email')
		     ->setRequired('form_enter_username')
		     ->setDefaultValue($this->session->get('email'));
		$form->addPassword('password', 'form_label_password')->setNullable();

		$form->addSubmit('save', 'btn_login');
		$form->addSubmit('next', 'btn_step2_continue_without_signin');

		$form->onValidate[] = [$this, 'formSignInValidate'];
		$form->onError[]    = [$this, 'formError'];
		$form->onSuccess[]  = [$this, 'formSignInSucceeded'];

		return $form;
	}

	private function init(): void
	{
		assert($this->parent instanceof Cart);

		$this->template->setTranslator($this->translator);
		$this->template->setParameters($this->parent->getTemplateParameters());


		$this->parent->refreshCart();
		$this->cartChanges = $this->parent->template->flashes;
		$this->template->flashes = array_merge($this->template->flashes, $this->parent->template->flashes);

		$this->session = $this->sessionContainer->getSection(self::class);

		if ($this->user->isLoggedIn()) {
			$this->userEntity = $this->orm->user->getById($this->user->getId());
			$this->session->set('status', UserStatus::LoggedIn);
			$this->session->set('id', $this->userEntity->id);
			$this->session->set('email', $this->userEntity->email);
		} elseif ( ! empty($this->session->get('status') === UserStatus::LoggedIn)) { // fallback pokud se v prubehu obj kroku odhlasi
			$this->session->remove();
		}


		$this->pages           = $this->template->pages;
		$this->mutation        = $this->template->mutation;
		$this->state           = $this->shoppingCart->getCountry();
		$this->template->state = $this->state;
		//$this->priceLevel = $this->template->priceLevel;

		//$this->session->remove();
	}

	private function initUserAddress(): void
	{
		if ($this->userEntity !== null) {
			$list      = $this->userEntity->customAddress ?? [];
			$countryId = $this->state->id;

			foreach ($list as $k => $address) {
				if ($address->invState !== $countryId || ( ! empty($address->delState) && $address->delState !== $countryId)) {
					unset($list[$k]);
				}
			}
			$this->userAddresses = $list;
		}
	}
}
