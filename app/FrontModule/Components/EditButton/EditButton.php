<?php declare(strict_types = 1);

namespace App\FrontModule\Components\EditButton;

use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\Routable;
use App\PostType\Benefit\Model\Orm\BenefitLocalization;
use App\PostType\BenefitTag\Model\Orm\BenefitTag\BenefitTagLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;
use App\PostType\Education\Model\Orm\EducationLocalization;
use App\PostType\EducationTag\Model\Orm\EducationTag\EducationTagLocalization;
use App\PostType\Newsletter\Model\Orm\NewsletterLocalization;
use App\PostType\Offer\Model\Orm\OfferLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\Model\Orm\User\User;
use App\Model\TranslatorDB;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\PostType\Story\Model\Orm\StoryLocalization;
use LogicException;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class EditButton extends UI\Control
{

	public function __construct(
		private readonly ?Routable $routable,
		private readonly ?User $user,
		private readonly TranslatorDB $translator,
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->add('showEditButton', $this->routable !== null && $this->user !== null && in_array($this->user->role, [User::ROLE_ADMIN, User::ROLE_DEVELOPER]));
		if ($this->routable !== null) {
			$this->template->add('link', $this->getLink($this->routable));
		}

		$this->template->render(__DIR__ . '/editButton.latte');
	}


	private function getLink(Routable $routable): string
	{
		return match (get_class($routable)) {
			CatalogTree::class, CommonTree::class => $this->presenter->link(':Page:Admin:Page:default', ['id' => $routable->getId()]),
			BlogTagLocalization::class =>  $this->presenter->link(':BlogTag:Admin:BlogTag:edit', ['id' => $routable->getId()]),
			BlogLocalization::class =>  $this->presenter->link(':Blog:Admin:Blog:edit', ['id' => $routable->getId()]),
			CalendarTagLocalization::class =>  $this->presenter->link(':CalendarTag:Admin:Calendar:edit', ['id' => $routable->getId()]),
			CalendarLocalization::class =>  $this->presenter->link(':Calendar:Admin:Calendar:edit', ['id' => $routable->getId()]),
			BenefitTagLocalization::class =>  $this->presenter->link(':BenefitTag:Admin:Benefit:edit', ['id' => $routable->getId()]),
			BenefitLocalization::class =>  $this->presenter->link(':Benefit:Admin:Benefit:edit', ['id' => $routable->getId()]),
			ProductLocalization::class => $this->getLinkForProductVariant($routable),
			ProfileLocalization::class => $this->presenter->link(':Profile:Admin:Profile:edit', ['id' => $routable->getId()]),
			StoryLocalization::class => $this->presenter->link(':Story:Admin:Story:edit', ['id' => $routable->getId()]),
			EducationLocalization::class => $this->presenter->link(':Education:Admin:Education:edit', ['id' => $routable->getId()]),
			EducationTagLocalization::class => $this->presenter->link(':EducationTag:Admin:EducationTag:edit', ['id' => $routable->getId()]),
			OfferLocalization::class => $this->presenter->link(':Offer:Admin:Offer:edit', ['id' => $routable->getId()]),
			NewsletterLocalization::class => $this->presenter->link(':Newsletter:Admin:Newsletter:edit', ['id' => $routable->getId()]),
			default => throw new LogicException(sprintf("Unknown Routable object class '%s' for EditButton.", get_class($routable)))
		};
	}


	private function getLinkForProductVariant(Routable $routable): string
	{
		assert($routable instanceof ProductLocalization);
		$parent = $routable->getParent();

		return $this->presenter->link(':Admin:Product:edit', ['id' => $parent->id]);
	}

}
