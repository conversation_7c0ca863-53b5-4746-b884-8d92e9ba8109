<?php declare(strict_types=1);

namespace App\FrontModule\Components\ProfileContacts;

use App\FrontModule\Presenters\BasePresenter;
use App\Infrastructure\Iterators\Mapper;
use App\Model\BucketFilter\BucketFilterFactory;
use App\Model\BucketFilter\Result;
use App\Model\BucketFilter\SetupCreator\Profile\BasicElasticItemListFactory as ProfileBasicElasticItemListFactory;
use App\Model\BucketFilter\SortCreator;
use App\Model\ElasticSearch\Common\ElasticCommon;
use App\Model\ElasticSearch\Common\ResultReader;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Routable;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\Core\Model\Publishable;
use App\PostType\Profile\Model\Orm\Profile;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use Nette\Application\UI;
use Nette\Application\UI\Presenter;
use Nette\Application\UI\Template;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Entity\IEntity;

/**
 * @property-read DefaultTemplate $template
 */
final class ProfileContacts extends UI\Control
{

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly ProfileLocalization $profileLocalization,
		private readonly EsIndex $esIndex,
		private readonly TranslatorDB $translator,
		private readonly BucketFilterFactory $bucketFilterFactory,
		private readonly ProfileBasicElasticItemListFactory $basicElasticItemListFactory,
		private readonly ResultReader $resultReader,
		private readonly SortCreator $sortCreator,
	)
	{

	}

	protected function createTemplate(): Template
	{
		/** @var DefaultTemplate $template */
		$template = parent::createTemplate();
		$template->setTranslator($this->translator);

		$profile = $this->profileLocalization->getParent();
		$template->object = $this->object;
		$template->profile = $profile;
		$template->profileLocalization = $this->profileLocalization;
//		$template->profile = $this->profileLocalization->getParent();
		$template->templates = FE_TEMPLATE_DIR;

		$template->addFilter('classmates', function (Profile $profile): iterable {
			$basicElasticItemGenerator = $this->basicElasticItemListFactory->create(
				classmates: true,
				profile: $profile,
			);

			$bucketFilter = $this->bucketFilterFactory->create(
				basicElasticItemListGenerator: $basicElasticItemGenerator,
				elasticItemListGenerator: null,
				boxListGenerator: null,
				resultReader: $this->resultReader,
				esIndex: $this->esIndex,
				commonType: ElasticCommon::TYPE_PROFILE
			);

			// TODO - lepší řešení filtrovat v DB...
			$items = $bucketFilter->getItems(
				limit: 1000,
				offset: 0,
				sort: $this->sortCreator->create('newest')
			);
			return array_filter(
				iterator_to_array($items),
				fn(IEntity $item): bool => $item instanceof Publishable ? $item->isPublic() : true,
			);
		});

		// TODO - move permission logic elsewhere ?
		$presenter = $this->getPresenter();
		assert($presenter instanceof BasePresenter);
		$template->addFilter('sectionVisible', fn(Profile $profile, string $section): bool => $presenter->isProfileSectionVisible($profile, $section));

		return $template;
	}

	public function render(): void
	{
		$this->template->render(__DIR__ . '/profileContacts.latte');
	}
}
