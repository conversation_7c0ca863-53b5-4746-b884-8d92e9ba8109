<?php declare(strict_types=1);

namespace App\FrontModule\Components\ProfileContacts;

use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Routable;
use App\Model\StaticPage\StaticPage;
use App\PostType\Profile\Model\Orm\ProfileLocalization;

interface ProfileContactsFactory
{
	public function create(Routable|StaticPage $object, ProfileLocalization $profileLocalization, EsIndex $esIndex): ProfileContacts;
}
