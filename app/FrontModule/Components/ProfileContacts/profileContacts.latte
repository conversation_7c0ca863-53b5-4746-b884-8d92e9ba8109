{varType App\Model\BucketFilter\Result $classmates}
{varType App\PostType\Profile\Model\Orm\ProfileLocalization $profileLocalization}

{* Spolužáci *}
{var $classmates = ($profile|classmates)}
<h3 class="u-mt-0 u-mb-sm u-mb-md@xl">{_'profile_classmates'} ({count($classmates)})</h3>
{include $templates.'/part/crossroad/absolvents.latte', class: 'u-mb-md u-mb-xl@md', condensed: true, items: $classmates, gridSizes: 'size--6-12@sm size--6-12@lg size--4-12@xl', pager: false}

{* Sleduji *}
{var $followings = $profileLocalization->getFollowings(publicOnly: true)}
<h3 class="u-mt-0 u-mb-sm u-mb-md@xl">{_'profile_followings'} ({count($followings)})</h3>
{include $templates.'/part/crossroad/absolvents.latte', class: 'u-mb-md u-mb-xl@md', condensed: true, items: $followings, type: 'followings', gridSizes: 'size--6-12@sm size--6-12@lg size--4-12@xl', pager: false}

{* Sledování *}
{var $followers = $profileLocalization->getFollowers(publicOnly: true)}
<h3 class="u-mt-0 u-mb-sm u-mb-md@xl">{_'profile_followers'} ({count($followers)})</h3>
{include $templates.'/part/crossroad/absolvents.latte', class: 'u-mb-md u-mb-xl@md', condensed: true, items: $followers, type: 'followers', gridSizes: 'size--6-12@sm size--6-12@lg size--4-12@xl', pager: false}
