{$control['form']|actionAddFormId}
{snippet form}
	{form form class: 'f-newsletter block-loader', data-naja: '', novalidate: "novalidate"}
		<div class="grid grid--middle grid--y-sm">
			<div class="grid__cell size--6-12@xl u-mb-last-0">
				<h2 class="f-newsletter__title h3 u-mb-xs">
					<label for="f-newsletter__email"> {* TODO: unikátní id *}
						{_"newsletter_title"}
					</label>
				</h2>
			</div>
			<div class="grid__cell size--6-12@xl">
				{control messageForForm, $flashes, $form, TRUE, ['timeToggle'=>true]}
				{embed '../inp.latte', class: 'inp--group u-mb-0', noP: true, form: $form, name: email, labelLang: 'newsletter_placeholder', placeholderLang=>'newsletter_placeholder', validate: true}
					{block content}
						<span class="inp__btn">
							<button class="btn btn--secondary btn--block--m" type="submit">
								<span class="btn__text">{_"newsletter_btn"}</span>
							</button>
						</span>
					{/block}
				{/embed}
			</div>
		</div>

		{*ANTISPAM*}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
		{/if}
		{*/ANTISPAM*}

		<div class="block-loader__loader"></div>

		{if isset($flashes[0]) && $flashes[0]->type == "ok" && $flashes[0]->message == "newsletterAdded"}
			<script>
				dataLayer.push({
					'event' : 'action.optin.newsletter'
				});
			</script>
		{/if}
	{/form}
{/snippet}
