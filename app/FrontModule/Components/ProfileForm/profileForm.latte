{varType ?App\PostType\Profile\Model\Orm\Profile $profile}
{varType App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition $formDefinition}

{snippetArea form}
	{default $showSaveButton = true}

	{var $markers = []}
	{form $form autocomplete: 'off', class: 'f-profile u-mb-md u-mb-xl@md', data-naja-loader: 'body', novalidate: "novalidate", data-naja: '', data-naja-scroll: '.f-profile'}
		{snippet flash}
			{control messageForForm, $flashes, $form, TRUE, ['animate'=>true]}
		{/snippet}

		{foreach $formDefinition->extenders as $formExtender}
			{var $section = $formExtender->getName()}
			{* Additional information to some of the multiforms... *}
			{var $infoSection = $section . 'Info'}
			{* Skip these for special logic later *}
			{continueIf in_array($section, ['visibility',])}

			{ifset $form[$section]}
				{snippet 'section-' . $section}
					<div data-section="{$section}" class="u-mb-md u-mb-lg@md">
						<div class="u-mb-md">
							<div class="grid grid--space-between grid--middle grid--x-sm grid--y-xs">
								<div class="grid__cell size--auto@sm size--12-12@md size--auto@lg">
									<h2 n:tag="in_array($section, ['educations_external']) ? h3" class="u-mb-0">{_'profile_section_' . $section}</h2>
								</div>

								<div n:if="isset($form['visibility'][$section])" class="grid__cell size--auto@sm size--12-12@md size--auto@lg">
									{formContainer visibility}
										{* {include '../inp.latte', form: $form['visibility'], class: 'u-mb-0', name: $section, labelLang: 'form_label_visibility'} *}

										<p class="inp u-mb-0">
											<span class="inp__fix">
												<label n:name="$section" class="inp__label inp__label--inside">{_"form_label_visibility"}</label>
												<select n:name="$section" class="inp-select" data-controller="choices ajax-link"
														data-action="change->ajax-link#handleSelectChange" data-ajax-link-url-value="{$control->link('changeVisibility!', [section => $section, value => '%value'])}"></select>
											</span>
										</p>
									{/formContainer}
								</div>
							</div>
						</div>

						{switch $section}
							{case avatar}
								{formContainer $form[$section]->getName()}
									{include $templates.'/part/box/avatar.latte', profile: $profile, class: 'u-maw-2-12'}
								{/formContainer}
							{case name}
								{include #section container: $form[$section], gridSizes: 'size--6-12@sm size--12-12@md size--6-12@lg size--4-12@xl'}
							{case contact}
								{include #section container: $form[$section], gridSizes: 'size--6-12@xl'}
							{case socials, educations, educations_external, employments}
								{if $section == 'employments'}
									{php $gridSizes = 'size--6-12@sm size--4-12@xl'}
								{/if}
								<div data-controller="add" data-add-section-name-value="{$section}">

									{if isset($form[$infoSection])}
										{include #section container: $form[$infoSection], gridSizes: false}
									{/if}

									{include #multiSection container: $form[$section]}

									{capture $markers[$section]}
										{include #multiSection container: $form[$section], marker: true}
									{/capture}

								</div>
							{default}
								{include #section container: $form[$section]}
						{/switch}
					</div>

					{sep}<hr n:if="!in_array($section, ['educations'])" class="u-mb-md u-mb-lg@md">{/sep}
				{/snippet}
			{/ifset}
		{/foreach}

		<p n:if="$showSaveButton" class="f-profile__submit u-mb-0">
			<button type="submit" class="btn" n:name="save">
				<span class="btn__text">{_'btn_save'}</span>
			</button>
		</p>
	{/form}

	{* Templaty - mimo formulář *}
	<div class="u-hide">
		{foreach $markers as $marker}
			{$marker}
		{/foreach}
	</div>
{/snippetArea}

{define #section}
	{default $gridSizes = 'size--6-12@sm size--12-12@md size--6-12@lg'}

	{formContainer $container->getName()}
		<div n:class="b-profile__section, 'b-profile__section--' . $section" {if $section == 'educations' && $container->getName() != 'educationsInfo'}data-controller="faculty" data-faculty-choices-outlet=".inp-faculty, .inp-programme, .inp-branch"{/if}>
			<div class="grid grid--x-sm grid--y-0 grid--middle">
				{foreach $container->getControls() as $name => $sectionControl}
					{if $sectionControl instanceof \Nette\Forms\Controls\Checkbox && $name != 'untilNow' || in_array($name, ['level'])}
						{php $gridSizes = false}
					{elseif in_array($name, ['from', 'to'])}
						{php $gridSizes = 'size--6-12@sm size--2-12@xl'}

					{elseif in_array($name, ['untilNow'])}
						{php $gridSizes = 'size--3-12@xl'}

					{elseif in_array($name, ['note'])}
						{php $gridSizes = 'size--5-12@xl'}

					{elseif in_array($name, ['faculty'])}
						{php $gridSizes = 'size--8-12@xl'}

					{else}
						{php $gridSizes = 'size--6-12@sm size--12-12@md size--6-12@lg'}
					{/if}

					<div n:tag-if="!($sectionControl instanceof \Nette\Forms\Controls\HiddenField)" n:class="grid__cell, $gridSizes">
						{if in_array($name, ['phone'])}
							{include '../inp.latte', class: 'u-mb-sm', form: $container, name: $name, type: 'tel'}
						{elseif in_array($name, ['birthdate'])}
							{include '../inp.latte', class: 'u-mb-sm', form: $container, name: $name, type: 'date'}
						{elseif in_array($name, ['level'])}
							{include '../inp.latte', class: 'u-mb-md', form: $container, name: $name}
						{elseif in_array($name, ['faculty', 'programme', 'branch'])}
							{include '../inp.latte', class: 'u-mb-sm', form: $container, name: $name, facultyFilter: true, required: $section == 'educations' && in_array($name, ['faculty', 'programme'])}
						{elseif $section == 'educations' && in_array($name, ['from', 'to'])}
							{include '../inp.latte', class: 'u-mb-sm', form: $container, name: $name, required: true}
						{else}
							{include '../inp.latte', class: 'u-mb-sm', form: $container, name: $name}
						{/if}
					</div>
				{/foreach}
			</div>
		</div>
	{/formContainer}
{/define}

{define #multiSectionItem}
	{default $marker = false}

	<div n:tag-if="$marker" data-template="{$section}"{if $section == 'educations'} style="border: 1px solid blue"{/if}>
		<div class="grid grid--nowrap grid--x-sm grid--y-0" data-item>
			<div class="f-profile__cell f-profile__cell--items grid__cell">
				<div n:tag-if="in_array($section, ['employments', 'educations', 'educations_external'])" class="u-mb-sm">
					{formContainer $name}
						{switch $name}
							{case educations}
								{include #section container: $component, gridSizes: 'size--6-12@sm size--12-12@md size--6-12@lg size--4-12@xl'}
							{case employments}
								{include #section container: $component, gridSizes: 'size--6-12@sm size--12-12@md size--6-12@lg size--4-12@xl'}
							{default}
								{include #section container: $component, gridSizes: 'size--6-12@sm size--12-12@md size--6-12@lg'}
						{/switch}
					{/formContainer}
				</div>
			</div>
			<div class="f-profile__cell f-profile__cell--remove grid__cell">
				<p class="f-profile__remove">
					<button type="button" class="btn-circle" data-action="add#remove">{('close-sm')|icon}</button>
				</p>
			</div>
		</div>
	</div>

{/define}

{define #multiSection}
	{default $marker = false}

	{if $marker}
		{include #multiSectionItem section: $section, name: $container->getName(), component: $container['newItemMarker'], marker: true}
	{else}
		<div class="f-profile__multi" data-add-target="items">
			{foreach $container->getComponents() as $component}
				{continueIf $component->getName() === 'newItemMarker'}
				{include #multiSectionItem section: $section, name: $container->getName(), component: $component}
			{/foreach}
		</div>
		<p class="u-mb-0">
			<button type="button" class="btn btn--secondary" data-action="add#add" data-type="{$section}">
				<span class="btn__text">
					{_"btn_add"}
				</span>
			</button>
		</p>
	{/if}
{/define}
