<?php declare(strict_types=1);

namespace App\FrontModule\Components\ProfileForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\HasError500Catcher;
use App\Model\Form\CommonFormFactory;
use App\Model\Orm\Routable;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Profile\FrontModule\Components\Form\ProfileFormPrescription;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\PostType\Profile\Model\Orm\ProfileModel;
use App\PostType\Profile\Model\Orm\Settings\ProfileVisibilitySetting;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Application\UI\Template;
use Nette\InvalidStateException;
use Nette\Utils\ArrayHash;
use Throwable;

final class ProfileForm extends Control
{
	use HasError500Catcher;

	/** @internal */
	private array $formDefinitions = [];

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly ProfileLocalization $profileLocalization,
		private readonly string $renderMode,
		private readonly array $renderParameters,
		private readonly TranslatorDB $translator,
		private readonly Builder $builder,
		private readonly CommonFormFactory $commonFormFactory,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly ProfileFormPrescription $profileFormPrescription,
		private readonly ProfileModel $profileModel,
	)
	{
	}

	protected function createTemplate(): Template
	{
		$template = parent::createTemplate();
		$template->setTranslator($this->translator);

		$template->object = $this->object;
		$template->profile = $this->profileLocalization->getParent();
		$template->templates = FE_TEMPLATE_DIR;

		return $template;
	}

	public function handleChangeVisibility(string $section, string $value): void
	{
		if (in_array($section, ProfileVisibilitySetting::keys(), true) &&
			in_array($value, ProfileVisibilitySetting::options(), true)) {
			$profile = $this->profileLocalization->getParent();
			$profile->settings->visibility->$section = $value;

			$this->profileModel->update($this->profileLocalization);
		}

		if ($this->presenter->isAjax()) {
			$this->presenter->sendPayload();
		} else {
			$this->redirect('this');
		}
	}

	public function render(): void
	{
		$name = $this->renderMode . 'Form';
		$parameters = $this->renderParameters;

		try {
			$this->template->form = $this->getComponent($name);
			$this->template->formDefinition = $this->getFormDefinition($name);
			$this->template->setParameters($parameters);

			$this->template->render(__DIR__ . '/profileForm.latte');
		} catch (Throwable $e) {
			/** @noinspection PhpUnhandledExceptionInspection */
			$this->handleRenderError500($e);
		}
	}

	// --------------------------------------------------------------------------------------

	/**
	 * Creates form component based on definition identified by component name
	 *
	 * @param string $name
	 * @return Form
	 */
	private function createFormFromDefinition(string $name): Form
	{
		$formDefinition = $this->getFormDefinition($name);

		$form = $this->commonFormFactory->create();
		$form->setTranslator($this->translator);
		$this->builder->addExtenders($form, $formDefinition->extenders);

		$form->addSubmit('save', 'btnSave');

		$form->onValidate[] = [$this, 'formValidate'];
		$form->onError[] = [$this, 'formError'];
		$form->onSuccess[] = function (Form $form, ArrayHash $formData) use ($formDefinition): void {
			foreach ($formDefinition->extenders as $extender) {
				($extender->getSuccessHandlerFunction())($form, $formData);
			}

			$this->profileLocalization->syncNameFromSections();

			$this->profileModel->update($this->profileLocalization);

			if ($this->presenter->isAjax()) {
				// TODO how to invalidate presenter snippets here?
				$this->redrawControl();
			} else {
				$this->presenter->redirect('this');
			}
		};

		return $formDefinition->form = $form;
	}

	public function formValidate(Form $form, ArrayHash $values): void
	{

	}

	public function formError(Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	// --------------------------------------------------------------------------------------
	// Form definitions / forms

	public function getFormDefinition(string $name): FormDefinition
	{
		if (!isset($this->formDefinitions[$name])) {
			switch ($name) {
				case 'profileForm':
					$formDefinition = $this->profileFormPrescription->getDefinition($this->profileLocalization);
					break;
				case 'avatarForm':
					$formDefinition = $this->profileFormPrescription->getAvatarDefinition($this->profileLocalization);
					break;
				case 'notificationsForm':
					$formDefinition = $this->profileFormPrescription->getNotificationsDefinition($this->profileLocalization);
					break;
				case 'interestsForm':
					$formDefinition = $this->profileFormPrescription->getInterestsDefinition($this->profileLocalization);
					break;
			}

			if (!isset($formDefinition)) {
				throw new InvalidStateException(sprintf('Can not get form definition for "%s"', $name));
			}
			$this->formDefinitions[$name] = $formDefinition;
		}

		return $this->formDefinitions[$name];
	}

	protected function createComponentAvatarForm(string $name): Form
	{
		return $this->createFormFromDefinition($name);
	}

	protected function createComponentProfileForm(string $name): Form
	{
		return $this->createFormFromDefinition($name);
	}

	protected function createComponentInterestsForm(string $name): Form
	{
		return $this->createFormFromDefinition($name);
	}

	protected function createComponentNotificationsForm(string $name): Form
	{
		return $this->createFormFromDefinition($name);
	}
}
