<?php

declare(strict_types=1);

namespace App\FrontModule\Components\ClassmateTipForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Routable;
use App\Model\Orm\User\User;
use Nette\Application\UI;
use Nette\Utils\ArrayHash;
use App\FrontModule\Components\HasAntispamInput;
use App\FrontModule\Components\HasError500Catcher;
use App\Model\Mutation\MutationHolder;
use App\Model\TranslatorDB;
use function assert;


final class ClassmateTipFormControl extends UI\Control
{
	use HasAntispamInput;
	use HasError500Catcher;

	private Mutation $mutation;

	public function __construct(
		private readonly Routable $object,
		private readonly ?User $user,
		private readonly ClassmateTipFormFactory $classmateTipFormFactory,
		private readonly TranslatorDB $translator,
		private readonly MessageForFormFactory $messageForFormFactory,
		MutationHolder $mutationHolder,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}

	protected function createComponentForm(): UI\Form
	{
		$form = $this->classmateTipFormFactory->create($this->object, $this->mutation, $this->user);
		assert($form instanceof UI\Form);

		$this->attachAntispamTo($form);

		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	private function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$this->flashMessage('tip_ok', 'ok');

		if ($this->presenter->isAjax()) {
			$valuesArr = (array) $values;
			unset($valuesArr['name']);
			unset($valuesArr['antispamHash']);

			$form->setValues(array_fill_keys(array_keys($valuesArr), null));
			$this->redrawControl();
		} else {
			$this->presenter->redirect('this');
		}
	}

	private function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function render(): void
	{
		try {
			$this->template->setTranslator($this->translator);
			$this->template->object = $this->object;
			$this->template->pages = $this->mutation->pages;
			$this->template->render(__DIR__ . '/classmateTipForm.latte');

		} catch (\Throwable $e) {
			/** @noinspection PhpUnhandledExceptionInspection */
			$this->handleRenderError500($e);
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
