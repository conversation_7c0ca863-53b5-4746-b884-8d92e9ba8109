<?php

declare(strict_types=1);

namespace App\FrontModule\Components\ClassmateTipForm;

use App\Model\Email\CommonFactory;
use App\Model\Form\CommonFormFactory;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\Model\Orm\User\User;
use App\PostType\Profile\Model\Orm\Profile;
use App\PostType\Profile\Model\Orm\ProfileRepository;
use Nette\Application\UI\Form as UIForm;
use Nette\Forms\Form;
use Nette\Utils\ArrayHash;
use Throwable;
use Tracy\Debugger;

final readonly class ClassmateTipFormFactory
{
	private const string EMAIL_TEMPLATE_UID = 'classmateTip';

	public function __construct(
		private readonly CommonFormFactory $formFactory,
		private readonly LinkFactory $linkFactory,
		private readonly CommonFactory $commonEmailFactory,
		private readonly ProfileRepository $profileRepository,
	)
	{

	}

	public function create(
		Routable $object,
		Mutation $mutation,
		?User $user = null,
		bool $standalone = false,
	): Form
	{
		$form = $this->formFactory->create($standalone ? Form::class : UIForm::class);

		$name = $user?->profile !== null ?
			$user->profile->getLocalization($mutation)->getSectionsShortName() : $user?->name;
		$form->addText('name', 'form_label_your_name')->setRequired()
			->setDefaultValue($name);
		$form->addText('tipName', 'form_label_tip_name')->setRequired();
		$form->addTextArea('tip', 'form_label_tip_note')->setRequired();
		$form->addEmail('email', 'form_label_tip_email')->setRequired()
			->addRule(Form::Email);
		$form->addText('phone', 'form_label_tip_phone');

		$form->addSubmit('send');

		$form->onSuccess[] = function (Form $form, ArrayHash $values) use ($user, $mutation): void {
			try {
				$profile = $user?->profile;

				// Is authenticated user AND has socials profile...
				if ($user !== null && $profile !== null) {
					$nameLink = $this->linkFactory->linkTranslateToNette(
						$user->profile->getLocalization($mutation)
					);
				}

				// Try search by name...
				/** @var Profile|null $profileTipDetail */
				$profileTipDetail = $this->profileRepository->searchByName($values->tipName, array_filter([$profile?->id]))
					->limitBy(1)->fetch();
				if ($profileTipDetail) {
					$nameTipLink = $this->linkFactory->linkTranslateToNette(
						$profileTipDetail->getLocalization($mutation)
					);
				}

				$this->commonEmailFactory->create()->send(
					from: '',
					to: $mutation->getRealAdminEmail(),
					dbTemplate: self::EMAIL_TEMPLATE_UID,
					data: [
						'NAME' => $values->name,
						'NAME-LINK' => $nameLink ?? '',
						'NAME-TIP' => $values->tipName,
						'NAME-TIP-LINK' => $nameTipLink ?? '',
						'TIP' => $values->tip,
						'EMAIL' => $values->email,
						'PHONE' => $values->phone,
					],
				);
			} catch (Throwable $error) {
				Debugger::log($error);
				$form->addError('Operation failed');
			}
		};

		return $form;
	}
}
