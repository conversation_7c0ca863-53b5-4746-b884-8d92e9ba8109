{default $class = false}
{default $cols = false}
{default $rows = false}
{default $type = isset($form[$name]) ? $form[$name]->getOption('type') : false}
{default $label = isset($form[$name]) ? ($type == 'checkbox' ? $form[$name]->caption : $form[$name]->label?->getText()) : ''}
{default $labelLang = false}
{default $inpClass = false}
{default $labelClass = false}
{default $labelReplace = false}
{default $agreeLabel = false}
{default $validate = false}
{default $noP = false}
{default $disabled = isset($form[$name]) ? $form[$name]->isDisabled() : false}
{default $required = isset($form[$name]) ? $form[$name]->isRequired() : false}
{default $placeholder = ''}
{default $placeholderLang = false}
{if is_string($placeholderLang)}{capture $placeholder}{translate}{$placeholderLang}{/translate}{/capture}{/if}
{default $dataAction = false}
{default $validate = false}
{default $showError = true}
{default $labelInside = true}
{default $hintLang = false}
{default $facultyFilter = false}

{if $validate}{php $inpClass = $inpClass . ' inp-validate'}{/if}
{capture $label}{if $labelLang}{translate}{$labelLang}{/translate}{else}{$label}{/if}{if $label && $type != 'checkbox'}:{/if}{if $required && $label} <span class="u-c-red">*</span>{/if}{/capture}

<p n:tag="$noP ? 'span'" n:class="inp, $form[$name]->errors ? has-error, $class, $hintLang ? inp--hint">
	{if $type == 'checkbox'}
		<label n:class="inp-item, inp-item--checkbox, $labelClass">
			<input n:name="{$name}" n:class="inp-item__inp, $validate ? inp-validate"{if $dataAction} data-action="{$dataAction}"{/if}>
			<span class="inp-item__text">
				{if $labelReplace}
					{$label|replace:"%link%",$labelReplace|noescape}
				{elseif $agreeLabel}
					{_form_label_agree_text} <a href="{plink $pages->personalData}" target="_blank" rel="noopener noreferrer">{_form_label_agree_link}</a> <span class="inp-required">*</span>
				{else}
					{_$label|noescape}
				{/if}
			</span>
		</label>
	{elseif $type == 'radio'}
		<span n:class="inp__label, $labelClass">{$label|noescape}</span>

		<ul class="inp-list">
			{foreach $form[$name]->items as $k=>$i}
				<li>
					<label class="inp-item inp-item--radio">
						<input type="radio" name="{$name}" value="{$k}" n:class="inp-item__inp, $validate ? inp-validate" {if $form[$name]->value == $k} checked="checked"{/if}{if $dataAction} data-action="{$dataAction}"{/if}>
						<span class="inp-item__text">
							{translate}{$i|noescape}{/translate}
						</span>
					</label>
				</li>
			{/foreach}
		</ul>
	{elseif $type == 'checkboxList'}
		<span n:class="inp__label, $labelClass">{$label|noescape}</span>

		<ul class="inp-list inp-list--block">
			{foreach $form[$name]->items as $k=>$i}
				<li>
					<label class="inp-item inp-item--checkbox">
						<input type="checkbox" n:name="$name" value="{$k}" n:class="inp-item__inp, $validate ? inp-validate" n:attr="checked => in_array($k, $form[$name]->value) ? checked, data-action ? $dataAction">
						<span class="inp-item__text">
							{translate}{$i|noescape}{/translate}
						</span>
					</label>
				</li>
			{/foreach}
		</ul>
	{else}
		<label n:if="!$labelInside" n:name="{$name}" n:class="inp__label, $labelClass">{$label|noescape}</label>

		{if $type == 'file'}
			{capture $dataText}{_select_file}{/capture}
			{input $name class=>'inp-file', data-text=>$dataText, data-action=>$dataAction}
		{elseif $type == 'select'}
			<span class="inp__fix">
				<label n:if="$labelInside" n:name="{$name}" n:class="inp__label, inp__label--inside, $labelClass">{$label|noescape}</label>
				{if $facultyFilter}
					{input $name class=>'inp-select inp-'.$name, data-action=>$dataAction, aria-describedby=>$name.'_error', data-controller=>'choices', data-faculty-target=>"inp".($name|firstUpper)}
				{else}
					{input $name class=>'inp-select', data-action=>$dataAction, aria-describedby=>$name.'_error', data-controller=>'choices'}
				{/if}
				{* <span id="{$name.'_error'}" n:if="$validate && $showError" class="inp__error"></span> *}
				{include #error}
				{include #hint}
			</span>
		{elseif $type == 'date'}
			<span class="inp__fix inp__fix--icon-after">
				<label n:if="$labelInside" n:name="{$name}" n:class="inp__label, inp__label--inside, $labelClass">{$label|noescape}</label>
				{input $name, type=>$type, class=>'inp__text inp__text--bold', data-inp-phone-target=>'inp', placeholder=>$placeholder, data-controller=>'datepicker', data-action=>$dataAction, aria-describedby=>$name.'_error'}
				{('calendar')|icon, 'inp__icon'}
			</span>
		{elseif $type == 'tel'}
			<span class="inp__fix" data-controller="inp-phone">
				<label n:if="$labelInside" n:name="{$name}" n:class="inp__label, inp__label--inside, $labelClass">{$label|noescape}</label>
				{input $name, type=>$type, class=>'inp__text', data-inp-phone-target=>'inp', placeholder=>$placeholder, data-action=>$dataAction, aria-describedby=>$name.'_error'}
				{* <span id="{$name.'_error'}" n:if="$validate && $showError" class="inp__error u-d-n">{_'error_invalid_phone'}</span> *}
				{include #error}
				{include #hint}
			</span>
		{else}
			<span class="inp__fix">
				<label n:if="$labelInside && $label" n:name="{$name}" n:class="inp__label, inp__label--inside, $labelClass">{$label|noescape}</label>
				{input $name class=>implode(' ', ['inp__text', $inpClass]), cols=>$cols, rows=>$rows, disabled=>$disabled, placeholder=>$placeholder, data-action=>$dataAction, aria-describedby=>$name.'_error'}
				{* <span id="{$name.'_error'}" n:if="$validate && $showError" class="inp__error"></span> *}
				{include #error}
				{include #hint}
			</span>
		{/if}
	{/if}
	{block content}{/block}
</p>


{define #hint}
	<span n:if="$hintLang" class="inp__hint hint hint--icon" data-controller="tippy" title="{_$hintLang}">
		{('hint')|icon, 'inp__hint-icon'}
	</span>
{/define}

{define #error}
	{if $validate && $showError || $form[$name]->errors}
		{('close-circle')|icon, 'inp__icon'}
	{/if}
{/define}
