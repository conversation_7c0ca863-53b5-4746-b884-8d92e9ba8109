{varType App\PostType\Profile\Model\Orm\Profile $profile}
{varType App\PostType\Profile\Model\Orm\Profile|int $profileOther}

{snippet follow}
	{var $profileOtherId = is_int($profileOther) ? $profileOther : $profileOther->id}
	{if $profileOtherId !== $profile->id}
		{var $following = $profile->getFollowing($profileOtherId)}
		<a n:attr="$following !== null ? ('following_since'|translate) . ' ' . ($following->since|date:'d.m.Y H:i')"
			n:href="$following === null ? 'follow!' : 'unfollow!'" data-naja data-naja-history="off" data-naja-force-redirect
				class="btn btn--loader btn--secondary btn--outline btn--block btn--sm link-mask__unmask">
			<span class="btn__text">{_($following === null ? 'btn_follow' : 'btn_unfollow')}</span>
		</a>
	{/if}
{/snippet}
