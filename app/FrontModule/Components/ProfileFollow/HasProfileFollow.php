<?php declare(strict_types=1);

namespace App\FrontModule\Components\ProfileFollow;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\Routable;
use App\Model\StaticPage\StaticPage;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use Nette\Application\UI\Multiplier;
use Nette\Application\UI\Presenter;
use Nextras\Orm\Entity\IEntity;

trait HasProfileFollow
{

	private ProfileFollowFactory $profileFollowFactory;

	abstract protected function getUserProfileLocalization(): ?ProfileLocalization;

	abstract public function getObject(): Routable|StaticPage;

	public function injectProfileFollowFactory(ProfileFollowFactory $ProfileFollowFactory): void
	{
		$this->profileFollowFactory = $ProfileFollowFactory;
	}

	protected function createComponentProfileFollow(): ProfileFollow
	{
		$profileLocalization = $this->getUserProfileLocalization();
		if ($profileLocalization === null) {
			$presenter = $this->presenter;
			assert($presenter instanceof BasePresenter);
			$presenter->redirectToUserPage('userLogin');
		}

		$profileFollow = $this->profileFollowFactory->create(
			object: $this->getObject(),
			profileLocalization: $profileLocalization
		);
		$profileFollow->renderMode = 'detail';
		return $profileFollow;
	}

	protected function getComponentProfileFollow(): ProfileFollow
	{
		return $this->getComponent('profileFollow');
	}

	protected function createComponentProfileFollows(): Multiplier
	{
		return new Multiplier(function ($otherProfileId): ProfileFollow {
			$profileFollow = $this->createComponentProfileFollow();
			$profileFollow->setProfileOther(intval($otherProfileId));
			$profileFollow->renderMode = 'list';

			return $profileFollow;
		});
	}

}
