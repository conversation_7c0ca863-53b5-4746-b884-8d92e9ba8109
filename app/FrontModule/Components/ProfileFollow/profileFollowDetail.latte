{varType App\PostType\Profile\Model\Orm\Profile $profile}
{varType App\PostType\Profile\Model\Orm\Profile|int $profileOther}

{snippet follow}
	{var $profileOtherId = is_int($profileOther) ? $profileOther : $profileOther->id}
	{if $profileOtherId !== $profile->id}
		{var $following = $profile->getFollowing($profileOtherId)}
		<a n:attr="$following !== null ? ('following_since'|translate) . ' ' . ($following->since|date:'d.m.Y H:i')"
			n:href="$following === null ? 'follow!' : 'unfollow!'" data-naja data-naja-history="off" data-naja-loader="body"
				class="btn btn--sm btn--outline btn--white">
			<span class="btn__text">{_($following === null ? 'btn_follow' : 'btn_unfollow')}</span>
		</a>
	{/if}
{/snippet}
