<?php declare(strict_types=1);

namespace App\FrontModule\Components\ProfileFollow;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Form\CommonFormFactory;
use App\Model\Image\ImageObjectFactory;
use App\Model\Link\LinkFactory;
use App\Model\MenuService;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Routable;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserRepository;
use App\Model\Pages;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\Model\Email\CommonFactory as EmailCommonFactory;
use App\PostType\Profile\Model\Orm\Profile;
use App\PostType\Profile\Model\Orm\ProfileFollowingRepository;
use App\PostType\Profile\Model\Orm\ProfileInvite;
use App\PostType\Profile\Model\Orm\ProfileInviteRepository;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\PostType\Profile\Model\Orm\ProfileRepository;
use Nette\Application\UI;
use Nette\Application\UI\Template;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\InvalidStateException;
use Nette\Utils\ArrayHash;
use Nette\Forms\Form;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Tracy\Debugger;
use Throwable;

/**
 * @property-read DefaultTemplate $template
 */
final class ProfileFollow extends UI\Control
{
	private readonly Mutation $mutation;

	private Profile|int $profileOther;

	public string $renderMode;

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly ProfileLocalization $profileLocalization,
		private readonly TranslatorDB $translator,
		private readonly ProfileRepository $profileRepository,
		private readonly ProfileFollowingRepository $profileFollowingRepository,
		MutationHolder $mutationHolder,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}

	/**
	 * Profile to follow/unfollow
	 *
	 * @param int|Profile $profile
	 * @return void
	 */
	public function setProfileOther(int|Profile $profile): void
	{
		$this->profileOther = $profile;
	}

	public function loadProfileOther(): ?Profile
	{
		if (!isset($this->profileOther)) {
			throw new InvalidStateException('$profileOther must be set in this place');
		}

		return is_int($this->profileOther) ? $this->profileRepository->getById($this->profileOther) : $this->profileOther;
	}

	public function handleFollow(): void
	{
		$profile = $this->profileLocalization->getParent();
		$profileOther = $this->loadProfileOther();

		if ($profileOther !== null && $profileOther !== $profile) {
			$following = $profile->follow($profileOther);
			if (!$following->isPersisted()) {
				$this->profileFollowingRepository->persistAndFlush($following);
			}
		}

		$this->flashMessage('following');

		if ($this->presenter->isAjax()) {
			$this->redrawControl('follow');
		} else {
			$this->presenter->redirect('this');
		}
	}

	public function handleUnfollow(): void
	{
		$profile = $this->profileLocalization->getParent();
		$profileOther = $this->loadProfileOther();

		if ($profileOther !== null && $profileOther !== $profile) {
			$following = $profile->unfollow($profileOther);
			if ($following !== null) {
				$this->profileFollowingRepository->removeAndFlush($following);
			}
		}

		$this->flashMessage('unfollowing');

		if ($this->presenter->isAjax()) {
			$this->redrawControl('follow');
		} else {
			$this->presenter->redirect('this');
		}
	}

	protected function createTemplate(): Template
	{
		/** @var DefaultTemplate $template */
		$template = parent::createTemplate();
		$template->setTranslator($this->translator);

		$profile = $this->profileLocalization->getParent();

		$template->object = $this->object;
		$template->profile = $profile;
		$template->profileLocalization = $this->profileLocalization;
		$template->mutation = $this->mutation;
		$template->templates = FE_TEMPLATE_DIR;

		if (!isset($this->profileOther)) {
			throw new InvalidStateException('$profileOther must be set in this place');
		}
		$template->profileOther = $this->profileOther;

		return $template;
	}

	public function render(): void
	{
		if (!isset($this->renderMode)) {
			throw new InvalidStateException('$renderMode not set');
		}

		switch ($this->renderMode) {
			case 'list':
				$this->template->render(__DIR__ . '/profileFollowList.latte');
				break;
			case 'detail':
				$this->template->render(__DIR__ . '/profileFollowDetail.latte');
				break;
		}
	}
}
