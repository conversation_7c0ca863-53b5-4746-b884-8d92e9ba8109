<div class="m-sub">
	<div class="m-sub__col">
		<ul class="m-sub__list">
			<li n:foreach="$m->crossroad as $i" class="m-sub__item">
				<a href="{plink $i filter => null}" n:class="m-sub__link, $object->id == $i->id ? is-active">{$i->nameAnchor}</a>
				<ul n:ifcontent class="m-sub__list">
					<li n:foreach="$i->crossroad as $j" class="m-sub__item">
						<a href="{plink $j filter => null}" n:class="m-sub__link, $object->id == $j->id ? is-active">{$j->nameAnchor}</a>
					</li>
				</ul>
			</li>
		</ul>
	</div>
</div>
