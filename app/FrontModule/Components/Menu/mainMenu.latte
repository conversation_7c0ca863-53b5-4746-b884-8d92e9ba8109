<a href="#" class="m-main__toggle" data-action="toggle-class#toggle">
	{('menu')|icon, 'm-main__toggle-icon m-main__toggle-icon--open'}
	{('close-sm')|icon, 'm-main__toggle-icon m-main__toggle-icon--close'}
	<span class="m-main__toggle-label">Menu</span>
</a>

{php $mainMenuCf = $mutation->cf->mainMenu ?? []}

<ul class="m-main__list m-main__list--main">
	{foreach $mainMenuCf as $m}
		{php $hasSubmenu = isset($m->crossroad) && $m->crossroad->count()}
		{php $active = false}

		{*vyuzijeme path - ktará obsahuje IDčka *}
		{if in_array($m->id, $object->path)}
			{* pokud aktuální IDčko leží v path => aktivní *}
			{php $active = true}
		{elseif isset($object->uid) && $object->uid}
			{php $active = $m->uid == $object->uid}
		{/if}
		<li class="m-main__item" {if $hasSubmenu} data-controller="toggle-class touch-open etarget"{/if}>
			<a href="{plink $m filter => null}" n:class="m-main__link, $active ? is-active" data-action="touch-open#open">{$m->nameAnchor}</a>
			<a n:if="$hasSubmenu" href="#" class="m-main__toggle-sub" data-action="toggle-class#toggle">
				{('angle-d')|icon, 'm-main__toggle-sub-icon'}
				<span class="u-vhide">Submenu</span>
			</a>
			<div n:if="$hasSubmenu" class="m-main__submenu">
				{include './submenu.latte', m: $m}
			</div>
		</li>
	{/foreach}
	<li class="m-main__item hide--m u-hide@xl">
		{control toggleLanguage}
	</li>
</ul>
