{default $open = false}
{default $title = false}
{default $tooltip = false}

{* <div n:class="f-filter__group, $open ? is-open" data-controller="toggle-class">
    <p n:if="$title" class="f-filter__title">
		<button type="button" n:class="f-filter__name, as-link, $open ? is-open" data-action="toggle-class#toggle" aria-expanded="{$open ? 'true' : 'false'}">
			{_$title}
			{('angle-d')|icon, 'f-filter__arrow'}
		</button>
        <span n:if="$tooltip" class="f-filter__tooltip" title="{$box->description}">
            {$tooltip}
		</span>
	</p>
	<div class="f-filter__inner"> *}
		{block content}{/block}
	{* </div>
</div> *}