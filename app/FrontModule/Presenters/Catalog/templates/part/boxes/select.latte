{default $forceOpen = false}
{default $groupBy = null}
{default $facultyFilter = false}
{varType App\Model\BucketFilter\Box\CheckBoxes $box}

{embed './part/filter-group.latte', open: $box->isOpen() || $forceOpen, title: $box->title, tooltip: $box->description ?? false}
	{block content}
		{var $items = $box->getItems()}
		{var $itemsGrouped = $items}
		{var $groupsNames = []}
		{if $groupBy !== null}
			{var $itemsGrouped = []}
			{foreach $items as $item}
				{var $group = $item->attributes[$groupBy][id]}
				{php $groupsNames[$group] = $item->attributes[$groupBy][name]}
				{php $itemsGrouped[$group][] = $item}
			{/foreach}
		{/if}

		<p class="inp u-mb-0">
			<span class="inp__fix">
				<label for="filter[dials][{$box->getName()}][]" class="inp__label inp__label--inside">{_$box->title}</label>
				<select name="filter[dials][{$box->getName()}][]" id="filter[dials][{$box->getName()}][]"
						n:class="inp-select, $facultyFilter ? 'inp-'.$box->name"
						data-controller="choices autosubmit"
						data-action="change->autosubmit#submitForm"
						n:attr="data-group-name => $box->getName(), data-group-by => $groupBy ?? null"
						{if $facultyFilter} data-faculty-target="inp{$box->name|firstUpper}"{/if}>
						<optgroup n:tag-if="$groupBy !== null" label=""> {* todo nezobrazovat, pokud z be prijde jina vybrana fakulta / program *}
							<option value="0">{_'form_label_not_selected'}</option>
						</optgroup>
					{define #option value: $value}
						<option value="{$value->inputValue}" n:attr="selected => $value->isChecked ? selected">
							{_$value->name}
						</option>
					{/define}

					{foreach $itemsGrouped as $group => $value}
						{if $groupBy !== null}
							{var $itemsGroup = $value}
							<optgroup label="{$groupsNames[$group]}" data-group="{$group}" n:inner-foreach="$itemsGroup as $value">
								{include #option value => $value}
							</optgroup>
						{else}
							{include #option value => $value}
						{/if}
					{/foreach}
				</select>
			</span>
		</p>
	{/block}
{/embed}
