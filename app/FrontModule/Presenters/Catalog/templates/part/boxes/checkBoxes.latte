{default $forceOpen = false}
{varType App\Model\BucketFilter\Box\CheckBoxes $box}

{embed './part/filter-group.latte', open: $box->isOpen() || $forceOpen, title: $box->title, tooltip: $box->description ?? false}
	{block content}
		<div n:if="count($box->getItems())" class="inp-group" data-controller="inp-group">
			{php $activeTags = []}
			{foreach $box->getItems() as $value}
				{if isset($value->isChecked) && $value->isChecked}{php $activeTags[] = $value->name}{/if}
			{/foreach}

			<p class="inp-group__label inp__label inp__label--inside u-mb-0" data-inp-group-target="label">
				{_$box->title}
			</p>
			<p class="inp-group__inp inp__text inp__text--bold u-mb-0" data-inp-group-target="values" data-action="click->inp-group#toggle">
				{foreach $activeTags as $tag}{$tag}{sep}, {/sep}{/foreach}
			</p>

			<ul class="inp-group__list">
				{foreach $box->getItems() as $value}
					{capture $link}{link 'this', filter => $value->followingFilterParameters, 'pager-page' => null}{/capture}
					{php $link = urldecode(htmlspecialchars_decode($link))}

					<li class="inp-group__item link-mask">
						<label class="inp-group__item-label inp-item inp-item--checkbox link-mask__link">
							<input type="checkbox" name="{$value->inputName}" value="{$value->inputValue}" data-action="change->inp-group#update change->filter#submit" class="inp-item__inp" {if !$value->isChecked && $value->count == 0} disabled{/if} {if isset($value->isChecked) && $value->isChecked} checked{/if} data-inp-group-target="input" data-name="{$value->name}">
							<span class="inp-item__text">
								{if !$value->isChecked && $value->count == 0}
									{$value->name}
									{if $box->unit}
										{$box->unit}
									{/if}
								{else}
									<a href="{$link}" class="inp-group__link"{if $linkSeo->hasNofollow($object, ['filter' => $value->followingFilterParameters])} rel="nofollow"{/if}>
										{$value->name}
										{if $box->unit}
											{$box->unit}
										{/if}
									</a>
								{/if}
								{* <span n:if="!$value->isChecked && $value->count" class="inp-item__count">
									({if isset($box->isPlus) && $box->isPlus}+{/if}{$value->count})
								</span> *}
							</span>
						</label>
					</li>
				{/foreach}
			</ul>
		</div>
	{/block}
{/embed}