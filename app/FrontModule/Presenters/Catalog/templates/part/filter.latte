{default $class = false}
{default $showCalendarFilter = false}

{define submit}
	<span class="inp__btn">
		<button type="submit" class="btn btn--secondary btn--block--m">
			<span class="btn__text">{_"btn_search2"}</span>
		</button>
	</span>
{/define}

{define #textFilter}
	{var $selected = $filter->selectedParameters}
	<div class="u-pt-sm u-maw-5-12 u-mb-xs">
		<p class="inp inp--group u-mb-xs">
			<span class="inp__fix">
				<label for="filter[q]" class="inp__label inp__label--inside">{_'form_label_find'}</label>
				<input type="text" name="filter[q]" id="filter[q]" value="{$selected[q] ?? ''}" class="inp__text" placeholder="{_'form_label_find'}" data-controller="autosubmit" data-action="blur->autosubmit#submitForm">
			</span>
			{include #submit}
		</p>
	</div>
{/define}

<form action="{link $object, filter=>[], 'selectedTab' => $selectedTab ?? null, 'pager-page' => null}" n:class="f-filter, $class" data-controller="faculty" data-faculty-choices-outlet=".inp-faculty, .inp-programme, .inp-branch" data-naja data-naja-history="off" data-naja-loader="body" method="get">
	<input n:if="isset($cleanFilterParam['search'])" type="hidden" name="search" value="{$cleanFilterParam['search']}">
	<input n:if="isset($catalogOrder) && $catalogOrder != 'price'" name="order" type="hidden" value="{$catalogOrder}">

	{block before}
		{include #textFilter}
	{/block}

	<div class="grid grid--x--1 grid--y--1">
		{if isset($filter->boxes)}
			{foreach $filter->boxes as $name=>$box}
				<div class="grid__cell size--6-12@md size--4-12@lg size--3-12@xl">
					{block box}
						{if $box instanceOf App\Model\BucketFilter\Box\Slider}
							{include './boxes/slider.latte', box: $box}
						{else}
							{switch $box->name}
								{case faculty}
									{include './boxes/select.latte', box: $box, facultyFilter: true}
								{case programme}
									{include './boxes/select.latte', box: $box, groupBy: faculty, facultyFilter: true}
								{case branch}
									{include './boxes/select.latte', box: $box, groupBy: programme, facultyFilter: true}
								{default}
									{include './boxes/checkBoxes.latte', box: $box}
							{/switch}
						{/if}
					{/block}
				</div>
			{/foreach}
		{/if}
		{block next}{/block}
	</div>

	<p class="u-js-hide u-pt-sm u-mb-0">
		<button type="submit" class="btn btn--secondary btn--block--m">
			<span class="btn__text">{_"btn_filter"}</span>
		</button>
	</p>

	{block after}{/block}
</form>

