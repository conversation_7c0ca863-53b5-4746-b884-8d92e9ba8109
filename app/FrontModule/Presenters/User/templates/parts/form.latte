{block content}
	<div class="b-login holder holder--lg u-pt-md u-pt-xl@md u-pb-md u-pb-2xl@md">
		<div class="b-login__wrap u-mb-last-0">
			<div n:ifcontent class="u-mb-md">
				{if $object->uid == 'userLogin'} {* login *}
					{snippet signInForm}
						{control signInForm}
					{/snippet}
				{elseif $object->uid == 'registration'} {* registration *}
					<div class="u-mb-last-0 u-mb-md">
						<h1 class="b-login__title h2">{$object->name}</h1>
						<p n:if="$object->annotation ?? false">{$object->annotation|texy|noescape}</p>
					</div>

					<div data-controller="tabs">
						<nav class="m-tabs u-maw-5-12 u-mx-auto">
							<div class="m-tabs__wrap">
								<ul class="m-tabs__list">
									{define #tab}
										{default $active = false}
										<li class="m-tabs__item">
											<a href="#{$id}" class="m-tabs__link" role="tab" aria-controls="{$id}"{if $active} aria-selected="true"{/if} data-action="tabs#select" data-tabs-target="tab">
												<span class="m-tabs__inner">
													<span class="m-tabs__title">{_$nameLang}</span>
												</span>
											</a>
										</li>
									{/define}

									{php $tab = $_GET['tab'] ?? false}
									{include #tab, id: 'api', nameLang: 'registration_tab_api', active: !$tab || $tab == 'api'}
									{include #tab, id: 'manual', nameLang: 'registration_tab_manual', active: $tab && $tab == 'manual'}
								</ul>
							</div>
						</nav>
						<div id="api" role="tabpanel"{if $tab && $tab == 'manual'} aria-hidden="true"{/if} data-tabs-target="panel">
							{control registrationFormApi}
						</div>
						<div id="manual" role="tabpanel"{if !$tab || $tab == 'api'} aria-hidden="true"{/if} data-tabs-target="panel">
							{control registrationFormManual}
						</div>
					</div>
				{elseif $object->uid == 'lostPassword'} {* lost password *}
					{control lostPasswordForm}
				{elseif $object->uid == 'resetPassword'} {* reset password *}
					{control lostPasswordForm:reset}
				{/if}
			</div>

			{include $templates . '/part/box/content.latte', class: false}
		</div>
	</div>



{* {foreach $flashes as $flash}
	<div class="message message--{$flash->type}">{_$flash->message}</div>
{/foreach} *}
