{varType App\PostType\Profile\Model\Orm\ProfileLocalization $userProfileLocalization}
{varType App\Model\Pages $pages}

{block content}
{snippet flash}
	{control messageForForm, $flashes}
{/snippet}

{if $userProfile !== null}
	{embed $templates . '/part/box/profile.latte', titleTag: 'p', profileLocalization: $userProfileLocalization}
		{block header}
			{if $userProfileLocalization->isPublic()}
				<p>
					<a n:href="$userProfileLocalization" class="btn btn--sm btn--outline btn--white">
						<span class="btn__text">
							{_'btn_show_profile'}
						</span>
					</a>
				</p>
			{else}
				<p class="b-profile__text">
					{_"profile_not_public"}
				</p>
			{/if}
		{/block}

		{block content}
			<div class="b-profile__nav">
				{control userSideMenu}
			</div>
			<div class="b-profile__body">
				<div class="grid grid--x-0 grid--y-0">
					<div class="grid__cell size--5-12@md size--4-12@xl holder holder--lg b-profile__side">
						<h1 class="u-tt-u">{$object->name}</h1>
						<p>
							{$object->annotation}
						</p>

						<p n:if="$object->uid == 'userProfil'" n:class="$userProfileLocalization->score == 100 ? u-c-green : u-c-orange">
							{if $userProfileLocalization->score == 100}
								{_"profile_score_done"|replace:'%score', (string)$userProfileLocalization->score|noescape}
							{else}
								{_"profile_score_progress"|replace:'%score', (string)$userProfileLocalization->score|noescape}
							{/if}
						</p>
					</div>
					<div class="grid__cell size--7-12@md size--8-12@xl holder holder--lg b-profile__content u-mb-last-0">
						{include $templates . '/part/box/content.latte', limit: false, class: $object->uid == 'userDeactivate' ? 'u-mb-md' : 'u-mb-md u-mb-xl@md'}

						{switch $object->uid}
							{case userProfil}
								{control profileForm-avatar}
								{control profileForm-profile}
								{control changePasswordForm}
								<h2 class="u-mt-0 u-mb-md">
									{_"title_deactivate_account"}
								</h2>
								<p>
									{_"info_deactivate_account"}
								</p>
								<p class="u-mb-0">
									<a href="{plink $pages->userDeactivate}" class="btn" data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}' data-snippetid="snippet--content">
										<span class="btn__text">{_'user_deactivate_account'}</span>
									</a>
								</p>
							{case userVerification}
								{if !$userProfile->verified}
									{control verificationForm}
								{else}
									<p>
										{_'account_already_verified'}
									</p>
								{/if}
							{case userProfilInterests}
								{control profileForm-interests}
							{case userProfilNotifications}
								{control profileForm-notifications}
							{* {case userChangePassword}
								{control changePasswordForm} *}
							{case userContacts}
								{control profileContacts}
							{case userInvites}
								{control profileInvites}
							{case userDeactivate}
								{control userDeactivation}
						{/switch}

						{control customContentRenderer, [specLayout: true]}
					</div>
				</div>
			</div>
		{/block}
	{/embed}
{else}
	<div class="holder holder--lg u-pt-sm u-pt-lg@md">
		<p class="u-ta-c u-mb-sm u-mb-lg@md u-fw-b message message--warning u-maw-6-12 u-mx-auto">
			{_'account_not_asociated_to_profile'}
		</p>
	</div>
{/if}
