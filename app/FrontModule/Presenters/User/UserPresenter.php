<?php declare(strict_types=1);

namespace App\FrontModule\Presenters\User;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Components\VisualPaginator\VisualPaginator;
use App\FrontModule\Components\ChangePasswordForm\ChangePasswordForm;
use App\FrontModule\Components\ChangePasswordForm\ChangePasswordFormFactory;
use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Components\GoogleConnect\GoogleConnect;
use App\FrontModule\Components\GoogleConnect\GoogleConnectFactory;
use App\FrontModule\Components\GoogleLogin\GoogleLogin;
use App\FrontModule\Components\GoogleLogin\GoogleLoginFactory;
use App\FrontModule\Components\LostPasswordForm\LostPasswordForm;
use App\FrontModule\Components\LostPasswordForm\LostPasswordFormFactory;
use App\FrontModule\Components\OrderHistory\OrderHistory;
use App\FrontModule\Components\OrderHistory\OrderHistoryFactory;
use App\FrontModule\Components\ProfileForm\ProfileForm;
use App\FrontModule\Components\ProfileForm\ProfileFormFactory;
use App\FrontModule\Components\ProfileInvites\ProfileInvites;
use App\FrontModule\Components\ProfileInvites\ProfileInvitesFactory;
use App\FrontModule\Components\RegistrationForm\RegistrationForm;
use App\FrontModule\Components\RegistrationForm\RegistrationFormFactory;
use App\FrontModule\Components\UserAddressForm\UserAddressForm;
use App\FrontModule\Components\UserAddressForm\UserAddressFormFactory;
use App\FrontModule\Components\ProfileContacts\ProfileContacts;
use App\FrontModule\Components\ProfileContacts\ProfileContactsFactory;
use App\FrontModule\Components\UserDeactivation\UserDeactivation;
use App\FrontModule\Components\UserDeactivation\UserDeactivationFactory;
use App\FrontModule\Components\UserSideMenu\UserSideMenu;
use App\FrontModule\Components\UserSideMenu\UserSideMenuFactory;
use App\FrontModule\Components\VerificationForm\VerificationForm;
use App\FrontModule\Components\VerificationForm\VerificationFormFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\Routable;
use App\Model\Orm\User\UserModel;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserHash\UserHashModel;
use Nette\Application\Attributes\Persistent;
use Nette\Application\UI\Multiplier;

/**
 * @property Tree $object
 * @method Tree getObject()
 */
final class UserPresenter extends BasePresenter
{
	use HasCustomContentRenderer;

	#[Persistent]
	public string $backlink = '';

	#[Persistent]
	public ?string $loginback;

	#[Persistent]
	public ?string $tab;

	public function __construct(
		private readonly ProfileFormFactory $profileFormFactory,
		private readonly ProfileContactsFactory $profileContactsFactory,
		private readonly ProfileInvitesFactory $profileInvitesFactory,
		private readonly UserDeactivationFactory $userDeactivationFactory,
		private readonly ChangePasswordFormFactory $changePasswordFormFactory,
		private readonly LostPasswordFormFactory $lostPasswordFormFactory,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly RegistrationFormFactory $registrationFormFactory,
		private readonly VerificationFormFactory $verificationFormFactory,
		private readonly UserSideMenuFactory $userSideMenuFactory,
		private readonly UserModel $userModel,
		private readonly UserHashModel $userHashModel,
		private readonly GoogleConnectFactory $googleConnectFactory,
		private readonly GoogleLoginFactory $googleLoginFactory,
		private readonly UserAddressFormFactory $userAddressFormFactory,
		private readonly OrderHistoryFactory $orderHistoryFactory,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();
		$this->setObject($this->orm->tree->getById($this->params['idref']));

		// Redirect back to this stored page after login...
		if ($this->user->isLoggedIn() && !empty($this->loginback)) {
			$loginBackObject = $this->decodeObjectTarget($this->loginback);
			$this->loginback = null;
			if ($loginBackObject instanceof Routable) {
				$this->redirect($loginBackObject);
			}
		}
	}


	public function actionDefault(): void
	{
		if ($this->user->loggedIn) {
			if ($this->isAjax()) {
				$this->redrawControl();
			}
		} else {
			$this->redirectToLoginPage();
		}
	}


	public function actionLogin(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfileDashboard();
		}

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function actionLogout(): void
	{

	}

	public function actionLostPassword(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}
	}


	public function actionProfil(): void
	{
		if (!$this->user->loggedIn) {
			$this->redirectToLoginPage();
		}
	}


	public function actionRegistration(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfileDashboard();
		}
	}


	public function actionResetPassword(?string $hashToken): void
	{
		if ($hashToken) {

			$userHash = $this->userHashModel->getHash($hashToken, UserHash::TYPE_LOST_PASSWORD);
			if ($userHash && $userHash->isValid()) {

			} else {

				$this->getComponent('lostPasswordForm')->flashMessage($this->translator->translate("reset_password_expired_link"), "error");
				$this->redirect($this->mutation->pages->lostPassword);
			}

		} else {
			$this->getComponent('lostPasswordForm')->flashMessage($this->translator->translate("reset_password_expired_link"), "error");
			$this->redirect($this->mutation->pages->lostPassword);
		}

	}

	public function actionReactivation(?string $hashToken): void
	{
		if ($hashToken) {
			$userHash = $this->userHashModel->getHash($hashToken, UserHash::TYPE_REACTIVATION);
			if ($userHash && $userHash->isValid()) {
				$this->userModel->activate(user: $userHash->user, publishProfile: true);
				$this->userModel->update($userHash->user);

				$this['signInForm']->flashMessage('account_reactivated');
				$this->redirect($this->mutation->pages->userLogin);
			}
		}

		$this->redirect($this->mutation->pages->title);
	}

	public function actionOrderHistory(string $orderHash = null): void
	{
		if (isset($orderHash)) { // detail
			if ($this->user->loggedIn) {
				$order = $this->orm->order->getBy([
					'hash' => $orderHash,
					'user' => $this->userEntity,
				]);
			} else {
				$order = $this->orm->order->getBy([
					'hash' => $orderHash,
				]);
			}
			$this->template->order = $order;
		} elseif (!$this->user->loggedIn) { // list
			$this->redirectToLoginPage();
		}

		if ($this->user->loggedIn) {
			if ($this->isAjax()) {
				$this->redrawControl('userOrderHistoryArea');
				$this->redrawControl('orderHistoryControl');
			}
		}
	}

	public function renderDeactivate(): void
	{
		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}


	public function handleRepeatOrder(int $orderId): void
	{
		$order = $this->orm->order->getById($orderId);

		if (!isset($order)) {
			$this->error('Invalid order ID');
		}

		if ($order->user->id === $this->userEntity->id) {
			foreach ($order->products as $orderItem) {
				// TODO:
				//$this->basket->addProductVariant($orderItem->variant, $orderItem->amount);
			}
		}

		$this->redirect($this->mutation->pages->step1);
	}

	public function createComponentVp(): VisualPaginator
	{
		return $this->visualPaginatorFactory->create();
	}


	/**
	 * presmerovani na prihlasovaci stranku, v pripade pristupu do zabezpecene sekce
	 */
	private function redirectToLoginPage(): never
	{
		$this->redirectToUserPage('userLogin');
	}


	/**
	 * presmerovani na detail uziv. sekce, pri pristupu na registraci, zapomenute heslo a prihlaseni
	 */
	private function redirectToProfilePage(): never
	{
		$this->redirectToUserPage('userProfil');
	}

	private function redirectToProfileDashboard(): never
	{
		$this->redirectToUserPage('userSection');
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}


	protected function createComponentUserSideMenu(): UserSideMenu
	{
		return $this->userSideMenuFactory->create($this->object);
	}

	private function createComponentRegistrationForm(string $renderMode): RegistrationForm
	{
		$registrationForm = $this->registrationFormFactory->create($this->object);
		if ($this->getInvitesSession()->id) {
			$registrationForm->setProfileInvite($this->orm->profileInvite->getById($this->getInvitesSession()->id));
		}
		$registrationForm->renderMode = $renderMode;

		return $registrationForm;
	}

	protected function createComponentRegistrationFormManual(): RegistrationForm
	{
		return $this->createComponentRegistrationForm(renderMode: RegistrationForm::RENDER_MODE_MANUAL);
	}

	protected function createComponentRegistrationFormApi(): RegistrationForm
	{
		return $this->createComponentRegistrationForm(renderMode: RegistrationForm::RENDER_MODE_API);
	}

	protected function createComponentVerificationForm(): VerificationForm
	{
		return $this->verificationFormFactory->create(
			object: $this->getObject(),
			profileLocalization: $this->getUserProfileLocalization(),
		);
	}

	protected function createComponentProfileForm(): Multiplier
	{
		return new Multiplier(function (string $renderMode): ProfileForm {
			return $this->profileFormFactory->create(
				object: $this->getObject(),
				profileLocalization: $this->getUserProfileLocalization(),
				renderMode: $renderMode,
				renderParameters: [
					'showSaveButton' => $renderMode !== 'avatar',
				],
			);
		});
	}


	protected function createComponentChangePasswordForm(): ChangePasswordForm
	{
		return $this->changePasswordFormFactory->create($this->object, $this->userEntity);
	}

	protected function createComponentLostPasswordForm(): LostPasswordForm
	{
		$hash = NULL;
		$params = $this->getParameters();
		if (isset($params['hashToken'])) {
			$hash = $params['hashToken'];
		}
		return $this->lostPasswordFormFactory->create($this->mutation, $this->object, $hash);
	}

	protected function createComponentProfileContacts(): ProfileContacts
	{
		return $this->profileContactsFactory->create(
			object: $this->getObject(),
			profileLocalization: $this->getUserProfileLocalization(),
			esIndex: $this->orm->esIndex->getCommonLastActive($this->mutation),
		);
	}

	protected function createComponentProfileInvites(): ProfileInvites
	{
		return $this->profileInvitesFactory->create(
			object: $this->getObject(),
			profileLocalization: $this->getUserProfileLocalization(),
		);
	}

	protected function createComponentUserDeactivation(): UserDeactivation
	{
		return $this->userDeactivationFactory->create(
			object: $this->getObject(),
			user: $this->userEntity,
		);
	}

	// ----------------------------------------------------------------
	// Not used components

	protected function createComponentGoogleLogin(): GoogleLogin
	{
		$googleLogin = $this->googleLoginFactory->create($this->mutation);

		$googleLogin->onLogin[] = fn() => $this->redirect($this->mutation->pages->userSection);

		$googleLogin->onEmailTaken[] = function () {
			$this->flashMessage('msg_info_google_login_email_taken', 'error');
			$this->redirect('this');
		};

		$googleLogin->onError[] = function () {
			$this->flashMessage('msg_info_google_login_error', 'error');
			$this->redirect('this');
		};

		return $googleLogin;
	}

	protected function createComponentGoogleConnect(): GoogleConnect
	{
		$googleConnect = $this->googleConnectFactory->create($this->mutation);

		$googleConnect['googleLogin']->onLogin[] = fn() => $this->redirect('this');

		$googleConnect['googleLogin']->onEmailTaken[] = function () {
			$this->flashMessage('msg_info_google_login_email_taken', 'error');
			$this->redirect('this');
		};

		$googleConnect['googleLogin']->onError[] = function () {
			$this->flashMessage('msg_info_google_login_error', 'error');
			$this->redirect('this');
		};

		return $googleConnect;
	}

	public function createComponentUserAddressForm(): UserAddressForm
	{
		return $this->userAddressFormFactory->create($this->getObject(), $this->userEntity);
	}

	public function createComponentOrderHistory(): OrderHistory
	{
		return $this->orderHistoryFactory->create($this->getObject(), $this->userEntity, $this->mutation);
	}

}
