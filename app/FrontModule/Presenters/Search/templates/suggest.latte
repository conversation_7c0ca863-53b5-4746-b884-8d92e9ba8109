{if $hasResults}
	{if $trees->count()}
		<div class="b-suggest__group">
			<h2>
				{_search_tab_trees} {$trees->count()}
			</h2>
			<ul>
				{foreach $trees as $page}
					{breakIf $iterator->getCounter() > 5}
					<li>
						<a href="{link $page}">
							{$page->nameAnchor}
						</a>
					</li>
				{/foreach}
			</ul>
			{if count($trees) > 5}
				<p>
					<a href="{link $pages->search, 'search' => $search}">
						{_search_show_all} {$trees->count()}
					</a>
				</p>
			{/if}
		</div>
	{/if}

	{if $blogs->count()}
		<div class="b-suggest__group">
			<h2>
				{_search_tab_blogs} {$blogs->count()}
			</h2>
			<ul>
				{foreach $blogs as $page}
					{breakIf $iterator->getCounter() > 5}
					<li>
						<a href="{link $page}">
							{$page->nameAnchor}
						</a>
					</li>
				{/foreach}
			</ul>
			{if count($blogs) > 5}
				<p>
					<a href="{link $pages->search, 'search' => $search}">
						{_search_show_all} {$blogs->count()}
					</a>
				</p>
			{/if}
		</div>
	{/if}
	{if $products->count()}
		<div class="b-suggest__group">
			<h2>
				{_search_tab_blogs} {$products->count()}
			</h2>
			<ul>
				{foreach $products as $product}
					{breakIf $iterator->getCounter() > 5}
					<li>
						<a href="{link $product}">
							{$product->nameAnchor}
						</a>
					</li>
				{/foreach}
			</ul>
			{if count($products) > 5}
				<p>
					<a href="{link $pages->search, 'search' => $search}">
						{_search_show_all} {$products->count()}
					</a>
				</p>
			{/if}
		</div>
	{/if}
{else}
	<h2>
		{_search_title}
	</h2>
	<p>
		{_search_nothing}
	</p>
{/if}

{* <div class="c-autocomplete">
	<ul class="c-autocomplete__list">
		<li class="c-autocomplete__item">Sekretariát <span class="c-autocomplete__highlight">rektor</span>a</li>
		<li class="c-autocomplete__item">Pro<span class="c-autocomplete__highlight">rektor</span>ka pro zahraniční vztahy</li>
		<li class="c-autocomplete__item">Sekretariát <span class="c-autocomplete__highlight">rektor</span>a</li>
	</ul>
	<ul class="c-autocomplete__list c-autocomplete__list--persons">
		<li class="c-autocomplete__item c-autocomplete__item--person">
			<a href="#" class="b-autocomplete-person">
				<div class="b-autocomplete-person__img">
					<img src="/static/img/illust/sample.jpg" width="40" height="40" alt="">
				</div>
				<div class="b-autocomplete-person__content">
					<p class="b-autocomplete-person__name">Hermannová Renata, Ing.</p>
					<p class="b-autocomplete-person__position">Vedoucí marketingu a vnějších vztahů</p>
				</div>
			</a>
		</li>
	</ul>
</div> *}

