{default $query = ""}
{block content}
	{snippet tabContent}
		{embed $templates.'/part/box/intro.latte', object: $object}
			{block content}
				<div class="u-pt-sm u-maw-9-12">
					<form action='{link $pages->search, tab=>$selectedTab, suggest=>0, search=>null, filter=>null}' id="form-search" data-naja data-naja-loader="body">
						<p class="inp inp--group u-mb-sm u-maw-5-12">
							<span class="inp__fix">
								<label for="search" class="inp__label inp__label--inside">{_'form_label_search'}</label>
								<input type="text" name="search" value="{$search ?? false}" class="inp__text" placeholder="{_'form_label_search'}" data-controller="autosubmit" data-action="blur->autosubmit#submitForm">
							</span>
							<button type="submit" class="btn btn--secondary">
								<span class="btn__text">
									{_"btn_search2"}
								</span>
							</button>
						</p>
					</form>
					{include './parts/menu.latte'}
				</div>
			{/block}
		{/embed}
		{include $templates . '/part/box/content.latte'}

		<div class="u-mb-last-0 u-mb-md u-mb-xl@md holder holder--lg">
			{switch $selectedTab}
				{case pages}
					{include #results, sections: (object) [
						'trees' => $trees,
						'profiles' => $profiles,
						'blogs' => $blogs,
						'events' => $events,
						'stories' => $stories,
						'benefits' => $benefits,
					]}
				{case profiles}
					{include #results, sections: (object) ['profiles' => $profiles]}
				{* todo poptávky *}
			{/switch}
		</div>
	{/snippet}
{/block}

{define #results}
	{php $hasResults = false}
	<ul n:ifcontent class="c-subjects">
		{foreach $sections as $name=>$section}
			{if $section->count()}
				{include 'parts/tabs/'. $name .'.latte', name: $name}
				{php $hasResults = true}
			{/if}
		{/foreach}
	</ul>
	{if !$hasResults}
		<p>{_"no_results"}</p>
	{/if}
{/define}
