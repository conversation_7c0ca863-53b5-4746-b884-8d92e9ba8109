<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Search;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\BucketFilter\BucketFilterFactory;
use App\Model\BucketFilter\Result;
use App\Model\BucketFilter\SetupCreator\Search\BasicElasticItemListFactory;
use App\Model\BucketFilter\SetupCreator\Search\BoxListFactory;
use App\Model\BucketFilter\SetupCreator\Search\ElasticItemListFactory;
use App\Model\BucketFilter\SortCreator;
use App\Model\ElasticSearch\Common\ElasticCommon;
use App\Model\ElasticSearch\Common\Repository;
use App\Model\ElasticSearch\Common\ResultReader;
use App\Model\Link\LinkSeo;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use Elastica\Query\Term;
use Elastica\ResultSet;
use Nette\Application\Attributes\Persistent;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read CommonTree $object
 */
final class SearchPresenter extends BasePresenter
{
	private const string TAB_PAGES = 'pages';
	private const string TAB_PROFILES = 'profiles';

	#[Persistent]
	public string $search;

//	private array $filterParams;

	public function __construct(
		private readonly ResultReader $commonResultReader,
		private readonly Repository $esCommonRepository,
//		private readonly BucketFilterFactory $bucketFilterFactory,
		private readonly SortCreator $sortCreator,
		private readonly LinkSeo $linkSeo,
//		private readonly BasicElasticItemListFactory $basicElasticItemListFactory,
//		private readonly ElasticItemListFactory $elasticItemListFactory,
//		private readonly BoxListFactory $boxListFactory,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();
		$this->setObject($this->params['object']);
	}


	public function actionDefault(CommonTree $object, ?string $search = null, bool $suggest = false, array $filter = [], ?string $tab = null): void
	{
		$this->setObject($object);
//		$this->filterParams = $filter;

		if ($search === null) {
			$search = $this->request->getPost('search');
		}

		if ($search === null) {
			$this->redirect($this->mutation->pages->title);
		}

		$this->search = $search;
	}

	public function renderDefault(?string $search = null, bool $suggest = false, array $filter = [], ?string $tab = null): void
	{
		// TODO too much commented mess, delete ?

		$tab = $tab ?? self::TAB_PAGES;

		$sort = $this->sortCreator->create('score');//$this->currentState, $this->priceLevel

		if ($search === null) {
			$search = $this->request->getPost('search');
		}

		$typeKeys = [
			ElasticCommon::TYPE_TREE,
			ElasticCommon::TYPE_BLOG,
			ElasticCommon::TYPE_CALENDAR,
			ElasticCommon::TYPE_BENEFIT,
			ElasticCommon::TYPE_STORY,
			ElasticCommon::TYPE_PROFILE,
		];

		$resultSets = [];
		$collections = [];
		foreach ($typeKeys as $typeKey) {
			$collections[$typeKey] = new EmptyCollection();
		}

		$itemsObject = Result::from(
			new EmptyCollection(),
			0,
			0,
		);

		/*$this->template->resultsCount = 0;
		$minScore = 1;
		$basicElasticItemListGenerator = $this->basicElasticItemListFactory->create($this->mutation->pages, $search);
		$elasticItemListGenerator = $this->elasticItemListFactory->create($this->filterParams, $this->currentState, $this->priceLevel);
		$boxListGenerator = $this->boxListFactory->create($this->mutation->pages);*/

		/*$bucketFilter = $this->bucketFilterFactory->create(
			basicElasticItemListGenerator: $basicElasticItemListGenerator,
			elasticItemListGenerator: $elasticItemListGenerator,
			boxListGenerator: $boxListGenerator,
			resultReader: $this->commonResultReader,
			esIndex: $this->orm->esIndex->getProductLastActive($this->mutation),
			minScore: $minScore
		);*/

		if ($search) {
			$search = trim($search);

			$mutation = $this->object->mutation;
			$this->template->search = $search;

//			if ($suggest) {
//				$treeMust = [
//					new Term(['type' => 'tree']),
//					new Term(['subType' => Tree::TYPE_COMMON]),
//					new Term(['public' => true]),
//					new Term(['hide' => false]),
//				];
//				$treeResults = $this->esCommonRepository->fulltextSearch($mutation, $treeMust, $search, 10);
//
//				$blogMust = [
//					new Term(['type' => 'blog']),
//					new Term(['public' => true]),
//				];
//				$blogResults = $this->esCommonRepository->fulltextSearch($mutation, $blogMust, $search, 10);
//
//				$itemsObject = $bucketFilter->getItems(30, 0, $sort);
//
//			} else {
				/*$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
				$this['pager']->object = $this->getObject();

				if (isset($this->params['more'])) {
					$this['pager']->setStartPage($this->params['more']);
				}

				$paginator = $this['pager']->getPaginator();
				$paginator->itemsPerPage = $this->configService->get('shop', 'productsPaging');*/

				// ---------------------------------------------------------------
				// Results

				if ($tab === self::TAB_PAGES) {
					// All results = posttypes (including profiles) + trees

					$resultSets[ElasticCommon::TYPE_TREE] = $this->esCommonRepository->fulltextSearch(
						mutation: $mutation,
						must: [
							new Term(['type' => ElasticCommon::TYPE_TREE]),
							new Term(['subType' => Tree::TYPE_COMMON]),
							new Term(['public' => true]),
							new Term(['hide' => false]),
						],
						q: $search,
						size: 30,
					);

					foreach ([
							 ElasticCommon::TYPE_PROFILE,
							 ElasticCommon::TYPE_BLOG,
							 ElasticCommon::TYPE_CALENDAR,
							 ElasticCommon::TYPE_BENEFIT,
							 ElasticCommon::TYPE_STORY,
						] as $typeKey) {
						$resultSets[$typeKey] = $this->esCommonRepository->fulltextSearch(
							mutation: $mutation,
							must: [
								new Term(['type' => $typeKey]),
								new Term(['public' => true]),
							],
							q: $search,
							size: 10,
						);
					}
				} elseif ($tab === self::TAB_PROFILES) {
					// Profiles only

					$resultSets[$typeKey] = $this->esCommonRepository->fulltextSearch(
						mutation: $mutation,
						must: [
							new Term(['type' => ElasticCommon::TYPE_PROFILE]),
							new Term(['public' => true]),
						],
						q: $search,
						size: 30,
					);
				}

//				$filter = $bucketFilter->getFilter($this->filterParams);
//				$itemsObject = $bucketFilter->getItems($paginator->itemsPerPage, $paginator->offset, $sort);
//				$paginator->itemCount = $itemsObject->totalCount;

//				$this->template->filter = $filter;
//			}

			foreach ($resultSets as $type => $resultSet) {
				$collections[$type] = $this->commonResultReader->mapResultToEntityCollection($resultSet, $type, 10);
			}
		}

//		$this->template->showProductFilter = ($this->filterParams) || $itemsObject->totalCount;

//		$collectionsCounts = array_map(fn(ICollection $collection): int => count($collection), $collections);
//		$collectionsSum = array_sum($collectionsCounts);

		$this->template->tabs = [
			self::TAB_PAGES,
			self::TAB_PROFILES,
		];
		$this->template->selectedTab = $tab;

//		$this->template->resultsCount = array_sum($this->template->tabs);

		$this->template->trees = $collections[ElasticCommon::TYPE_TREE];
		$this->template->blogs = $collections[ElasticCommon::TYPE_BLOG];
		$this->template->events = $collections[ElasticCommon::TYPE_CALENDAR];
		$this->template->stories = $collections[ElasticCommon::TYPE_STORY];
		$this->template->benefits = $collections[ElasticCommon::TYPE_BENEFIT];
		$this->template->profiles = $collections[ElasticCommon::TYPE_PROFILE];

//		$this->template->products = $itemsObject->items;
//		$this->template->countProducts = $itemsObject->totalCount;

		$this->template->search = $this->search;
//		$this->template->cleanFilterParam = $this->filterParams;
		$this->template->linkSeo = $this->linkSeo;

		/*if ($suggest) {
			$this->setView('suggest');
		}*/

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

}
