<?php declare(strict_types=1);

namespace App\FrontModule\Presenters;

use App\Exceptions\LogicException;
use App\FrontModule\Components\AntispamSession;
use App\FrontModule\Components\Breadcrumb\Breadcrumb;
use App\FrontModule\Components\Breadcrumb\BreadcrumbFactory;
use App\FrontModule\Components\CanonicalUrl\CanonicalUrl;
use App\FrontModule\Components\CanonicalUrl\CanonicalUrlFactory;
use App\FrontModule\Components\ClassmateTipForm\ClassmateTipFormControl;
use App\FrontModule\Components\ClassmateTipForm\ClassmateTipFormControlFactory;
use App\FrontModule\Components\CommunityTipForm\CommunityTipFormControl;
use App\FrontModule\Components\CommunityTipForm\CommunityTipFormControlFactory;
use App\FrontModule\Components\CommunityTipForm\CommunityTipFormFactory;
use App\FrontModule\Components\ContactForm\ContactFormControl;
use App\FrontModule\Components\ContactForm\ContactFormControlFactory;
use App\FrontModule\Components\EditButton\EditButton;
use App\FrontModule\Components\EditButton\EditButtonFactory;
use App\FrontModule\Components\LangSwitcherForm\LangSwitcherForm;
use App\FrontModule\Components\LangSwitcherForm\LangSwitcherFormFactory;
use App\FrontModule\Components\Menu\Menu;
use App\FrontModule\Components\Menu\MenuFactory;
use App\FrontModule\Components\NewsletterForm\NewsletterFormControl;
use App\FrontModule\Components\NewsletterForm\NewsletterFormControlFactory;
use App\FrontModule\Components\PodcastTipForm\PodcastTipFormControl;
use App\FrontModule\Components\PodcastTipForm\PodcastTipFormControlFactory;
use App\FrontModule\Components\ProfileFollow\HasProfileFollow;
use App\FrontModule\Components\Robots\Robots;
use App\FrontModule\Components\Robots\RobotsFactory;
use App\FrontModule\Components\SignInForm\SignInForm;
use App\FrontModule\Components\SignInForm\SignInFormFactory;
use App\FrontModule\Components\SocialsTipForm\SocialsTipFormControl;
use App\FrontModule\Components\SocialsTipForm\SocialsTipFormControlFactory;
use App\FrontModule\Components\ToggleLanguage\ToggleLanguage;
use App\FrontModule\Components\ToggleLanguage\ToggleLanguageFactory;
use App\FrontModule\Components\UserMenu\UserMenu;
use App\FrontModule\Components\UserMenu\UserMenuFactory;
use App\FrontModule\Presenters\Homepage\HomepagePresenter;
use App\Infrastructure\BasicAuth\Authenticator;
use App\Infrastructure\Latte\Filters;
use App\Model\CustomField\LazyValue;
use App\Model\ImageResizerWrapper;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationDetector;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Alias\Alias;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Routable;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\State\State;
use App\Model\PagesFactory;
use App\PostType\Core\Model\ExpandedPublishable;
use App\PostType\Core\Model\Publishable;
use App\PostType\Faculty\Model\Orm\Faculty;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeModel;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Profile\Model\Orm\Profile;
use App\PostType\Profile\Model\Orm\ProfileFollowing;
use App\PostType\Profile\Model\Orm\ProfileInvite;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\PostType\Profile\Model\Orm\ProfileRepository;
use Nette;
use Nette\Application\AbortException;
use Nette\Application\Attributes\Persistent;
use Nette\Application\Request;
use Nette\Application\UI\InvalidLinkException;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Caching\Cache;
use Nette\DI\Attributes\Inject;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nette\Http\SessionSection;
use Tracy\Debugger;
use function assert;

/**
 * @property Routable|StaticPage $object
 * @property-read DefaultTemplate $template
 */
abstract class BasePresenter extends \App\BasePresenter
{
	use HasProfileFollow;

	#[Persistent]
	public string $alias;

	#[Persistent]
	public ?string $invite;

	#[Inject]
	public Nette\Http\RequestFactory $requestFactory;

	#[Inject]
	public ToggleLanguageFactory $toggleLanguageFactory;

	#[Inject]
	public Nette\Http\Session $session;

	#[Inject]
	public Nette\Caching\Storage $cacheStorage;

	#[Inject]
	public TranslatorDB $translator;

	#[Inject]
	public PagesFactory $pagesFactory;

	#[Inject]
	public MutationHolder $mutationHolder;

	#[Inject]
	public MutationDetector $mutationDetector;

	#[Inject]
	public LinkFactory $linkFactory;

	#[Inject]
	public ImageResizerWrapper $imageResizerWrapper;

	#[Inject]
	public ContactFormControlFactory $contactFormFactory;

	#[Inject]
	public ClassmateTipFormControlFactory $classmateTipFormFactory;

	#[Inject]
	public SocialsTipFormControlFactory $socialsTipFormFactory;

	#[Inject]
	public CommunityTipFormControlFactory $communityTipFormFactory;

	#[Inject]
	public PodcastTipFormControlFactory $podcastTipFormFactory;

	#[Inject]
	public NewsletterFormControlFactory $newsletterFormFactory;

	#[Inject]
	public SignInFormFactory $signInFormFactory;

	#[Inject]
	public BreadcrumbFactory $breadcrumbFactory;

	#[Inject]
	public MenuFactory $menuFactory;

	#[Inject]
	public UserMenuFactory $userMenuFactory;

	#[Inject]
	public RobotsFactory $robotsFactory;

	#[Inject]
	public CanonicalUrlFactory $canonicalUrlFactory;

	#[Inject]
	public TreeModel $treeModel;

	#[Inject]
	public EditButtonFactory $editButtonFactory;

	protected Routable|StaticPage $object;

	protected Mutation $mutation;

	protected Cache $cache;

	/**
	 * state = kolize s persistant v NewsletterPresenter
	 */
	protected State $currentState;

	protected PriceLevel $priceLevel;

	#[Inject]
	public Authenticator $basicAuth;

	#[Inject]
	public AntispamSession $antispamSession;

	protected bool $bypassBasicAuth = false;

	protected ?Profile $userProfile = null;

	public array $parameters = [];

	/** @var array|null Info for render methods about signal processed in this runtime
	 * Useful for AJAX snippet redraw logic
	 */
	protected ?array $signalProcessed;

	protected function startup(): void
	{
		// ******* redirects ************************
		if (isset($_GET['terminate'])) {
			$this->terminate();
		}

		if ($this->request->hasFlag(Request::RESTORED)) { // reseni pro backlink po ajaxu
			$this->redirect('this');
		}

		// ******* basic ************************
		parent::startup();

		$this->dbaLog->register();
		$this->setMutation();
		$this->setState();
		$this->setPriceLevel();
		$this->cache = new Cache($this->cacheStorage);

//		$this->autoCanonicalize = FALSE;

		$this->signalProcessed = $this->getSignal();

		// ******* helpers & templates ************************
		Filters::$mutation = $this->mutationHolder->getMutation();
		Filters::$translator = $this->translator;
		Filters::$version = $this->configService->get('webVersion');

		// prepsani defaultnich hlasek
		Nette\Forms\Validator::$messages[Nette\Forms\Form::EMAIL] = 'form_valid_email';
		Nette\Forms\Validator::$messages[Nette\Forms\Form::FILLED] = 'form_valid_filled';

		// ******* profile ************************

		$this->userProfile = $this->userEntity?->profile;

		// ******* profile invites ************************

		if (!empty($this->invite)) {
			/** @var ProfileInvite $profileInvite */
			$profileInvite = $this->orm->profileInvite->findBy([
				'hash' => $this->invite,
				'sent!=' => null,
				'visited' => null,
			])->fetch();
			if ($profileInvite !== null) {
				$profileInvite->visited = new DateTimeImmutable();
				$this->orm->profileInvite->persistAndFlush($profileInvite);
				$this->getInvitesSession()->id = $profileInvite->id;
			}

			$this->invite = null;
		}
	}

	protected function getUserProfileLocalization(): ?ProfileLocalization
	{
		return $this->userProfile?->getLocalization($this->mutationHolder->getMutation());
	}

	protected function getInvitesSession(): SessionSection
	{
		return $this->getSession('invites');
	}


	public function checkRequirements(mixed $element): void
	{
		parent::checkRequirements($element);

		if (!$element instanceof Nette\Application\UI\ComponentReflection) {
			return;
		}

		if ($this->bypassBasicAuth) {
			return;
		}

		$this->basicAuth->authenticate(
			$this->getHttpRequest(),
			function (): never {
				$this->getHttpResponse()->setHeader('WWW-Authenticate', 'Basic realm="app"');
				if (Debugger::isEnabled()) {
					$this->terminate();
				} else {
					$this->error(httpCode: Nette\Http\IResponse::S401_Unauthorized);
				}
			},
		);
	}

	protected function setMutation(): void
	{
		if ($this->getParameter('mutation')) {
			$this->mutation = $this->getParameter('mutation');
		} else {
			$this->mutation = $this->mutationDetector->detect();
		}

		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setMutation($this->mutation);
	}


	/**
	 * Urceni statu podle cookie (user si nekde zvolil stat)
	 * nebo defaultne prvni stat mutace
	 *
	 * @throws LogicException
	 */
	protected function setState(): void
	{
		$currentState = null;
		$idState = (int)$this->getHttpRequest()->getCookie(State::COOKIE_NAME_SELECTED_STATE);

		if ($idState) {
			$currentState = $this->orm->state->getById($idState);
		}

		if (!$currentState) {
			$currentState = $this->orm->state->getDefault($this->mutation);
		}

		if (!$currentState) {
			throw new LogicException('Unknown current state');
		}

		$this->currentState = $currentState;
	}

	protected function setPriceLevel(): void
	{
		$priceLevelId = $this->userEntity?->priceLevel->id ?? PriceLevel::DEFAULT_ID;
		$this->priceLevel = $this->orm->priceLevel->getById($priceLevelId);
	}

	protected function beforeRender(): void
	{
		parent::beforeRender();

		$this->antispamSession->prepareAntispam();

		// ******* basic ************************
		$this->template->setTranslator($this->translator);
		$this->template->mutation = $this->mutationHolder->getMutation();
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->layout = FE_TEMPLATE_DIR . '/@layout.latte';
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->appDir = $this->parameters['appDir'];
		$this->template->filterTemplate = $this->parameters['appDir'] . '/FrontModule/Presenters/Catalog/templates/part/filter.latte';
		$this->template->isHomepage = $this instanceof HomepagePresenter;
		//  robots per mutation
		if ($this->mutation->langCode) {
			$this->template->currencyCode = $this->mutation->currency; //"CZK";
		}

		$this->template->object = $this->getObject();
		$this->template->pages = $this->mutationHolder->getMutation()->pages;
		$this->template->state = $this->currentState;
		$this->template->priceLevel = $this->priceLevel;

		// ******* callbacks ********************
		$this->template->getImage = function ($entity, $size) {
			return $this->imageResizerWrapper->getResizedImage($entity, $size);
		};

		$this->template->cfg = function () {
			return call_user_func_array([$this->configService, 'get'], func_get_args());
		};

		// ******* other ********************
		if (isset($this->object->template) && $this->object->template) {
			$_presenterName = explode(':', $this->object->template);
			if (isset($_presenterName[0])) {
				if (isset($this->object->parent) && $this->object->parent === null) {
					$this->template->presenterName = 'Homepage';
				} else {
					$this->template->presenterName = $_presenterName[0];
				}
			}
		}

		$this->template->googleAnalyticsCode = $this->mutationHolder->getMutation()->getGACode();
		$this->template->googleApiKey = $this->configService->getParam('google', 'apiKey');
//		$this->setTemplateMeasureIp();

		// ******* publish ********************

		$this->template->isContentForRegistered = $this->isContentForRegistered();
		$this->template->isContentForVerified = $this->isContentForVerified();

		$this->template->addFilter('encodeTarget', $this->encodeObjectTarget(...));

		$this->template->addFilter('getObjectExternalLink', $this->getObjectExternalLink(...));

		// ******* profile ********************

		$this->template->userProfile = $this->userProfile;
		$this->template->userProfileLocalization = $this->getUserProfileLocalization();

		$this->template->canViewVerifiedContent = $this->userProfile?->verified || $this->user->isManager();

		// Is profile section visible based on visibility settings?
		$this->template->addFilter(
			name: 'sectionVisible',
			callback: fn(Profile $profile, string $section): bool => $this->isProfileSectionVisible($profile, $section)
		);
	}

	protected function handleMeasureIp(): void
	{
		$this->template->measureIp = base64_encode($this->configService->get('REMOTE_ADDR'));

		if ($this->userEntity && isset($this->userEntity->id)) {
			$this->template->measureUserId = $this->userEntity->id;
			if ($this->userEntity->createdTime) {
				$this->template->measureUserCreated = $this->userEntity->createdTime->format('Y-d-m');
			} else {
				$this->template->measureUserCreated = null;
			}
		} else {
			$this->template->measureUserId = null;
			$this->template->measureUserCreated = null;
		}
	}


	public function setObject(Routable|StaticPage $object): void
	{
		$this->object = $object;
	}


	public function getObject(): Routable|StaticPage
	{
		return $this->object;
	}

	public function handleLogout(): void
	{
		$this->getUser()->logout(true);
		$this->flashMessage('msg_info_logout');
		$this->redirectToUserPage('userLandingPage');
	}

	public function isContentForRegistered(): bool
	{
		$object = $this->getObject();
		return $object instanceof ExpandedPublishable && $object->isForRegistered();
	}

	public function isContentForVerified(): bool
	{
		$object = $this->getObject();
		return $object instanceof ExpandedPublishable && $object->isForVerified();
	}

	public function isContentForPayedMembers(): bool
	{
		$object = $this->getObject();
		return $object instanceof ExpandedPublishable && $object->isForPayedMembers();
	}

	/**
	 * Can current user/profile view section of profile from another user?
	 *
	 * @param Profile $profile Profile to view
	 * @param string $section Section of profile to view
	 * @return bool
	 */
	public function isProfileSectionVisible(Profile $profile, string $section): bool
	{
		if ($this->user->isManager()) {
			return true;
		}

		return $profile->isSectionVisibleFor($section, $this->userProfile);
	}


	// ************************** COMPONENTS ****************************** /

	protected function createComponentBreadcrumb(): Breadcrumb
	{
		return $this->breadcrumbFactory->create($this->getObject());
	}

	protected function createComponentSignInForm(): SignInForm
	{
		return $this->signInFormFactory->create($this->mutation, $this->object);
	}

	protected function createComponentSignInFormHeader(): SignInForm
	{
		return $this->signInFormFactory->create($this->mutation, $this->object);
	}

	protected function createComponentMenu(): Menu
	{
		return $this->menuFactory->create($this->getObject());
	}

	protected function createComponentUserMenu(): UserMenu
	{
		$object = $this->getObject();

		return $this->userMenuFactory->create($object);
	}

	protected function createComponentContactForm(): ContactFormControl
	{
		assert($this->object instanceof Routable);
		return $this->contactFormFactory->create($this->object);
	}

	protected function createComponentClassmateTipForm(): ClassmateTipFormControl
	{
		assert($this->object instanceof Routable);
		return $this->classmateTipFormFactory->create($this->object, $this->userEntity);
	}

	protected function createComponentSocialsTipForm(): SocialsTipFormControl
	{
		assert($this->object instanceof Routable);
		return $this->socialsTipFormFactory->create($this->object, $this->userEntity);
	}

	protected function createComponentCommunityTipForm(): CommunityTipFormControl
	{
		assert($this->object instanceof Routable);
		return $this->communityTipFormFactory->create($this->object, $this->userEntity);
	}

	protected function createComponentPodcastTipForm(): PodcastTipFormControl
	{
		assert($this->object instanceof Routable);
		return $this->podcastTipFormFactory->create($this->object, $this->userEntity);
	}

	protected function createComponentNewsletterForm(): NewsletterFormControl
	{
		return $this->newsletterFormFactory->create();
	}

	protected function createComponentCanonicalUrl(): CanonicalUrl
	{
		return $this->canonicalUrlFactory->create();
	}


	protected function createComponentRobots(): Robots
	{
		return $this->robotsFactory->create($this->getObject(), $this->mutationHolder->getMutation());
	}


	protected function createComponentEditButton(): EditButton
	{
		$routable = $this->getObject();
		if ($routable instanceof Routable) {
			return $this->editButtonFactory->create($routable, $this->userEntity);
		} else {
			return $this->editButtonFactory->create(null, $this->userEntity);
		}

	}

	public function createComponentToggleLanguage(): ToggleLanguage
	{
		$page = $this->getObject();
		if ($page instanceof LocalizationEntity) {
			return $this->toggleLanguageFactory->create($page);
		} else {
			assert($page instanceof StaticPage);
			$page = $this->pagesFactory->create($this->mutation)->title;
			return $this->toggleLanguageFactory->create($page);
		}
	}

	// ****************************** INTERNALS ****************************** /

	/**
	 * @param array $args
	 * @throws Nette\Application\UI\InvalidLinkException
	 */
	public function link(Product|LazyValue|Routable|string $destination, $args = []): string
	{
		[$destination, $args] = $this->translateDestination($destination, $args);

		if ($destination instanceof Routable) {
			[$destination, $args] = $this->linkFactory->linkInPresenter($destination, $args);
		}

		return parent::link($destination, $args);
	}

	/**
	 * @throws Nette\Application\AbortException
	 */
	public function redirect(Product|LazyValue|Routable|string $destination, $args = []): never
	{
		[$destination, $args] = $this->translateDestination($destination, $args);

		if ($destination instanceof Routable) {
			[$destination, $args] = $this->linkFactory->linkInPresenter($destination, $args);
		}

		parent::redirect($destination, $args);
	}


	private function translateDestination(Product|LazyValue|Routable|string $destination, array $args): array
	{
		if ($destination instanceof LazyValue) {
			$destination = $destination->getEntity();
			if ($destination === null) {
				trigger_error('Bad CF LazyValue entity', E_USER_NOTICE);
				// value for common user without debug mode
				$destination = 'this';
			}
		}

		if (is_string($destination)) { // input: this, //this, logout!
			$mutation = $args['mutation'] ?? $this->mutation;
			if ($mutation->urlPrefix) {
				$args['urlPrefix'] = $mutation->urlPrefix;
			}
		}

		if ($destination instanceof Product) {
			$mutation = $args['mutation'] ?? $this->mutation;
			if ($mutation->urlPrefix) {
				$args['urlPrefix'] = $mutation->urlPrefix;
			}
			$destination = $destination->getLocalization($mutation);
		}

		return [$destination, $args];
	}


	public function formatTemplateFiles(): array
	{
		$fileName = static::getReflection()->getFileName();
		assert($fileName !== false);

		$dir = dirname($fileName);
		$dir = is_dir("$dir/templates") ? $dir : dirname($dir);
		return ["$dir/templates/$this->view.latte"];
	}

	/**
	 * @param string $uid
	 * @throws AbortException
	 * @throws InvalidLinkException
	 */
	public function redirectToUserPage(string $uid): never
	{
		// kdyz je ajax a fancybox=true - JS presmerovani
		if ($this->isAjax()) {
			if (isset($_GET['fancybox'])) {
//				$this->presenter->setLayout(false);
//				$this->setView("extra");
				$url = $this->link($this->mutation->pages->$uid);
//				echo '<meta http-equiv="refresh" content="0; url='.$url.'" />';
				echo '<script>
                            window.location = "' . $url . '"
						</script>';

				$this->terminate();
			}
		}

		//, array('backlink' => $this->storeRequest())
		$this->redirect($this->mutation->pages->$uid);
	}

	protected function processExternalRedirect(LocalizationEntity $object): void
	{
		if ($this->getParameter('show')) {
			return;
		}

		$externalLink = self::getObjectExternalLink($object);
		if ($externalLink !== null) {
			$this->redirectUrl($externalLink, 302);
		}
	}

	protected function getObjectExternalLink(object $object): ?string
	{
		$externalLink = $object->cf->settings->external_link ?? null;
		if ($externalLink !== null && !str_starts_with($externalLink, 'http://') && !str_starts_with($externalLink, 'https://')) {
			$externalLink = 'https://' . $externalLink;
		}

		return $externalLink;
	}

	protected function encodeObjectTarget(object $object): ?string
	{
		return $object instanceof Routable ?
			rtrim(base64_encode($object->getId() . '|' . $object->getModule()), '=') : null;
	}

	protected function decodeObjectTarget(string $objectTarget): ?object
	{
		$parts = explode('|', base64_decode($objectTarget));
		$id = intval(array_shift($parts));
		$module = strval(array_shift($parts));

		/** @var Alias|null $alias */
		$alias = $this->orm->alias->findBy(['module' => $module, 'referenceId' => $id])->fetch();
		return $alias !== null ? $alias->parent : null;
	}

}
