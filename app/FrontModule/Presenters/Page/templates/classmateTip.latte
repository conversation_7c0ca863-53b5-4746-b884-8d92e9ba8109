{block content}
{*	{snippet tip}*}
		{*THICKBOX*}
		{if $presenter->isAjax()}
			<h1 class="u-ta-c u-mb-md">
				{$object->name}
			</h1>
			{control classmateTipForm}
		{else}
			{php $showHero = $object->cf->headerType??->toggle ?? false}
			{if $showHero}
				{include $templates.'/part/box/hero-header.latte'}
			{else}
				{include $templates.'/part/box/intro.latte'}
			{/if}
			<div id="content" class="u-mb-md u-mb-xl@md holder holder--lg">
				<div class="u-maw-7-12 u-mx-auto u-mb-last-0">
					{include $templates.'/part/box/content.latte', limit: false, class: 'u-mb-md u-mb-xl@md'}
					{control classmateTipForm}
				</div>
			</div>
		{/if}
{*	{/snippet}*}
{/block}
