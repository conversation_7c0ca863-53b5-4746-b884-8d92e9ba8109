{block content}
	{snippet content}
		{*THICKBOX*}
		{if $presenter->isAjax()}
			{include $templates.'/part/box/content.latte'}
		{else}
			{php $showHero = $object->cf->headerType??->toggle ?? false}
			{if $showHero}
				{include $templates.'/part/box/hero-header.latte'}
			{else}
				{include $templates.'/part/box/intro.latte'}
			{/if}
			<div id="content" class="u-mb-last-0 u-mb-md u-mb-xl@md">
				{include $templates.'/part/box/content.latte'}
				{include $templates.'/part/crossroad/links.latte'}
				{control customContentRenderer}
			</div>
		{/if}
	{/snippet}
{/block}