{block content}
	{snippet content}
		<div data-controller="communities">
			{php $showHero = $object->cf->headerType??->toggle ?? false}
			{if $showHero}
				{include $templates.'/part/box/hero-header.latte'}
			{else}
				{embed $templates.'/part/box/intro.latte', object: $object}
					{block content}
						<p class="inp u-mb-md u-maw-3-12 u-pt-sm u-mb-0">
							<span class="inp__fix">
								<label for="faculty" class="inp__label inp__label--inside">{_"form_label_filter_faculty"}</label>
								<select id="faculty" class="inp-select" data-controller="choices" data-communities-target="select" data-action="communities#filter">
									<option selected value="all">{_"all"}</option>
									<option value="{$facultyCode}" n:foreach="$faculties as $facultyCode => $facultyName">
										{$facultyName}
									</option>
								</select>
							</span>
						</p>
					{/block}
				{/embed}
			{/if}
			<div id="content" class="u-mb-last-0 u-mb-md u-mb-xl@md">
				{include $templates.'/part/box/content.latte'}
				{include $templates.'/part/crossroad/communities.latte'}
				{control customContentRenderer}

				{include $templates . '/part/crossroad/ctas.latte', snippetId: 'snippet--productReviewAdd'}
			</div>
		</div>
	{/snippet}
{/block}
