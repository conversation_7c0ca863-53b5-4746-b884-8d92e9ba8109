<?php

declare(strict_types=1);

namespace App\FrontModule\Presenters\Page;


use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\PostType\Page\Model\Orm\Tree;


final class PagePresenter extends BasePresenter
{
	use HasCustomContentRenderer;

	protected function startup(): void
	{
		parent::startup();
		if (isset($this->params['idref'])) {
			$idref = $this->params['idref'];
			$this->setObject($this->orm->tree->getById($idref));
		}

		$object = $this->getObject();
		\assert($object instanceof Tree);

		$this->template->isDetail = (bool)$object->last;

		//bd($this->getGlobalState());
	}

	public function renderCommunities(): void
	{
		$this->template->faculties = $this->orm->facultyLocalization->findBy(['mutation' => $this->mutation])
			->orderBy('name', 'ASC')
			->fetchPairs('faculty->id', 'name');
	}

	public function actionContact(): void
	{
	}


	public function actionDefault(): void
	{
		if ($this->isAjax()) {
			$this->presenter->setLayout(FALSE);
		}

		// ??? neni ID
//		if ($this->getObject()->uid === "newsletter2") {
//			$session = $this->session->getSection('newsletter');
//			if (!$session->newsletterId) {
////				$this->redirectUrl("/");
//				$this->template->hideForm = TRUE;
//			}
//		}
	}


	public function renderDefault(): void
	{
//		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
//		$paginator = $this['pager']->getPaginator();
//		$paginator->itemsPerPage = $this->configService->getParam('pagesPaging');
//		$cfg = array('offset' => $paginator->offset, 'limit' => $paginator->itemsPerPage);
//		$items = $this->treeService->fetchCrossroad($this->getObject()->id, $cfg);
//		$paginator->itemCount = $items->count;
//		$this->template->crossroad = $items;

		if ($this->isAjax()) {
			if ($this->signalProcessed === null) {
				if ($this->getParameter('fancybox')) {
					$this->redrawControl('content');
				} else {
					$this->redrawControl();
				}
			}
		}

	}


	public function actionClassmateTip(): void
	{

	}

	public function renderClassmateTip(): void
	{
		if ($this->isAjax()) {
			$this->presenter->setLayout(false);
		}

	}

	public function actionSocialsTip(): void
	{

	}

	public function renderSocialsTip(): void
	{
		if ($this->isAjax()) {
			$this->presenter->setLayout(false);
		}

	}

	public function actionCommunityTip(): void
	{

	}

	public function renderCommunityTip(): void
	{
		if ($this->isAjax()) {
			$this->presenter->setLayout(false);
		}

	}

	public function actionPodcastTip(): void
	{

	}

	public function renderPodcastTip(): void
	{
		if ($this->isAjax()) {
			$this->presenter->setLayout(false);
		}

	}

}
