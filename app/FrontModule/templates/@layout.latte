<!DOCTYPE html>
<html lang="{$mutation->langCode}" class="no-js">
	<head>
		<meta charset="utf-8">
		<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
		{var $keywords = $seoLink??->keywords ?? $object->keywords}
		{if $keywords !== null}
			<meta name="keywords" content="{$keywords}">
		{/if}

		{var $description = $seoLink??->description ?? $object->description ?? $object->annotation}
		{if $description !== null}
			<meta name="description" content="{$description|texy:true}">
		{/if}

		{control robots}
		{control canonicalUrl}
		<meta name="viewport" content="width=device-width, initial-scale=1">

		<title n:snippet="title">
			{var $nameTitle = $seoLink??->nameTitle ?? $object->nameTitle}
			{if $nameTitle !== null}
				{$nameTitle}
			{/if}
			{if !$isHomepage} {* Přídavek za title, kter<PERSON> se dává jen pro ne homepage stránky *}
				| {_title}
			{/if}
		</title>

		{include 'part/head/style.latte', object=>$object}
		{include 'part/head/scripts.latte', object=>$object}

		{include 'part/head/meta.latte'}
		{include 'part/head/structured_data.latte'}

		{include 'part/tracking/googleAnalytics.latte' showTop=>TRUE}

		{var $scripts = [
			'/static/js/app.js?t=' . $webVersion,
			'https://cdn.jsdelivr.net/npm/add-to-calendar-button@2'
		] }
		{foreach $scripts as $script}
			<link rel="preload" as="script" href="{$script}">
		{/foreach}
		{*
			<link rel="dns-prefetch" href="https://www.google-analytics.com">
			<link rel="dns-prefetch" href="https://www.googletagmanager.com"> {/gtm.js}
			<link rel="preconnect" href="https://www.google.com" crossorigin>
			<link rel="preconnect" href="https://www.youtube.com" crossorigin>
			<link rel="preconnect" href="https://connect.facebook.net" crossorigin>
			<link rel="preconnect" href="https://static.doubleclick.net" crossorigin>
			<link rel="preconnect" href="https://client.crisp.chat" crossorigin>
		*}
	</head>
	<body id="body" data-controller="naja modal">
		<div class="mother">
			{include 'part/tracking/googleAnalytics.latte' showBottom=>TRUE}
			{include 'part/menu/accessibility.latte'}

			{snippetArea header}
				{include 'part/header.latte'}
			{/snippetArea}

			<main id="main" n:class="main, $mainClass ?? false">
				{varType App\Model\Pages $pages}

				{define #contentNotAllowed}
					{include $templates.'/part/box/intro.latte', annotation: $object->annotation}
					<div class="u-mb-last-0 u-mb-md u-mb-xl@md u-ta-c">
						{switch $reason}
							{case 'not-registered'}
								<p class="h2 u-mb-sm u-mb-md@md">{_'content_for_registered_info'}</p>
								<p>
									<a n:href="$pages->userLogin loginback => ($object|encodeTarget)" class="btn btn--secondary btn--outline btn--sm">
										<span class="btn__text">{_'btn_login'}</span>
									</a>
								</p>
							{case 'not-verified'}
								<p class="h2 u-mb-sm u-mb-md@md">{_'content_for_verified_info'}</p>
								<p class="u-mb-sm u-mb-md@md">{_'content_for_verified_additional_info'}</p>
								<a n:href="$pages->userVerification" class="btn"><span class="btn__text">{_'btn_verify'}</span></a>
						{/switch}
					</div>
				{/define}

				{* note - generic publishable logic is handled in router already *}
				{if $isContentForRegistered && !$user->isLoggedIn()}
					{include #contentNotAllowed reason: 'not-registered'}
				{elseif $isContentForVerified && !$canViewVerifiedContent}
					{include #contentNotAllowed reason: 'not-verified'}
				{else}
					{include #content}
				{/if}
			</main>

			{include './part/box/last-visited.latte'}

			{include 'part/footer.latte'}

			<a href="#body" class="to-top btn-circle">
				<span class="u-vhide">{_"btn_top"}</span>
				{('angle-u')|icon}
			</a>

			{if $mutation->isEnabledCookieModal}
				{include 'part/box/cookie.latte'}
			{/if}

			{control editButton}
			<div class="body-loader__loader"></div>
		</div>


		{foreach $scripts as $script}
			<script src="{$script}"></script>
		{/foreach}
		<script>
			App.run({
				apiKey: {$googleApiKey},
				assetsUrl: '/static/',
			});
		</script>
	</body>
</html>
