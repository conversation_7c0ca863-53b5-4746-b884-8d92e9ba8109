
										</td>
									</tr>
								</table> <!-- / content -->

								<table class="sk-block-table-all" width="100%" border="0" cellspacing="0" cellpadding="0" style="margin:0 auto; padding:0; font-size:12px; font-family: Arial, Helvetica, sans-serif; width:100%; text-align: left; border-top: 1px solid #eaeaea;">
									<tr>
										<td class="sk-pt20 sk-pb10 sk-pl20 sk-pr20 sk-tac" style="padding: 30px 45px 25px; color: #1F2125; vertical-align: top;">
											{*<p style="margin: 0 0 15px; line-height: 1;">
												<a href="{$mutation->getBaseUrlWithPrefix()}" style="text-decoration: none;">
													<img src="{$mutation->getBaseUrl()}/static/img/logo-portal.png" alt="{_logo}" width="200" height="71" style="vertical-align: top; border: none;" />
												</a>
											</p>*}
											<p style="margin: 0 0 10px; line-height: 18px;">
												{* {php $year = 2016} *}
												&copy; {*{$year} - *}{date("Y")} {_"email_copyrights"|noescape}
											</p>
{*											{if isset($unsubscribe) && $unsubscribe}*}
{*												<p style="margin: 0 0 10px; line-height: 18px;">*}
{*													{_email_unscribe}*}

{*													{php $emailEncoded = urlencode($data['email'])}*}
{*													<a href="{$mutation->getBaseUrl()}/odhlasit-z-odberu-novinek?email={$emailEncoded}&amp;hash={$data['hash']}" style="color:#1F2125;">*}
{*														{_here}*}
{*													</a>*}
{*												</p>*}
{*											{/if}*}
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table> <!-- / wrap -->
				</td>
			</tr>
		</table> <!-- / wrap -->
	</body>
</html>
