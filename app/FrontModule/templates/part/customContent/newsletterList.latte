<table n:if="count($items)" class="t-list t-helper rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
	<tbody>
		<tr>
			<td class="t-list__inner t-helper__inner u-pb-sm">
				<h2 n:if="$title">
					{$title}
				</h2>

				<table n:foreach="$items as $item" class="t-article rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
					{default $annot = $item->annotation ? $item->annotation : $item->description ? $item->description : null}
					{php $externalUrl = $item->cf->settings->external_link ?? false}
					<tbody>
						<tr>
							<td class="t-article__inner u-pb-md">
								<table class="rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="t-article__img">
												<a href="{if $externalUrl}{$externalUrl}{else}{plink $item filter => null}{/if}">
													{if isset($absolvents)}
														{var $profile = $item->getParent()}
														{php $img = isset($profile->avatar) ? $profile->avatar : false}
														{if $img}
															<img src="{$img->getSize('newsletter-avatar')->src}" alt="" class="rwd-full">
														{else}
															<img src="/static/img/illust/noimg.png" alt="" width="200" height="200" class="rwd-full">
														{/if}
													{else}
														{php $img = isset($item->cf->base->mainImage) ? $item->cf->base->mainImage->getEntity() ?? false : false}
														{if $img}
															<img src="{$img->getSize('newsletter-article')->src}" alt="" class="rwd-full">
														{else}
															<img src="/static/img/illust/noimg.png" alt="" width="200" height="200" class="rwd-full">
														{/if}
													{/if}
												</a>
											</td>
											<td class="t-article__content u-mb-last-0">
												{if isset($absolvents)}
													{var $sections = $item->sectionsTotal}
													{var $name = $sections->name}
													{var $fullName = $name->getFullName(exclude: [titlesBefore, titlesAfter, lastnameStudy])}

													<h3 class="h4">
														<a href="{if $externalUrl}{$externalUrl}{else}{plink $item filter => null}{/if}">
															{$name->titlesBefore} {$fullName} {$name->titlesAfter}
														</a>
													</h3>
												{else}
													<h3 class="h4">
														<a href="{if $externalUrl}{$externalUrl}{else}{plink $item filter => null}{/if}">
															{$item->name}
														</a>
													</h3>
												{/if}

												{if isset($absolvents)}
													{if $item->educations->count()}
														<table width="100%" border="0" cellspacing="0" cellpadding="0">
															<tbody>
																<tr>
																	<td class="u-fw-bold" style="font-size: 12px; line-height: 18px;">
																		<p class="u-mb-xs" n:foreach="$item->educations as $education">
																			{implode(' | ', array_filter([$education->facultyLocalization??->name ?? false, $education->programmeLocalization??->name ?? false, $education->branchLocalization??->name ?? false]))|noescape}{sep}<br> {/sep}
																		</p>
																	</td>
																</tr>
															</tbody>
														</table>
														<p n:if="$item->cf->base??->about ?? false" class="u-mb-0 u-mt-xs">
															{$item->cf->base->about}
														</p>
													{/if}
												{/if}

												<p n:if="$annot">
													{$annot|texy|noescape}
												</p>

												<p n:ifset="$articles">
													{capture $monthNumber}{$item->publicFrom|date:"n"}{/capture}
													<time datetime='{$item->publicFrom|date:"Y-m-d"}'>{$item->publicFrom|date:"j."} {_"month_".$monthNumber->__toString()} {$item->publicFrom|date:"Y"}</time>
												</p>

												<p n:ifset="$calendars">
													{php $from = $item->cf->settings??->from ?? false}
													<time n:if="$from" datetime="{$from|date:'Y-m-d'}">
														{$from|date('j')}. {capture $monthNumber}{$from|date:"n"}{/capture} {_"month_".$monthNumber->__toString()} {$from|date('Y')}
													</time>
												</p>

												<p n:ifset="educations">
													{$item->facultyLocalization??->name ?? false}
												</p>

												{if $externalUrl}
													<p class="u-fw-bold" style="font-size: 12px; line-height: 18px;">
														(Externí zdroj)
													</p>
												{/if}
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</tbody>
				</table> <!-- / t-article -->
			</td>
		</tr>
	</tbody>
</table> <!-- / t-list -->
