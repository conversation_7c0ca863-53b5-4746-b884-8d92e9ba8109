{varType App\Model\BucketFilter\SetupCreator\Blog\BasicElasticItemListFactory $blogEsItemListFactory}

{php $type = $customContentItem->type ?? 'custom'}
{php $title = $customContentItem->title ?? false}
{php $showMore = $customContentItem->showMore ?? false}
{php $limit = $specLayout ? 2 : 3}

{if $type === 'newest'}
	{php $items = ($blogEsItemListFactory->create()|bucketFilterItems:blog,newest,$limit)}
{else}
	{php $items = $customContentItem->items ?? []}
{/if}

{include $templates.'/part/crossroad/articles.latte', title: $title, titleTag: $title ? 'h3' : 'h2', items: $items, pager: false, showMore: $showMore, carousel: true}
