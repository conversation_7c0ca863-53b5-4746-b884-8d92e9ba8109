{php $content = $customContentItem->content ?? false}
{* {php $image = isset($customContentItem->image) ? $customContentItem->image->getEntity() ?? false : false} *}

<table n:if="$content" class="t-content t-helper rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
	<tbody>
		<tr>
			<td class="t-content__inner t-helper__inner u-pb-md">
				{$content|tables|lazyLoading|obfuscateEmailAddresses|noescape}
			</td>
		</tr>
	</tbody>
</table> <!-- / t-content -->
