{varType App\Model\BucketFilter\SetupCreator\Calendar\BasicElasticItemListFactory $calendarEsItemListFactory}

{php $type = $customContentItem->type ?? 'custom'}
{php $title = $customContentItem->title ?? false}
{php $showMore = $customContentItem->showMore ?? false}
{php $limit = $specLayout ? 2 : 3}

{if $type === 'nearest'}
	{php $items = ($calendarEsItemListFactory->create(dateFrom: date_create_immutable(today))|bucketFilterItems:calendar,eventFrom,$limit)}
{else}
	{php $items = $customContentItem->items ?? []}
{/if}

{include $templates.'/part/crossroad/events.latte', title: $title, titleTag: $title ? 'h3' : 'h2', items: $items, pager: false, showMore: $showMore, carousel: true}
