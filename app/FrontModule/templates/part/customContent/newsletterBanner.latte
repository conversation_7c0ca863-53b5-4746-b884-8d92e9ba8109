{php $image = isset($customContentItem->image) ? $customContentItem->image->getEntity() ?? false : false}
{php $href = $customContentItem->href ?? false}
{php $text = $customContentItem->text ?? false}

{capture $imageElement}
	<img src="{$image->getSize('newsletter')->src}" alt="{$text ?? false}" width="{$image->getSize('newsletter')->width}" height="{$image->getSize('newsletter')->height}">
{/capture}

<table n:if="$image" class="t-bnr rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
	<tbody>
		<tr>
			<td class="t-bnr__inner u-pb-md">
				{if $href}
					<a href="{$href}">
						{$imageElement}
					</a>
				{else}
					{$imageElement}
				{/if}
			</td>
		</tr>
	</tbody>
</table> <!-- / t-content -->
