{php $title = $customContentItem->title ?? false}
{php $annot = $customContentItem->annot ?? false}
{php $btn = $customContentItem->btn ?? false}
{php $image = isset($customContentItem->image) ? $customContentItem->image->getEntity() ?? false : false}


{php $link = false}
{php $btnText = false}
{if $btn}
	{php $type = $btn->toggle}
	{php $page = isset($btn->systemHref) && isset($btn->systemHref->page) ? $btn->systemHref->page->getEntity() ?? false : false}
	{php $hrefName = ($btn->systemHref??->hrefName ?? false) ?: ($btn->customHref??->hrefName ?? false)}
	{php $href = $btn->customHref??->href ?? false}

	{if $type == 'systemHref' && $page}
		{capture $link}{plink $page}{/capture}
		{php $link = $link->__toString()}
		{if $hrefName}
			{php $btnText = $hrefName}
		{else}
			{php $btnText = $page->nameAnchor}
		{/if}
	{elseif $type == 'customHref' && $href && $hrefName}
		{php $link = $href}
		{php $btnText = $hrefName}
	{/if}
{/if}

{include $templates.'/part/box/cta.latte', title: $title, annot: $annot, btn: $btn, image: $image, link: $link, btnText: $btnText}