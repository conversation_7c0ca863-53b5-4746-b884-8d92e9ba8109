{php $items = $customContentItem->items ?? []}

<div class="c-benefits u-mb-lg u-mb-xl@md holder holder--lg">
	<div class="c-benefits__list grid grid--x-md grid--y-md">
		{foreach $items as $item}
			<div class="c-benefits__item grid__cell">
				{define #linkContent}
					{default $external = false}
					<div class="b-benefit__img img">
						{php $img = isset($item->image) ? $item->image->getEntity() ?? false : false}
						{if $img}
							<img src="{$img->getSize('md')->src}" alt="" loading="lazy">
						{else}
							<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
						{/if}
					</div>
					<div class="b-benefit__content">
						<h2 class="b-benefit__title h3">
							{$name}
							{if $external}{('link')|icon}{/if}
						</h2>
						<p n:ifcontent class="b-benefit__text b-benefit__text--clamp u-mb-0">
							{$item->annot ?? false}
						</p>
					</div>
				{/define}

				{php $type = $item->link->toggle}
				{if $type}
					{php $page = isset($item->link->systemHref) && isset($item->link->systemHref->page) ? $item->link->systemHref->page->getEntity() ?? false : false}
					{php $hrefName = ($item->link->systemHref??->hrefName ?? false) ?: ($item->link->customHref??->hrefName ?? false)}
					{php $href = $item->link->customHref??->href ?? false}

					{if $type == 'systemHref' && $page}
						<a href="{plink $page}" n:ifcontent class="b-benefit b-benefit--center">
							{if $hrefName}
								{include #linkContent, name: $hrefName}
							{else}
								{include #linkContent, name: $page->nameAnchor}
							{/if}
						</a>
					{elseif $type == 'customHref' && $href && $hrefName}
						<a href="{$href}" class="b-benefit b-benefit--center" target="_blank">
							{include #linkContent, name: $hrefName, external: true}
						</a>
					{/if}
				{/if}
			</div>
		{/foreach}
	</div>
</div>