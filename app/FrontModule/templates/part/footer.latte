<footer class="footer">
	<div class="footer__group u-pt-sm u-pb-sm u-pt-md@md u-pb-md@md holder holder--lg u-no-print">
		{include $templates.'/part/form/newsletter.latte'}
	</div>
	<div class="footer__group u-pt-md@sm u-pt-lg@md u-pb-md@sm holder holder--lg u-no-print">
		{include $templates.'/part/menu/footer.latte' props: [
			data: [
				[
					title: $object->mutation->cf?->footerMenu?->footer_menu_1?->title ?? false,
					list: $object->mutation->cf?->footerMenu?->footer_menu_1?->list ?? false,
				],
				[
					title: $object->mutation->cf?->footerMenu?->footer_menu_2?->title ?? false,
					list: $object->mutation->cf?->footerMenu?->footer_menu_2?->list ?? false,
				],
				[
					title: $object->mutation->cf?->footerMenu?->footer_menu_3?->title ?? false,
					list: $object->mutation->cf?->footerMenu?->footer_menu_3?->list ?? false,
				],
				[
					title: $object->mutation->cf?->footerMenu?->footer_menu_4?->title ?? false,
					list: $object->mutation->cf?->footerMenu?->footer_menu_4?->list ?? false,
				],
				[
					title: $object->mutation->cf?->footerMenu?->footer_menu_5?->title ?? false,
					list: $object->mutation->cf?->footerMenu?->footer_menu_5?->list ?? false,
				],
				[
					title: $object->mutation->cf?->footerMenu?->footer_menu_6?->title ?? false,
					list: $object->mutation->cf?->footerMenu?->footer_menu_6?->list ?? false,
				],
			]
		]}
	</div>
	<div class="footer__group u-pb-md u-pb-lg@sm u-pt-sm u-pt-md@sm holder holder--lg">
		<div class="grid grid--middle grid--y-0">
			<div class="grid__cell size--6-12@md size--4-12@xl">
				<p class="footer__logo">
					{* {% set logoFooter = 'logo' %}
					{% if footerType %}
						{% set logoFooter = 'logo-' + footerType + '--sm' %}

						{% if eng %}
							{% set logoFooter = 'logo-' + footerType + '-en--sm' %}
						{% endif %}
					{% endif %} *}

					<a href="/">
						{* {if $mutation->langCode == 'cs'}
							{('logo')|icon}
						{else}
							{('logo--en')|icon}
						{/if} *}
						{('logo-portal')|icon}
						<span class="u-vhide">{_"logo"}</span>
					</a>
				</p>
			</div>

			{php $contactInfo = $mutation->cf->contact_info ?? false}
			<div class="grid__cell size--6-12@md size--4-12@xl">
				<div class="address footer__info">
					<p class="address__title u-ff-secondary">{_"logo"}</p>
					<div class="address__wrap">
						<p n:if="$contactInfo->address ?? false" class="address__item">
							{$contactInfo->address|texy|noescape}
						</p>
						<p n:ifcontent class="address__item">
							<a n:if="$contactInfo->web ?? false" href="https://{$contactInfo->web}" class="u-fw-b" target="_blank" rel="noopener noreferrer">{$contactInfo->web}</a><br>
							<a n:if="$contactInfo->mail ?? false" href="mailto:{$contactInfo->mail}" class="u-fw-b" target="_blank" rel="noopener noreferrer">{$contactInfo->mail}</a><br>
						</p>
					</div>
					<p n:if="$mutation->isEnabledCookieModal" class="u-pt-xs u-pt-sm@md u-mb-0">
						<button class="as-link" data-cookie-open>{_"btn_cookies_settings"}</button>
					</p>
				</div>
			</div>

			<div class="grid__cell size--6-12@md size--4-12@xl u-ml-auto">
				<div class="c-socials footer__socials">
					<ul class="c-socials__list">
						<li n:if="$contactInfo->facebook ?? false" class="c-socials__item">
							<a href="{$contactInfo->facebook}" class="c-socials__link" target="_blank" rel="noopener noreferrer">
								{('facebook')|icon, 'c-socials__icon'}
								<span class="u-vhide">Facebook</span>
							</a>
						</li>
						<li n:if="$contactInfo->twitter ?? false" class="c-socials__item">
							<a href="{$contactInfo->twitter}" class="c-socials__link" target="_blank" rel="noopener noreferrer">
								{('x')|icon, 'c-socials__icon'}
								<span class="u-vhide">X</span>
							</a>
						</li>
						<li n:if="$contactInfo->instagram ?? false" class="c-socials__item">
							<a href="{$contactInfo->instagram}" class="c-socials__link" target="_blank" rel="noopener noreferrer">
								{('instagram')|icon, 'c-socials__icon'}
								<span class="u-vhide">Instagram</span>
							</a>
						</li>
						<li n:if="$contactInfo->linkedin ?? false" class="c-socials__item">
							<a href="{$contactInfo->linkedin}" class="c-socials__link" target="_blank" rel="noopener noreferrer">
								{('linkedin')|icon, 'c-socials__icon'}
								<span class="u-vhide">LinkedIn</span>
							</a>
						</li>
						<li n:if="$contactInfo->youtube ?? false" class="c-socials__item">
							<a href="{$contactInfo->youtube}" class="c-socials__link" target="_blank" rel="noopener noreferrer">
								{('youtube')|icon, 'c-socials__icon'}
								<span class="u-vhide">YouTube</span>
							</a>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</footer>