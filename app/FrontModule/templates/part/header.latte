{varType App\Model\Pages $pages}

<header n:class="header" data-controller="header">
	<h1 n:tag="!$isHomepage ? 'p'" class="header__logo">
		<a href="/">
			{('symbol')|icon}
			<span n:class="header__logo-main, isset($_COOKIE['logo_shown']) ? is-hidden" data-header-target="logo">
				{('logo')|icon}
			</span>
			<span class="u-vhide">{_"logo"}</span>
		</a>
	</h1>

	<nav id="menu-main" class="m-main m-main--header header__menu" data-controller="toggle-class">
		{control menu}
		{include $templates.'/part/menu/main-user.latte'}
	</nav>

	{include 'form/search.latte'}

	{control breadcrumb}
</header>
