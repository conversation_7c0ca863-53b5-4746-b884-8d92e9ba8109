{* Twitter *}
<meta name="twitter:card" content="summary_large_image">
{* OpenGraph *}
{if isset($object->nameTitle)}
	<meta property="og:title" content="{$object->nameTitle}">
{/if}
{if isset($object->description) && $object->description}
	<meta property="og:description" content="{$object->description}">
{elseif isset($object->annotation) && $object->annotation}
	<meta property="og:description" content="{$object->annotation}">
{/if}
{if $cfImage = isset($object->cf->base->mainImage) ? $object->cf->base->mainImage->getEntity() ?? false : false}
	{php $img = $object->cf->base->mainImage->getSize('ogImage')}
	<meta property="og:image" content="{$mutation->getBaseUrl()}{$img->src}">
{elseif isset($mutation->cf->mutationData->ogImage)}
	{php $img = $mutation->cf->mutationData->ogImage->getSize('ogImage')}
	<meta property="og:image" content="{$mutation->getBaseUrl()}{$img->src}">
{/if}
<meta property="og:site_name" content="{_title}">
{if isset($object) && $object instanceOf App\Model\Orm\Routable}
	<meta property="og:url" content="{link $object}">
	{if isset($object) && $object && $object->template == ':Front:Product:detail'}
		<meta property="og:type" content="product">
	{elseif isset($object) && $object && $object->template == ':Blog:Front:Blog:detail'}
		<meta property="og:type" content="article">
	{else}
		<meta property="og:type" content="website">
	{/if}
{else}
	<meta property="og:url" content="{$mutation->getBaseUrl()}">
	<meta property="og:type" content="website">
{/if}

{* favicons *}
<link rel="apple-touch-icon" sizes="180x180" href="{$mutation->getBaseUrl()}/static/img/favicon//apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="32x32" href="{$mutation->getBaseUrl()}/static/img/favicon//favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="{$mutation->getBaseUrl()}/static/img/favicon//favicon-16x16.png">
<link rel="manifest" href="{$mutation->getBaseUrl()}/static/img/favicon//site.webmanifest">
<meta name="msapplication-TileColor" content="#da532c">
<meta name="theme-color" content="#ffffff">
