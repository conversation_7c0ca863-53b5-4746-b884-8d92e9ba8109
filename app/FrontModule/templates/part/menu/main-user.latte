<ul class="m-main__list m-main__list--side">
	{if $user->loggedIn}
		<li class="m-main__item" data-controller="touch-open etarget">
			<a n:href="$pages->userSection" class="m-main__link m-main__link--icon" data-action="touch-open#open" aria-expanded="false">
				{('user')|icon, 'm-main__icon u-c-green'}
				<span class="u-vhide">{_"header_user_logged"}</span>
			</a>
			<div class="m-main__submenu m-main__submenu--m">
				<div class="m-sub">
					<div class="m-sub__col">
						{control userMenu}
					</div>
				</div>
			</div>
		</li>
	{else}
		<li n:if="isset($pages->userLogin)" class="m-main__item">
			<a n:href="$pages->userLogin loginback => $isContentForRegistered ? ($object|encodeTarget) : null" class="m-main__link m-main__link--icon"
				{*data-naja
				data-naja-loader="body"
				data-naja-modal="snippet--signInForm"
				data-custom-class="b-modal--small"
				data-naja-history="off"*}>
				{('user')|icon, 'm-main__icon'}
				<span class="u-vhide">{_"header_user_unlogged"}</span>
			</a>
		</li>
	{/if}
	<li class="m-main__item m-main__item--search">
		<a href="#" class="m-main__link m-main__link--icon" data-controller="toggle-class etarget" data-action="toggle-class#toggle" data-toggle-content=".f-search">
			{('search')|icon, 'm-main__icon'}
			<span class="u-vhide">{_"btn_search"}</span>
		</a>
	</li>
	<li class="m-main__item hide--m u-hide u-show@xl">
		{control toggleLanguage}
	</li>
</ul>
