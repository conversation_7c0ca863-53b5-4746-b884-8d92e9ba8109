{ifset $props}
	<div class="grid grid--y-0 grid--y-md@md">
		{foreach $props['data'] as $item}
			<div n:if="isset($item['list']) && $item['list']" class="grid__cell size--4-12@md size--2-12@lg" data-controller="toggle-class">
				<h2 class="footer__title" data-action="click->toggle-class#toggle" aria-expanded="false">
					{$item['title']}
					{('angle-d')|icon, 'footer__title-icon'}
				</h2>
				<div class="footer__more">
					<ul class="footer__list">
						<li n:foreach="$item['list'] as $item" n:ifcontent class="footer__item">
							{php $type = $item->toggle}
							{php $page = isset($item->systemHref) && isset($item->systemHref->page) ? $item->systemHref->page->getEntity() ?? false : false}
							{php $hrefName = ($item->systemHref??->hrefName ?? false) ?: ($item->customHref??->hrefName ?? false)}
							{php $href = $item->customHref??->href ?? false}

							{if $type == 'systemHref' && $page}
								<a href="{plink $page}" n:ifcontent class="footer__link">
									{if $hrefName}
										{$hrefName}
									{else}
										{$page->nameAnchor}
									{/if}
								</a>
							{elseif $type == 'customHref' && $href && $hrefName}
								<a href="{$href}" class="footer__link" target="_blank">
									{$hrefName}
								</a>
							{/if}
						</li>
					</ul>
				</div>
			</div>
		{/foreach}
	</div>
{/ifset}
