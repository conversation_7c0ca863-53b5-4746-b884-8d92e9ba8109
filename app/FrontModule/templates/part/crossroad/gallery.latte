{default $class = 'u-mb-md u-mb-xl@md'}
{default $images = []}

<div n:if="count($images)" n:class="c-gallery, $class, holder, holder--lg">
	<div class="u-maw-7-12 u-mx-auto">
		<div class="c-gallery__inner">
			<ul class="c-gallery__list grid grid--x-xs grid--y-xs">
				{foreach $images as $img}
					{if $iterator->counter < 4}
						<li class="c-gallery__item grid__cell size--6-12@sm">
							<a href="{$img->getSize('2xl')->src}" data-modal='{"gallery": "article"}' class="c-gallery__link">
								<div class="c-gallery__img img img--3-2">
									<img src="{$img->getSize('md')->src}" alt="" loading="lazy">
								</div>
							</a>
						</li>
					{elseif $iterator->counter == 4}
						<li class="c-gallery__item grid__cell size--6-12@sm">
							<a href="{$img->getSize('2xl')->src}" data-modal='{"gallery": "article"}' class="c-gallery__link c-gallery__link--overlay">
								<div class="c-gallery__img img img--3-2">
									<img src="{$img->getSize('md')->src}" alt="" loading="lazy">
								</div>
								<div class="c-gallery__content">
									<span class="btn btn--sm btn--outline btn--white">
										<span class="btn__text">{_"btn_show_gallery"}</span>
									</span>
								</div>
							</a>
						</li>
					{else}
						<li class="u-vhide">
							<a href="{$img->getSize('2xl')->src}" data-modal='{"gallery": "article"}'>
								<img src="{$img->getSize('md')->src}" alt="" loading="lazy">
							</a>
						</li>
					{/if}
				{/foreach}
			</ul>
		</div>
	</div>
</div>