{default $class = 'u-mb-lg'}
{default $pager = true}
{default $titleTag = 'h2'}
{default $titleLang = false}
{default $products = false}
{default $ajaxPage = false}
{default $cleanFilterParam = []}

{if $products}
	<section n:class="c-products, $class">
		<h2 n:if="$titleLang" class="c-products__title">
			{_$titleLang}
		</h2>

		{php $cleanFilterParamCopy = $cleanFilterParam}
		{* {if $pager}
			{snippet productsPagerTop}
				{default $cleanFilterParam = []}
				{php $cleanFilterParamCopy = $cleanFilterParam}
				{control pager, [filter: $cleanFilterParamCopy, class: 'u-mb-sm']}
			{/snippet}
		{/if} *}

		<div class="c-products__list grid" n:snippet="productList" data-ajax-append>
			<div n:foreach="$products as $product" class="c-products__item grid__cell size--6-12@md size--4-12@xl">
				{include '../box/product.latte', product: $product, class: false}
			</div>
		</div>

		{if $pager}
			{snippet productsPagerBottom}
				{default $cleanFilterParam = []}
				{php $cleanFilterParamCopy = $cleanFilterParam}
				{default $ajaxPage = false}
				{control pager, [filter: $cleanFilterParamCopy, showMoreBtn: true, showCount: true, class: 'u-pt-sm', ajaxPage: $ajaxPage]}
			{/snippet}
		{/if}
	</section>
{else}
	<p n:class="message, $class">
		{_message_empty_filter}
	</p>
{/if}

