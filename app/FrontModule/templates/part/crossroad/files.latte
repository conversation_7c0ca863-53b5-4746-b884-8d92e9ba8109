{default $class = 'u-mb-md u-mb-xl@md'}
{default $specLayout}
{default $title = false}
{default $files = []}

<section n:class="c-files, $class, !$specLayout ? 'holder holder--lg'">
	<div n:tag-if="!$specLayout" class="u-maw-7-12 u-mx-auto">
		<h2 n:if="$title" n:class="!$specLayout ? u-ta-c, 'u-mb-sm u-mb-md@md'">
			{$title}
		</h2>
		<ul class="c-files__list">
			<li n:foreach="$files as $file" class="c-files__item">
				<a href="{$file->url}" class="link-file link-file--lg" download>
					{('file')|icon, 'link-file__icon'}
					<strong class="link-file__name link-file__name--secondary">{$file->name}</strong>
					<span class="tag tag--outline tag--xs link-file__tag">{$file->ext|upper}</span>
				</a>
			</li>
		</ul>
	</div>
</section>