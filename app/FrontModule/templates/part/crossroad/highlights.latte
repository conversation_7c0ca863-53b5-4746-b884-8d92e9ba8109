{default $class = 'u-mb-md u-mb-xl@md'}
{default $items = []}

<div n:if="count($items)" n:class="c-highlights, $class">
	<ul class="c-highlights__list grid grid--x-0 grid--y-0">
		<li n:foreach="$items as $item" n:ifcontent n:class="c-highlights__item, grid__cell, 'size--6-12@md', !($item->content ?? false) ? 'size--3-12@xl'">
			{include $templates.'/part/box/highlight.latte', item: $item}
		</li>
	</ul>
</div>