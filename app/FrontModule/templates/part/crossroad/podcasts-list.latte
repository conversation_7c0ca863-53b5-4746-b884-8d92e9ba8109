{default $class = 'u-mb-md u-mb-xl@md'}
{default $items = []}

<div n:if="count($items)" n:class="c-schedule, $class, holder, holder--lg">
	<div class="u-maw-7-12 u-mx-auto u-mb-last-0">
		<ul class="c-schedule__list grid">
			<li n:foreach="$items as $item" class="c-schedule__item grid__cell size--6-12@md">
				<p class="c-schedule__label">
					<span class="item-icon">
						<span class="item-icon__text">
							<a n:if="$item->link ?? false" class="c-schedule__link h4 u-d-b" href="{$item->link}" target="_blank" rel="noopener noreferrer">
								{$item->name ?? $item->link}
							</a>
							<span n:ifcontent class="c-schedule__info">
								<span n:if="$item->tag ?? false" class="tag tag--outline tag--sm">{$item->tag}</span>
								<span n:if="$item->type ?? false" class="item-icon">
									{($item->type)|icon, 'item-icon__icon'}
									<span class="item-icon__text">
										{$item->type|firstUpper}
									</span>
								</span>
							</span>
						</span>
					</span>
				</p>
			</li>
		</ul>
	</div>
</div>