{default $class = false}
{default $title = false}
{default $titleLang = false}
{default $specLayout = false}
{default $items = []}
{default $pager = true}
{default $carousel = false}
{default $gridSizes = 'size--6-12@xl'}
{default $type = false}
{default $showMore = false}
{default $titleTag = 'h2'}
{default $condensed = false}

<section n:class="c-absolvents, $class, !$specLayout ? 'holder holder--lg'">
	<h2 n:if="$titleLang || $title" n:class="!$specLayout ? u-ta-c, 'u-mb-sm u-mb-md@md'">
		{if $title}
			{_$title}
		{elseif $titleLang}
			{_$titleLang}
		{/if}
	</h2>
	{if count($items)}
		<div n:tag-if="$carousel" class="embla" data-controller="embla">
			<div n:tag-if="$carousel" class="embla__holder">
				<div n:tag-if="$carousel" class="embla__viewport" data-embla-target="viewport">
					<ul n:inner-foreach="$items as $item"
						n:class="c-vcards__list, grid, $condensed ? 'grid--x-sm grid--y-sm' : 'grid--y-md@md', $carousel ? 'grid--scroll embla__container'">
						{php $followingItem = $item instanceof App\PostType\Profile\Model\Orm\ProfileFollowing ? $item : null}
						{if $type === 'followings'}
							{php $item = $followingItem->followingLocalization}
						{elseif $type === 'followers'}
							{php $item = $followingItem->followerLocalization}
						{/if}

						<li n:class="c-vcards__item, grid__cell, $gridSizes">
							{include $templates.'/part/box/absolvent.latte', class: false, condensed: $condensed, type: $type, followingItem: $followingItem, item: $item}
						</li>
					</ul>
				</div>

				{if $carousel}
					<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
						{('angle-l')|icon}
						<span class="u-vhide">{_btn_prev}</span>
					</button>
					<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
						{('angle-r')|icon}
						<span class="u-vhide">{_btn_next}</span>
					</button>
				{/if}
			</div>
		</div>
		{if $pager}
			{snippet profilesPagerBottom}
				{control pager, []}
			{/snippet}
		{/if}
	{else}
		<p class="h2 u-ta-c u-mb-0">
			{_"empty_absolvents"}
		</p>
	{/if}

	<p n:if="$showMore && isset($pages->absolvents)" class="u-ta-c u-mb-0 u-pt-sm u-pt-md@md">
		<a href="{plink $pages->absolvents}" class="btn btn--secondary btn--outline">
			<span class="btn__text">
				{_"btn_show_more"}
			</span>
		</a>
	</p>
</section>
