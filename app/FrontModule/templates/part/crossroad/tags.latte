{default $class = 'u-mb-md u-mb-xl@md'}
{default $items = $object->crossroad ?? []}

<div n:if="count($items) > 0" n:class="$class">
	<ul class="grid grid--x-xs grid--y-xs">
		<li n:foreach="$items as $item" class="grid__cell size--auto">
			{var $tag = $item->tag ?? $item}
			{*		<a n:href="$tag" class="tag tag--outline tag--sm">*}
			<a href="{plink $parent}?filter[dials][tags][{$tag->id}]={$tag->id}" class="tag tag--outline tag--sm">
				{$tag->name}{if $item->count ?? false} ({$item->count}){/if}
			</a>
		</li>
	</ul>
</div>
