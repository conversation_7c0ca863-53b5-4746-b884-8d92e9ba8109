{default $class = 'u-mb-md u-mb-xl@md'}
{default $specLayout = false}
{default $title = false}
{default $items = $object->crossroad ?? []}
{default $uidsToSkip = []}

<section n:if="count($items) > 0" n:class="c-programs, $class, !$specLayout ? 'holder holder--lg'">
	<div n:tag-if="!$specLayout" class="row-main row-main--md">
		<h2 n:if="$title" class="u-ta-c u-mb-sm u-mb-md@md">
			{$title}
		</h2>
		<div class="c-programs__wrap">
			<ul class="c-programs__list grid grid--x-xl grid--y-xl">
				<li n:foreach="$items as $item" n:class="c-programs__item, grid__cell, $specLayout ? 'size--6-12@md' : 'size--6-12@md size--4-12@xl'">
					{php $toggle = $item->link??->toggle ?? false}
					{php $link = false}
					{php $title = false}
					{php $annot = false}
					{php $btnText = false}
					{php $img = false}
					{php $external = false}

					{if $toggle}
						{* Custom výpis *}
						{if $toggle == 'systemHref'}
							{php $page = isset($item->link->systemHref->page) ? $item->link->systemHref->page->getEntity() ?? false : false}
							{php $title = $item->title ?? ($page ? $page->name : false)}
							{php $link = $page ? $presenter->link($page) : false}
							{php $annot = $item->annot ?? $page->annotation}
							{php $btnText = $item->link->systemHref->hrefName ?? ($page ? $page->name : fase) ?? false}
							{php $imgPage = isset($item->cf->base->crossroadImg) ? $item->cf->base->crossroadImg->getEntity() ?? false : false}
							{php $img = $item->image ?? $imgPage}
						{elseif $toggle == 'customHref'}
							{php $title = $item->title ?? false}
							{php $annot = $item->annot ?? false}
							{php $link = $item->link->customHref??->href ?? false}
							{php $btnText = $item->link->customHref->hrefName ?? false}
							{php $external = true}
							{php $img = $item->image ?? false}
						{/if}
					{elseif isset($item->name)}
						{php $link = $presenter->link($item)}
						{php $title = $item->name}
						{php $annot = $item->annotation}
						{capture $btnText}{_"btn_visit"}{/capture}
						{php $btnText = $btnText->__toString()}
						{php $imgPage = isset($item->cf->base->crossroadImg) ? $item->cf->base->crossroadImg->getEntity() ?? false : false}
						{php $img = $item->image ?? $imgPage}
					{/if}

					{include '../box/std.latte', class: false, title: $title, link: $link, annot: $annot, btnText: $btnText, page: $page ?? false, img: $img, external: $external}
				</li>
			</ul>
		</div>
	</div>
</section>
