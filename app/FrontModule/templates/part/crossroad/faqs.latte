{default $class = 'u-mb-md u-mb-xl@md'}
{default $specLayout = false}
{default $title = false}
{default $items = []}

<div n:if="count($items)" n:class="c-faqs, $class, !$specLayout ? 'holder holder--lg'">
	<div n:tag-if="!$specLayout" class="row-main row-main--sm">
		<h2 n:if="$title" n:class="!$specLayout ? u-ta-c, h1">{$title}</h2>
		<ul class="c-faqs__list">
			<li n:foreach="$items as $item" n:if="($item->question ?? false) && ($item->answer ?? false)" class="c-faqs__item" data-controller="toggle-class">
				<h3 class="c-faqs__title">
					<button class="c-faqs__link as-link" data-action="toggle-class#toggle">
						{$item->question}
						{('angle-d')|icon, 'c-faqs__icon'}
					</button>
				</h3>
				<div class="c-faqs__more">
					<div class="c-faqs__content u-mb-last-0">
						{$item->answer|noescape}
					</div>
				</div>
			</li>
		</ul>
	</div>
</div>