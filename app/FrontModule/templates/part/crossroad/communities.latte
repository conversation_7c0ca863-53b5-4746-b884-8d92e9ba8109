{default $class = 'u-mb-md u-mb-xl@md'}
{default $communities = $object->cf->communities ?? []}

<section n:if="count($communities)" n:class="c-communities, $class, holder, holder--lg">
	<ul class="c-communities__faculties">
		<li n:foreach="$communities as $community" class="c-communities__faculty u-mb-last-0" data-communities-target="group">
			<p class="c-communities__name-wrap u-mb-xs">
				<span n:if="$community->short ?? false" n:class="faculty" style="background-color: {($community->color ?? '#7a99ac')|noescape}">{$community->short}</span>
				<span n:if="$community->name ?? false" class="c-communities__title h5 u-tt-u u-mt-0 u-mb-0">{$community->name}</span>
			</p>
			<ul n:if="count($community->items ?? [])" class="c-communities__list">
				{foreach $community->items as $item}
					{var $faculties = isset($item->faculties) ? $item->faculties->fetchPairs('faculty->id', name) : []}
					<li class="c-communities__item" data-faculty="{array_keys($faculties)|implode:','}" {*data-faculty="{if $faculty}{$faculty->getParent()->internalName}{/if}"*} data-communities-target="item">
						<span class="item-icon">
							<a class="item-icon__text" href="{$item->link ?? '#'|externalLink}" target="_blank" rel="noopener noreferrer">
								{if $item->name ?? false}{$item->name}{elseif $item->link ?? false}{$item->link}{/if}
								{* {$faculties ? '(' . ($faculties|implode:', ') . ')'} *}
							</a>
							{('link')|icon, 'item-icon__icon'}
						</span>
						{if $item->desc ?? false}- {$item->desc}{/if}
					</li>
				{/foreach}
			</ul>
		</li>
	</ul>

	<p class="u-mb-0 u-d-n" data-communities-target="empty">
		{_"communities_empty"}
	</p>
</section>
