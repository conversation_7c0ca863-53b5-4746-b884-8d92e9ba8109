{varType App\Model\Pages $pages}

{foreach [classmateTip, socialsTip, communityTip, podcastTip] as $tipName}
	{var $cta = $object->cf->{'cta_' . $tipName} ?? false}
	{if $cta && $pages->$tipName?->isPublished()}
		{include $templates . '/part/box/cta.latte',
			class: false,
			image: isset($cta->image) ? $cta->image->getEntity() ?? false : false,
			title: $cta->title ?? false,
			annot: $cta->annot ?? false,
			btnText: $cta->btnText ?? false,
			link: $presenter->link($pages->$tipName),
			snippetId: $snippetId,
		}
	{/if}
{/foreach}
