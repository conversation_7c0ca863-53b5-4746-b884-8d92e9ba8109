{default $class = 'u-mb-md u-mb-xl@md'}
{default $groups = $object->cf->links ?? []}

<div n:if="count($groups)" n:class="c-schedule, $class, holder, holder--lg, u-mb-last-0">
	<div n:foreach="$groups as $group" class="u-mb-md u-mb-lg@md">
		<h2 n:if="$group->title ?? false" class="title-underlined h1">{$group->title}</h2>
		<ul n:if="$group->items ?? []" class="c-schedule__list grid">
			<li n:foreach="$group->items as $item" n:if="($item->text ?? false) && ($item->link ?? false)" class="c-schedule__item grid__cell size--6-12@md">
				<p class="c-schedule__label">
					<span class="item-icon">
						{('link')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							<a href="{$item->link}" class="c-schedule__link h4 u-d-b" target="_blank" rel="noopener noreferrer">
								{$item->text}
							</a>
							{$item->desc ?? false}
						</span>
					</span>
				</p>
			</li>
		</ul>
	</div>
</div>