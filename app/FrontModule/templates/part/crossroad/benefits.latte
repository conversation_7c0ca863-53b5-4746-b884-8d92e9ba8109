{default $class = "u-mb-md u-mb-xl@md"}
{default $specLayout = false}
{default $title = false}
{default $titleLang = false}
{default $items = $object->crossroad ?? []}
{default $pager = true}
{default $showMore = false}
{default $carousel = false}
{default $titleTag = 'h2'}

<section n:if="count($items) || $pager" n:class="c-benefits, $class, !$specLayout ? 'holder holder--lg'">
	<h2 n:if="$titleLang || $title" n:class="!$specLayout ? u-ta-c, 'u-mb-sm u-mb-md@md'">
		{if $title}
			{_$title}
		{elseif $titleLang}
			{_$titleLang}
		{/if}
	</h2>
	{if count($items)}
		<div n:tag-if="$carousel" class="embla" data-controller="embla">
			<div n:tag-if="$carousel" class="embla__holder">
				<div n:tag-if="$carousel" class="embla__viewport" data-embla-target="viewport">
					<div n:class="c-benefits__list, grid, grid--x-md, grid--y-md, $carousel ? 'grid--scroll embla__container'" n:snippet="articleList" data-ajax-append>
						<div n:foreach="$items as $item" class="c-benefits__item grid__cell size--6-12@md">
							{include '../box/benefit.latte', item: $item, class: false}
						</div>
					</div>
				</div>

				{if $carousel}
					<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
						{('angle-l')|icon}
						<span class="u-vhide">{_btn_prev}</span>
					</button>
					<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
						{('angle-r')|icon}
						<span class="u-vhide">{_btn_next}</span>
					</button>
				{/if}
			</div>
		</div>

		{if $pager}
			{snippet articlesPagerBottom}
				{control pager, []}
			{/snippet}
		{/if}
	{else}
		<p class="h2 u-ta-c u-mb-0">
			{_"empty_benefits"}
		</p>
	{/if}

	<p n:if="$showMore && isset($pages->benefits)" class="u-ta-c u-mb-0 u-pt-sm u-pt-md@md">
		<a href="{plink $pages->benefits}" class="btn btn--secondary btn--outline">
			<span class="btn__text">
				{_"btn_show_more"}
			</span>
		</a>
	</p>
</section>
