{default $class = 'u-mb-md u-mb-xl@md'}
{default $specLayout = false}
{default $title = false}
{default $items = []}

<section n:if="count($items)" n:class="c-vcards, $class, !$specLayout ? 'holder holder--lg'">
	<h2 n:if="$title" n:class="!$specLayout ? u-ta-c, 'u-mb-sm u-mb-md@md'">
		{$title}
	</h2>
	<ul class="c-vcards__list grid grid--center">
		<li n:foreach="$items as $item" class="c-vcards__item grid__cell grid__cell--grow size--6-12@xl">
			{include $templates.'/part/box/contact.latte', class: false, item: $item}
		</li>
	</ul>
</section>