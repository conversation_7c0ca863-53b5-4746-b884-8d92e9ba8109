{default $class = false}
{php $hasContent = ($item->showContent ?? false) && ($item->content ?? false)}

<div n:ifcontent n:class="b-highlight, $class, !$hasContent ? holder, u-mb-last-0">
	{if $hasContent}
		{php $link = false}
		{php $btnText = false}
		{php $btn = $item->content->btn ?? false}
		{if $btn}
			{php $type = $btn->toggle}
			{php $page = isset($btn->systemHref) && isset($btn->systemHref->page) ? $btn->systemHref->page->getEntity() ?? false : false}
			{php $hrefName = ($btn->systemHref??->hrefName ?? false) ?: ($btn->customHref??->hrefName ?? false)}
			{php $href = $btn->customHref??->href ?? false}

			{if $type == 'systemHref' && $page}
				{capture $link}{plink $page}{/capture}
				{php $link = $link->__toString()}
				{if $hrefName}
					{php $btnText = $hrefName}
				{else}
					{php $btnText = $page->nameAnchor}
				{/if}
			{elseif $type == 'customHref' && $href && $hrefName}
				{php $link = $href}
				{php $btnText = $hrefName}
			{/if}
		{/if}

		{include $templates.'/part/box/cta.latte', class: 'b-cta--full', title: $item->content->title ?? false, annot: $item->content->annot ?? false, link: $link, btnText: $btnText}
	{else}
		<p n:if="$item->number ?? false" class="b-highlight__title">{$item->number}</p>
		<p n:if="$item->text ?? false" class="b-highlight__text">{$item->text}</p>
	{/if}
</div>