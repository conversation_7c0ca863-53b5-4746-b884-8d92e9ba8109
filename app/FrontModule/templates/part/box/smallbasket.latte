{varType App\FrontModule\Components\Cart\Cart $control}
{varType Nextras\Orm\Collection\ICollection $products}
{varType Nextras\Orm\Collection\ICollection $vouchers}
{varType Nette\Application\UI\Form $form}

{default $class = false}

<div n:class="b-smallbasket, $class">
	{if $products->count() === 0}
		<p>
			{_"empty_smallbasket_text"}
		</p>
	{else}
		<form n:name="$form" data-naja class="block-loader">
			<ul class="b-smallbasket__list">
				<li class="b-smallbasket__item" n:foreach="$products as $productItem">
					{php $product = $productItem->variant->product}
					{varType App\Model\Orm\Order\Product\ProductItem $productItem}
					{var $link = $presenter->link($product, ['v' => $productItem->variant->id])}

					<a href="{$link}" class="b-smallbasket__img img img--contain">
						{if $product->firstImage}
							{php $img = $product->firstImage->getSize('xs')}
							<img src="{$img->src}" alt="{$productItem->getName()}" loading="lazy">
						{else}
							<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
						{/if}
					</a>
					<a href="{$link}" class="b-smallbasket__link">
						{$productItem->getName()}
					</a>
					<span class="b-smallbasket__count">
						{include $templates.'/part/form/part/count.latte', autosubmit: true, class: false, variant: $productItem->variant, input: $form['products'][$productItem->variant->id]['quantity'], maxAmount: $productItem->getMaxAvailableAmount()}
					</span>
					<b class="b-smallbasket__price">
						{*$productItem->unitPriceVat|money*}
						{$productItem->totalPriceVat|money} {* TODO: vybrat aka suma tam ma byt, ci za KS alebo celkova *}
					</b>
					<a n:href="deleteItem! variantId: $productItem->variant->id" data-naja data-naja-history="off" class="b-smallbasket__remove">
						{('close')|icon}
					</a>
				</li>
				<li n:foreach="$vouchers as $voucherItem" class="b-smallbasket__item">
					{varType App\Model\Orm\Order\Voucher\VoucherItem $voucherItem}
					<span class="b-smallbasket__img img img--contain"></span>
					<span class="b-smallbasket__link">
						{$voucherItem->getName()}
					</span>
					<span class="b-smallbasket__count">

					</span>
					<b class="b-smallbasket__price">
						{$voucherItem->unitPriceVat|money}
					</b>
					<a n:href="deleteVoucherItem! voucherCodeId: $voucherItem->voucherCode->id" data-naja data-naja-history="off" class="b-smallbasket__remove">
						{('close')|icon}
					</a>
				</li>
			</ul>
			<div class="block-loader__loader"></div>
		</form>

		<hr>

		<p n:ifcontent class="u-ta-c">
			{control freeDelivery}
		</p>

		<p class="u-ta-c u-mb-0">
			<a href="{plink $pages->cart}" class="btn">
				<span class="btn__text">
					{_"btn_go_to_basket"}
				</span>
			</a>
		</p>
	{/if}
</div>
