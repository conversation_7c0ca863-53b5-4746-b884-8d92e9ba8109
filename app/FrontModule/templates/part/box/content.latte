{default $content = $object->content ?? false}
{default $specLayout = false}
{default $class = 'u-mb-md u-mb-xl@md'}
{default $limit = true}

<div n:if="$content" n:class="b-content, $class, !$limit ? u-mb-last-0, !$specLayout ? 'holder holder--lg'">
	<div n:tag-if="$limit && !$specLayout" class="u-maw-7-12 u-mx-auto u-mb-last-0">
		{$content|tables|lazyLoading|obfuscateEmailAddresses|noescape}
	</div>
</div>
