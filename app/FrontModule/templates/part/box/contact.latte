{default $class = false}
{default $item = false}

<article n:class="b-vcard, $class">
	<div class="b-vcard__side">
		<div class="b-vcard__img img">
			{if isset($item->photo) ? $item->photo->getEntity() ?? false : false}
				<img src="{$item->photo->getSize('md')->src}" alt="{$item->name ?? false}" loading="lazy">
			{else}
				<img src="/static/img/illust/person.svg" alt="" loading="lazy">
			{/if}
		</div>
	</div>
	<div class="b-vcard__content u-mb-last-0">
		<div class="b-vcard__title u-mb-last-0">
			<p n:if="$item->name_prefix ?? false" class="title title--xs title--secondary-darken">
				<span class="title__item">{$item->name_prefix}</span>
			</p>
			<h3 n:if="$item->name ?? false" class="title title--sm title--secondary">
				{php $words = explode(' ', $item->name)}
				<span n:foreach="$words as $word" class="title__item">{$word}</span>
			</h3>
			<p n:if="$item->name_suffix ?? false" class="title title--xs title--secondary-darken">
				<span class="title__item">{$item->name_suffix}</span>
			</p>
		</div>
		<div n:if="$item->annot ?? false" class="b-vcard__text u-mb-last-0">
			<p>
				{$item->annot|texy|noescape}
			</p>
		</div>
		<p n:if="$item->possition ?? false" class="b-vcard__position">
			{$item->position}
		</p>
		<p n:ifcontent class="b-vcard__contacts u-mb-last-0">
			<a n:if="$item->phone ?? false" href="tel:{$item->phone|replace:' ',''}" class="item-icon">
				{('phone')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{$item->phone}
				</span>
			</a>
			<a n:if="$item->email ?? false" href="mailto:{$item->email}" class="item-icon">
				{('message')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{$item->email}
				</span>
			</a>
		</p>
	</div>
</article>