{default $class = false}
{default $titleTag = 'h2'}
{default $title = false}
{default $link = false}
{default $annot = false}
{default $btnText = false}
{default $page = false}
{default $img = false}
{default $external = false}

<article n:class="b-program, b-program--center, $class, u-mb-last-0">
	<h2 n:tag="$titleTag" class="b-program__title h3">
		{$title}
	</h2>
	<p n:if="$img" class="b-program__img img img--3-2 u-mb-0">
		<img src="{$img->getSize('md-3-2')->src}" alt="" loading="lazy">
	</p>
	<div n:ifcontent class="b-program__content u-mb-last-0">
		<p n:if="$annot" class="u-mb-0">
			{$annot|texy|noescape}
		</p>

		{* % vyplnění profilu *}
		<p n:if="$page && $page->uid == 'userProfil'" n:class="$userProfileLocalization->score == 100 ? u-c-green : u-c-orange">
			{if $userProfileLocalization->score == 100}
				{_"profile_score_done"|replace:'%score', (string)$userProfileLocalization->score|noescape}
			{else}
				{_"profile_score_progress"|replace:'%score', (string)$userProfileLocalization->score|noescape}
			{/if}
		</p>

		<p n:if="$btnText" class="b-program__btn">
			<a href="{$link}" class="btn btn--outline btn--secondary btn--sm"{if $external} target="_blank" rel="noopener noreferrer"{/if}>
				<span class="btn__text">
					{$btnText}
				</span>
			</a>
		</p>
	</div>
</article>
