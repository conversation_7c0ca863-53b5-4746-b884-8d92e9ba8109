{default $class = false}

<div n:class="b-avatar, $class" data-controller="avatar">
	<p class="img u-mb-0">
		<img class="b-avatar__img" src="/static/img/illust/person.svg" alt="" loading="lazy">
		<img class="b-avatar__img" n:if="$profile->avatar ?? false" src="{$profile->avatar->getSize('sm')->src}" alt="" loading="lazy">
	</p>
	{if $profile->avatar ?? false}
		<label class="b-avatar__btn b-avatar__btn--remove btn-circle">
			<input type="checkbox" n:name="imageRemove" class="b-avatar__inp u-vhide" data-controller="autosubmit" data-action="autosubmit#submitForm">
			{('close')|icon}
		</label>
	{else}
		<label class="b-avatar__upload">
			<span class="b-avatar__btn b-avatar__btn--upload btn-circle">
				{('upload')|icon}
			</span>
			<input class="b-avatar__inp u-js-hide" type="file" n:name="imageUpload" data-controller="autosubmit" data-action="autosubmit#submitForm">
		</label>
	{/if}
	<p class="u-js-hide u-pt-sm u-mb-0">
		<button type="submit" class="btn">
			<span class="btn__text">
				{_"btn_save"}
			</span>
		</button>
	</p>
</div>