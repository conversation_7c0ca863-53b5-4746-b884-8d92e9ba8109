{default $class = false}
{default $name = $seoLink??->name ?? $object->name}
{default $annotation = $seoLink??->description ?? $object->annotation ?? $object->description ?? null}
{default $cf = $object->cf->headerType ?? false}

<div n:if="$cf" n:class="b-hero-header, $class">
	{php $image = isset($cf->image) ? $cf->image->getEntity() ?? false : false}
	<p n:if="$image" class="b-hero-header__img img u-mb-0">
		<img srcset="
				{$image->getSize('sm')->src} 320w,
				{$image->getSize('md')->src} 560w,
				{$image->getSize('lg')->src} 750w,
				{$image->getSize('xl')->src} 1200w,
				{$image->getSize('2xl')->src} 1920w"
			sizes="(max-width: 90rem) 100vw,
				144rem"
			src="{$image->getSize('2xl')->src}"
			alt="{$image->getAlt($mutation)}" fetchpriority="high">
	</p>
	<div class="b-hero-header__content holder holder--lg">
		<h1 class="title b-hero-header__title">
			{php $words = explode(' ', $name)}
			<span n:foreach="$words as $word" class="title__item">{$word}</span>
		</h1>
		<p n:if="$annotation" class="b-hero-header__subtitle">{$annotation|texy|noescape}</p>


		{php $links = $cf->links ?? []}
		<p n:ifcontent class="b-hero-header__btns">
			{foreach $links as $link}
				{php $type = $link->link->toggle}
				{php $page = isset($link->link->systemHref) && isset($link->link->systemHref->page) ? $link->link->systemHref->page->getEntity() ?? false : false}
				{php $hrefName = ($link->link->systemHref??->hrefName ?? false) ?: ($link->link->customHref??->hrefName ?? false)}
				{php $href = $link->link->customHref??->href ?? false}

				{if $type == 'systemHref' && $page}
					<a href="{plink $page}" n:ifcontent n:class="btn, btn--sm, $link->btnType ?? false">
						<span n:ifcontent class="btn__text">
							{if $hrefName}
								{$hrefName}
							{else}
								{$page->nameAnchor}
							{/if}
						</span>
					</a>
				{elseif $type == 'customHref' && $href && $hrefName}
					<a href="{$href}" n:class="btn, btn--sm, $link->btnType ?? false" target="_blank" rel="noopener noreferrer">
						<span class="btn__text">
							{$hrefName}
						</span>
					</a>
				{/if}
			{/foreach}
		</p>

		<a href="#content" class="b-hero-header__next{*% if heroHeader.bleed %} b-hero-header__next--bleed{% endif %*}">
			{('angle-d')|icon}
			<span class="u-vhide">Číst více</span>
		</a>
	</div>
</div>