{default $class = 'u-mb-md u-mb-xl@md'}
{default $title = false}
{default $subtitle = false}
{default $name = false}
{default $video = false}
{default $image = false}

<div n:class="b-hero, $class">
	<div class="b-hero__content holder holder--lg">
		<div class="b-hero__inner u-mb-last-0">
			<h2 n:if="$title" class="b-hero__title h1">{$title}</h2>
			<p n:if="$subtitle" class="b-hero__subtitle">{$subtitle}</p>
			<p n:if="$name" class="b-hero__annot">{$name}</p>
			<a n:if="$video" href="{$video}" class="playlink b-hero__playlink u-d-n u-d-b@xxl" data-modal>
				<span class="u-vhide">{_"btn_play"}</span>
			</a>
		</div>
	</div>
	<div class="b-hero__img img img--4-3">
		{if $image}
			<img src="{$image->getSize('xl-4-3')->src}" alt="" loading="lazy">
		{else}
			<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
		<a n:if="$video" href="{$video}" class="playlink b-hero__playlink u-hide@xxl" data-modal>
			<span class="u-vhide">{_"btn_play"}</span>
		</a>
	</div>
</div>