{default $class = false}
{default $image = false}
{default $title = false}
{default $annot = false}
{default $btnText = false}
{default $link = false}
{default $snippetId = false}

<a href="{$link}" n:class="b-cta, $class"{if $snippetId} data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}' data-snippetid="{$snippetId}"{/if}>
	<div n:if="$image" class="b-cta__img img">
		<img srcset="
				{$image->getSize('sm')->src} 320w,
				{$image->getSize('md')->src} 560w,
				{$image->getSize('lg')->src} 750w,
				{$image->getSize('xl')->src} 1200w,
				{$image->getSize('2xl')->src} 1920w"
			sizes="(max-width: 90rem) 100vw,
				1440rem"
			src="{$image->getSize('2xl')->src}"
			alt="{$image->getAlt($mutation)}" loading="lazy">
	</div>
	<div class="b-cta__content">
		<div class="holder holder--lg u-mb-last-0">
			<h2 n:if="$title" n:class="b-cta__title, $image ? title : h1">
				{if $image}
					{php $words = explode(' ', $title)}
					<span n:foreach="$words as $word" class="title__item">{$word}</span>
				{else}
					{$title}
				{/if}
			</h2>
			<p n:if="$annot" n:class="b-cta__subtitle, !$image ? u-tt-u">{$annot|texy|noescape}</p>
			<p n:if="$btnText">
				<span class="btn btn--sm">
					<span class="btn__text">{$btnText}</span>
				</span>
			</p>
		</div>
	</div>
</a>