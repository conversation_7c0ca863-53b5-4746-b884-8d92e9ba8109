{* Nastaveni viditelnosti: {embed section name: ...} (hodnoty viditelností viz ProfileVisibilitySetting::keys()) *}

{varType App\PostType\Profile\Model\Orm\ProfileLocalization $profileLocalization}
{varType App\Model\Pages $pages}

{var $profile = $profileLocalization->getParent()}
{var $sections = $profileLocalization->sectionsTotal}

{default $class = false}
{default $titleTag = 'h1'}

{define section}
	{if ($profile|sectionVisible:$name)}
		{block sectionContent}{/block}
	{/if}
{/define}

{var $name = $sections->name}
{var $fullName = $name->getFullName(exclude: [titlesBefore, titlesAfter])}


<div n:class="b-profile, $class" data-controller="tabs">
	<div class="b-profile__head">
		<div class="b-profile__info holder holder--lg u-mb-last-0">
			<div class="b-profile__title">
				<p n:ifcontent class="title title--xs title--secondary-darken">
					<span n:ifcontent class="title__item">{$name->titlesBefore}</span>
				</p>
				<h1 n:tag="$titleTag" class="title title--secondary h1">
					{php $words = explode(' ', $fullName)}
					<span n:foreach="$words as $word" class="title__item">{$word}</span>
				</h1>
				<p n:ifcontent class="title title--xs title--secondary-darken">
					<span n:ifcontent class="title__item">{$name->titlesAfter}</span>
				</p>
			</div>

			<p n:if="$profileLocalization->cf->base??->about ?? false" class="b-profile__text">
				{$profileLocalization->cf->base->about|texy|noescape}
			</p>

			{embed section name: employments, profile: $profile}
				{block sectionContent}
					<p n:ifcontent class="b-profile__position">
						{foreach $sections->employments as $employment}
							{varType App\PostType\Profile\Model\Orm\Sections\ProfileEmploymentSection $employment}
							{if $employment->untilNow}{$employment->position}{sep}, {/sep}{/if}
						{/foreach}
					</p>
				{/block}
			{/embed}

			{embed section name: educations, profile: $profile}
				{block sectionContent}
					{if $profile->lastEducation}
						<p class="b-profile__text u-mb-sm">
							{_"graduation_year"}: {$profile->lastEducation->to|date:'Y'}
						</p>
					{/if}
				{/block}
			{/embed}

			{*
				TODO BE:
					- Administrátor vidí, zda je účet aktivní, resp. není zablokovaný
					- Administrátor vidí, zda má absolvent aktivní placené členství, resp. je členem “Absolventského klubu”
			*}

			<p n:if="$profile->verified" class="b-profile__text">
				{_"profile_verified_tooltip"}
				<span class="validated"></span>
			</p>

			{block header}{/block}
		</div>

		<div class="b-profile__img img">
			{php $avatar = $profile->avatar ?? false}

			{if $avatar && ($profile|sectionVisible:avatar)}
				<img srcset="
					{$avatar->getSize('sm')->src} 320w,
					{$avatar->getSize('md')->src} 560w,
					{$avatar->getSize('lg')->src} 750w,
					{$avatar->getSize('xl')->src} 1200w,
					{$avatar->getSize('2xl')->src} 1920w"
				sizes="(max-width: 48rem) 100vw,
					(max-width: 90rem) 57vw,
					820rem"
				src="{$avatar->getSize('2xl')->src}"
				alt="{$fullName}" loading="lazy">
			{else}
				<img src="/static/img/illust/person.svg" alt="" fetchpriority="high">
			{/if}

		</div>
	</div>

	{block content}{/block}
</div>
