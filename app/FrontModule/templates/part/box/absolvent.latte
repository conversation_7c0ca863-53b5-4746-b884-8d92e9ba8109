{varType App\PostType\Profile\Model\Orm\ProfileLocalization $item}

{default $class = false}
{default $type = false}
{default $titleTag = 'h2'}
{default $condensed = false}
{default $followingItem = null}

{var $profile = $item->getParent()}
{var $sections = $item->sectionsTotal}

<article n:class="b-vcard, $class, $condensed ? b-vcard--condensed, link-mask">
	<div class="b-vcard__side">
		<div class="b-vcard__img img">
			{if $profile->avatar && ($profile|sectionVisible:avatar)}
				<img src="{$profile->avatar->getSize('md')->src}" alt="{$item->name ?? false}" loading="lazy">
			{else}
				<img src="/static/img/illust/person.svg" alt="" loading="lazy">
			{/if}
		</div>
		{if $userProfile ?? false}
			<p class="b-vcard__btn u-mb-0">
				{control profileFollows-{$profile->id}}
			</p>
			{*<p class="b-vcard__btn u-mb-0"
				<a href="#" class="btn btn--outline btn--block btn--sm link-mask__unmask">
					<span class="btn__text">{_"btn_invite"}</span>
				</a>
			</p>*}
		{/if}
	</div>
	<div class="b-vcard__content u-mb-last-0">
		<div class="b-vcard__title u-mb-last-0">
			{var $name = $sections->name}
			{var $fullName = $name->getFullName(exclude: [titlesBefore, titlesAfter, lastnameStudy])}

			<p n:if="$name->titlesBefore ?? false" class="title title--xs title--secondary-darken">
				<span class="title__item">{$name->titlesBefore}</span>
			</p>
			<h3 n:tag="$titleTag" class="h3">
				<a href="{plink $item filter => null}" class="b-vcard__link title title--sm title--secondary link-mask__link">
					{php $words = explode(' ', $fullName)}
					<span n:foreach="$words as $word" class="title__item">{$word}</span>
				</a>
			</h3>
			<p n:if="$name->titlesAfter ?? false" class="title title--xs title--secondary-darken">
				<span class="title__item">{$name->titlesAfter}</span>
			</p>
		</div>

		{if !$condensed}
			{embed section name: educations, profile: $profile}
				{block sectionContent}
					{if $item->educations->count()}
						<p class="b-vcard__position u-mb-sm">
							<span n:foreach="$item->educations as $education">
								{implode(' | ', array_filter([$education->facultyLocalization??->name ?? false, $education->programmeLocalization??->name ?? false, $education->branchLocalization??->name ?? false]))|noescape}{sep}, {/sep}
							</span>
						</p>
					{/if}
				{/block}
			{/embed}

			<p n:if="$item->cf->base??->about ?? false">
				{$item->cf->base->about|texy|noescape}
			</p>

			<hr class="u-mt-0 u-mb-sm">

			<div class="b-vcard__text u-mb-last-0">
				<ul>
					<li n:if="$name->lastnameStudy ?? false">
						{_"form_label_lastname_study"}: <b>{$name->lastnameStudy}</b>
					</li>
					{embed section name: educations, profile: $profile}
						{block sectionContent}
							{if $profile->lastEducation}
								<li>
									{_"graduation_year"}: {$profile->lastEducation->to|date:'Y'}
								</li>
							{/if}
						{/block}
					{/embed}
					{embed section name: socials, profile: $profile}
						{block sectionContent}
							{foreach $sections->socials as $type => $url}
								{if $type == 'personal_web'}
									<li>
										<a href="{$url|externalLink}" target="_blank" rel="noopener noreferrer">{$url}</a>
									</li>
								{/if}
							{/foreach}
						{/block}
					{/embed}
					<li n:if="$profile->user !== null && $profile->user->lastLogin !== null">
						{_'profile_last_login'}: {$profile->user->lastLogin|date:'d.m.Y H:i'}
					</li>
					<li n:ifset="$followingItem">
						{if $type == 'followers'}{_"followers_since"}{elseif $type == 'followings'}{_"following_since"}{/if}:
						{$followingItem->since|date:'d.m.Y H:i'}
					</li>
				</ul>
			</div>

			{embed section name: contact, profile: $profile}
				{block sectionContent}
					<p n:ifcontent class="b-vcard__contacts u-mb-last-0">
						<a n:if="$item->phone ?? false" href="tel:{$item->phone|replace:' ',''}" class="item-icon link-mask__unmask">
							{('phone')|icon, 'item-icon__icon'}
							<span class="item-icon__text">
								{$item->phone}
							</span>
						</a>
						<a n:if="$item->email ?? false" href="mailto:{$item->email}" class="item-icon link-mask__unmask">
							{('message')|icon, 'item-icon__icon'}
							<span class="item-icon__text">
								{$item->email}
							</span>
						</a>
					</p>
				{/block}
			{/embed}
		{/if}
	</div>
	<div n:if="$profile->verified" class="b-vcard__validated validated link-mask__unmask" data-controller="tippy" data-tippy-placement-value="left" title="{_'profile_verified_tooltip'}"></div>
</article>

{define section}
	{if ($profile|sectionVisible:$name)}
		{block sectionContent}{/block}
	{/if}
{/define}
