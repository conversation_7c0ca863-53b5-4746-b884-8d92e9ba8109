{default $class = 'u-mb-md u-mb-0@md'}
{default $title = false}
{default $items = []}
{default $type = 'primary'}

<div n:class="c-features, $class">
	<h2 n:if="$title" class="u-ta-c h1 u-mb-md u-pt-md u-pt-xl@md">
		{$title}
	</h2>
	{foreach $items as $item}
		<div n:class="b-feature, 'b-feature--' . $type">
			<div class="b-feature__content holder holder--lg u-mb-last-0">
				<h2 class="b-feature__title h1">
					{$item->title ?? false}
				</h2>
				<p n:if="$item->content ?? false" class="u-fz-lg">
					{$item->content|texy|noescape}
				</p>
				<p n:if="$item->btn ?? false">
					{php $toggle = $item->btn->toggle}
					{php $page = isset($item->btn->systemHref) && isset($item->btn->systemHref->page) ? $item->btn->systemHref->page->getEntity() ?? false : false}
					{php $hrefName = ($item->btn->systemHref??->hrefName ?? false) ?: ($item->btn->customHref??->hrefName ?? false)}
					{php $href = $item->btn->customHref??->href ?? false}


					{if $toggle == 'systemHref' && $page}
						<a href="{plink $page}" n:ifcontent n:class="btn, btn--outline, btn--sm, $type == 'secondary' ? btn--secondary">
							<span class="btn__text">
								{if $hrefName}
									{$hrefName}
								{else}
									{$page->nameAnchor}
								{/if}
							</span>
						</a>
					{elseif $toggle == 'customHref' && $href && $hrefName}
						<a href="{$href}" n:class="btn, btn--outline, btn--sm, $type == 'secondary' ? btn--secondary" target="_blank">
							<span class="btn__text">
								{$hrefName}
							</span>
						</a>
					{/if}
				</p>
			</div>
			<div n:if="isset($item->media->toggle)" class="b-feature__img holder holder--lg">
				{php $image = isset($item->media->image) ? $item->media->image->getEntity() ?? false : false}

				{if $item->media->toggle == 'video' && ($item->media->video ?? false)}
					{php $link = $item->media->video}

					<a href="{$link}" class="b-feature__thumbnail b-feature__thumbnail--video img img--4-3" data-modal>
						{if $image}
							<img src="{$image->getSize('lg-4-3')->src}" alt="" loading="lazy">
						{elseif strpos($link, 'youtube')}
							{php $urlObject =  new \Nette\Http\Url($link)}
							{php $id = $urlObject->getQueryParameter('v')}

							{include $templates.'/part/core/video-thumb.latte', id: $id}
						{elseif strpos($link, 'vimeo')}
							{var $videoId = pathinfo(explode('/', $link)[count(explode('/', $link))-1], PATHINFO_FILENAME)}
							<img src="https://vumbnail.com/{$id}.jpg" alt="" loading="lazy">
						{/if}
						<span class="b-feature__playlink playlink">
							<span class="u-vhide">{_"btn_play"}</span>
						</span>
					</a>
				{elseif $item->media->toggle == 'image' && $image}
					<div class="b-feature__thumbnail img img--4-3">
						<img src="{$image->getSize('lg-4-3')->src}" alt="" loading="lazy">
					</div>
				{/if}
			</div>
		</div>
	{/foreach}
</div>