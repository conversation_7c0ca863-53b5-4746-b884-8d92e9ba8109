{default $class = false}
{default $titleTag = 'h2'}
{default $article = false}
{default $showDate = true}
{default $showFaculty = false}

{php $externalUrl = $article->cf->settings->external_link ?? false}

<article n:if="$article" class="b-article">
	{capture $articleBlock}
		{php $imgCrossroad = isset($article->cf->base->crossroadImg) ? $article->cf->base->crossroadImg->getEntity() ?? false : false}
		{php $imgMain = isset($article->cf->base->mainImage) ? $article->cf->base->mainImage->getEntity() ?? false : false}
		{php $img = $imgCrossroad ? $imgCrossroad : $imgMain}

		<div n:if="$img" class="b-article__img img img--3-2">
			<img src="{$img->getSize('lg-3-2')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
		</div>
		<div class="b-article__content u-mb-last-0">
			<p n:ifcontent class="b-article__meta font-secondary">
				{if $showDate}
					{capture $monthNumber}{$article->publicFrom|date:"n"}{/capture}
					<time datetime='{$article->publicFrom|date:"Y-m-d"}'>{$article->publicFrom|date:"j."} {_"month_".$monthNumber->__toString()} {$article->publicFrom|date:"Y"}</time>
				{elseif $showFaculty}
					{$article->facultyLocalization??->name ?? false}
				{/if}
			</p>
			<h2 n:tag="$titleTag" n:class="b-article__title, h3">
				{$article->nameTitle}
				{if $externalUrl}{('link')|icon}{/if}
			</h2>
		</div>
	{/capture}

	{if $externalUrl}
		<a href="{$externalUrl|externalLink}" class="b-article__link" target="_blank" rel="noopener noreferrer">{$articleBlock}</a>
	{else}
		<a href="{plink $article filter => null}" class="b-article__link">{$articleBlock}</a>
	{/if}
</article>
