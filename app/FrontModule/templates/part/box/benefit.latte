{default $class = false}
{default $item = false}
{default $titleTag = 'h2'}

<a href="{plink $item filter => null}" n:class="b-benefit, $class">
	{php $img = isset($item->cf->base->mainImage) ? $item->cf->base->mainImage->getEntity() ?? false : false}
	<div class="b-benefit__img img">
		{if $img}
			<img src="{$img->getSize('sm')->src}" alt="" loading="lazy">
		{else}
			<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
	</div>
	<div class="b-benefit__content">
		<h2 n:tag="$titleTag" class="b-benefit__title h3">{$item->nameAnchor}</h2>
		<p n:ifcontent class="b-benefit__text u-mb-0">
			{$item->description}
		</p>
	</div>
</a>
