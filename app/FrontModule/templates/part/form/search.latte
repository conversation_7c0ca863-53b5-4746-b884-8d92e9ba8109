<div n:if="$pages->search !== null" class="header__search">
	{* <form action='{link $pages->search, suggest=>1, search=>null, filter=>null}' id="form-search" class="f-search" data-controller="etarget suggest"data-suggest='{link $pages->search, suggest=>1, search=>null, filter=>null}'> *}
	<form action='{link $pages->search, suggest=>0, search=>null, filter=>null}' id="form-search" class="f-search" data-controller="etarget">
		<p class="inp inp--group inp--group--m u-mb-0">
			<label for="search" class="u-vhide">{_"search_placeholder"}</label>
			<span class="inp__fix inp__fix--btn-icon">
				<input type="text" class="inp__text inp__text--lg inp__text--flat" id="search" name="search"{* data-suggest-target="input"*} placeholder='{_"search_placeholder"}' data-focus autocomplete="off">
				<button class="btn btn--lg btn--icon-only btn--blank inp__btn-icon" type="submit">
					<span class="btn__text">
						<span class="u-vhide">{_"btn_search"}</span>
						{('search')|icon, 'btn__icon'}
					</span>
				</button>
			</span>
			<span class="inp__btn">
				<button type="button" class="btn btn--lg btn--secondary btn--icon-only" data-controller="toggle-class" data-action="toggle-class#toggle" data-toggle-content=".f-search">
					<span class="btn__text">
						<span class="u-vhide">{_"btn_close"}</span>
						{('close')|icon, 'btn__icon'}
					</span>
				</button>
			</span>
		</p>

		{* <div data-suggest-target="wrap"></div> *}
	</form>
</div>