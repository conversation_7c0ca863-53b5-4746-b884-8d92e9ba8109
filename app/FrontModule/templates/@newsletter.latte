<!DOCTYPE html>
<html lang="{$mutation->langCode}">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<title>
			{var $nameTitle = $seoLink??->nameTitle ?? $object->nameTitle}
				{if $nameTitle !== null}
				{$nameTitle}
			{/if}
				{if !$isHomepage} {* Přídavek za title, který se dává jen pro ne homepage stránky *}
				| {_title}
			{/if}
		</title>

		<!-- Responzivní styly -->
		<!--[if !mso]><!-->
			<style>
				@media screen and (max-width: 599px) {
					img {
						max-width: 100% !important;
						height: auto !important;
					}

					a[href^="mailto"] {
						word-break: break-all;
					}

					col,
					.rwd-hide,
					.rwd-block-table-all .rwd-hide {
						display: none !important;
					}

					.rwd-full {
						width: 100% !important;
					}

					.rwd-block-table,
					.rwd-block-table > tbody,
					.rwd-block-table-all,
					.rwd-block-table-all tbody,
					.rwd-block-table-all table{
						width: 100% !important;
						display: block !important;
					}
					.rwd-block,
					.rwd-block-table > tbody > tr,
					.rwd-block-table > tbody > tr > td,
					.rwd-block-table > tbody > tr > th,
					.rwd-block-table-all tr,
					.rwd-block-table-all td,
					.rwd-block-table-all th {
						display: block !important;
						width: auto !important;
					}

					.rwd-clearfix {
						overflow: hidden;
					}

					.rwd-p-sm { padding: 20px !important; }
					.rwd-pl-sm { padding-left: 20px !important; }
					.rwd-pr-sm { padding-right: 20px !important; }
					.rwd-pt-sm { padding-top: 20px !important; }
					.rwd-pb-sm { padding-bottom: 20px !important; }

					.rwd-p-0 { padding: 0px !important; }
					.rwd-pl-0 { padding-left: 0px !important; }
					.rwd-pr-0 { padding-right: 0px !important; }
					.rwd-pt-0 { padding-top: 0px !important; }
					.rwd-pb-0 { padding-bottom: 0px !important; }

					.rwd-m-sm { margin: 20px !important; }
					.rwd-ml-sm { margin-left: 20px !important; }
					.rwd-mr-sm { margin-right: 20px !important; }
					.rwd-mt-sm { margin-top: 20px !important; }
					.rwd-mb-sm { margin-bottom: 20px !important; }

					.rwd-m-0 { margin: 0px !important; }
					.rwd-ml-0 { margin-left: 0px !important; }
					.rwd-mr-0 { margin-right: 0px !important; }
					.rwd-mt-0 { margin-top: 0px !important; }
					.rwd-mb-0 { margin-bottom: 0px !important; }

					.rwd-ta-center { text-align: center !important; }
					.rwd-ta-left { text-align: left !important; }
					.rwd-ta-right { text-align: right !important; }

					/* CUSTOM CSS */
					.t-header__claim {
						margin: 20px 0 !important;
					}
					.t-header__claim h1 {
						text-align: left !important;
					}

					.t-btn a {
						display: block !important;
						width: 100% !important;
					}

					.t-article__content {
						padding: 10px 0 0 !important;
					}
				}
			</style>
		<!--<![endif]-->
	</head>
	<body>
		<table class="t-wrap rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
			<tbody>
				<tr>
					<td class="t-wrap__inner">
						<table class="t-container rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr>
									<td class="t-container__inner">
										<table class="t-header rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="t-header__inner">
														<table class="rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
															<tbody>
																<tr>
																	<td class="t-header__logo">
																		<a href="#">
																			<img src="/static/img/newsletter/logo.png" alt="{_newsletter_logo}" width="200">
																		</a>
																	</td>
																	<td class="t-header__claim">
																		<h1 class="u-mb-0">
																			{$object->name}
																		</h1>
																	</td>
																</tr>
															</tbody>
														</table>
													</td>
												</tr>
											</tbody>
										</table> <!-- t-header -->

										<table class="t-main rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="t-main__inner">
														{include #content}
													</td>
												</tr>
											</tbody>
										</table> <!-- t-main -->


										<table class="t-footer rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="t-footer__inner">
														<table class="rwd-block-table" width="100%" border="0" cellspacing="0" cellpadding="0">
															<tbody>
																<tr>
																	<td class="t-footer__social u-pb-sm u-ta-center">
																		<a n:if="isset($object->cf->newsletter->social->facebook) && $object->cf->newsletter->social->facebook" href="{$object->cf->newsletter->social->facebook}"><img src="/static/img/newsletter/icon-facebook.png" alt="Facebook" width="50" height="50"></a>
																		&nbsp;
																		<a n:if="isset($object->cf->newsletter->social->x) && $object->cf->newsletter->social->x" href="{$object->cf->newsletter->social->x}"><img src="/static/img/newsletter/icon-x.png" alt="X" width="50" height="50"></a>
																		&nbsp;
																		<a n:if="isset($object->cf->newsletter->social->instagram) && $object->cf->newsletter->social->instagram" href="{$object->cf->newsletter->social->instagram}"><img src="/static/img/newsletter/icon-instagram.png" alt="Instagram" width="50" height="50"></a>
																		&nbsp;
																		<a n:if="isset($object->cf->newsletter->social->youtube) && $object->cf->newsletter->social->youtube" href="{$object->cf->newsletter->social->youtube}"><img src="/static/img/newsletter/icon-youtube.png" alt="YouTube" width="50" height="50"></a>
																	</td>
																</tr>
															</tbody>
														</table>
														<table class="rwd-block-table" n:if="isset($object->cf->newsletter->footer) && $object->cf->newsletter->footer" width="100%" border="0" cellspacing="0" cellpadding="0">
															<tbody>
																<tr>
																	<td class="t-footer__content">
																		<p>
																			{$object->cf->newsletter->footer|texy|noescape}
																		</p>
																	</td>
																</tr>
															</tbody>
														</table>
													</td>
												</tr>
											</tbody>
										</table> <!-- t-footer -->
									</td>
								</tr>
							</tbody>
						</table>

					</td>
				</tr>
			</tbody>
		</table>
	</body>
</html>
