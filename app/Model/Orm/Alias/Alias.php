<?php declare(strict_types=1);

namespace App\Model\Orm\Alias;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Orm\Traits\HasTreeRepository;
use App\PostType\Benefit\Model\Orm\BenefitLocalization;
use App\PostType\Benefit\Model\Orm\BenefitLocalizationRepository;
use App\PostType\BenefitTag\Model\Orm\BenefitTag\BenefitTagLocalizationRepository;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalizationRepository;
use App\PostType\Calendar\Model\Orm\HasCalendarRepository;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalizationRepository;
use App\PostType\Education\Model\Orm\EducationLocalization;
use App\PostType\Education\Model\Orm\EducationLocalizationRepository;
use App\PostType\EducationTag\Model\Orm\EducationTag\EducationTagLocalization;
use App\PostType\EducationTag\Model\Orm\EducationTag\EducationTagLocalizationRepository;
use App\PostType\Newsletter\Model\Orm\NewsletterLocalization;
use App\PostType\Newsletter\Model\Orm\NewsletterLocalizationRepository;
use App\PostType\Offer\Model\Orm\OfferLocalization;
use App\PostType\Offer\Model\Orm\OfferLocalizationRepository;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Blog\Model\Orm\HasBlogRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\Profile\Model\Orm\HasProfileRepository;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\PostType\Profile\Model\Orm\ProfileLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use App\PostType\Story\Model\Orm\StoryLocalization;
use App\PostType\Story\Model\Orm\StoryLocalizationRepository;
use App\PostType\StoryTag\Model\Orm\StoryTag\StoryTagLocalization;
use App\PostType\StoryTag\Model\Orm\StoryTag\StoryTagLocalizationRepository;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $alias
 * @property string $module {enum self::MODULE_*}
 * @property int $referenceId
 *
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$aliases}
 *
 *
 * VIRTUALS
 * @property-read Tree $parent {virtual}
 * @property-read string $name {virtual}
 */
final class Alias extends Entity
{

	use HasTreeRepository;
	use HasBlogRepository;
	use HasCalendarRepository;
	use HasProfileRepository;

	public const MODULE_TREE = 'tree';
	public const MODULE_BLOG = 'blogLocalization';
	public const MODULE_BLOG_TAG = 'blogTagLocalization';
	public const MODULE_CALENDAR = 'calendarLocalization';
	public const MODULE_CALENDAR_TAG = 'calendarTagLocalization';
	public const MODULE_PRODUCT = 'productLocalization';
	public const MODULE_SEOLINK = 'seoLinkLocalization';
	public const MODULE_PROFILE = 'profileLocalization';
	public const MODULE_PROFILE_TAG = 'profileTagLocalization';
	public const MODULE_BENEFIT = 'benefitLocalization';
	public const MODULE_BENEFIT_TAG = 'benefitTagLocalization';

	public const MODULE_STORY = 'storyLocalization';
	public const MODULE_STORY_TAG = 'storyTagLocalization';

	public const MODULE_EDUCATION = 'educationLocalization';
	public const MODULE_EDUCATION_TAG = 'educationTagLocalization';
	public const MODULE_OFFER = 'offerLocalization';

	public const MODULE_NEWSLETTER = 'newsletterLocalization';

	private BlogLocalizationRepository $blogLocalizationRepository;

	private BlogTagLocalizationRepository $blogTagLocalizationRepository;

	private CalendarLocalizationRepository $calendarLocalizationRepository;
	private CalendarTagLocalizationRepository $calendarTagLocalizationRepository;

	private BenefitLocalizationRepository $benefitLocalizationRepository;
	private BenefitTagLocalizationRepository $benefitTagLocalizationRepository;

	private ProductLocalizationRepository $productLocalizationRepository;

	private SeoLinkLocalizationRepository $seoLinkLocalizationRepository;

	private ProfileLocalizationRepository $profileLocalizationRepository;

	private StoryLocalizationRepository $storyLocalizationRepository;
	private StoryTagLocalizationRepository $storyTagLocalizationRepository;

	private EducationLocalizationRepository $educationLocalizationRepository;
	private EducationTagLocalizationRepository $educationTagLocalizationRepository;
	private OfferLocalizationRepository $offerLocalizationRepository;

	private NewsletterLocalizationRepository $newsletterLocalizationRepository;

	public function injectRepositories(
		BlogTagLocalizationRepository $blogTagLocalizationRepository,
		BlogLocalizationRepository $blogLocalizationRepository,
		ProductLocalizationRepository $productLocalizationRepository,
		SeoLinkLocalizationRepository $seoLinkLocalizationRepository,
		ProfileLocalizationRepository $profileLocalizationRepository,
		CalendarLocalizationRepository $calendarLocalizationRepository,
		CalendarTagLocalizationRepository $calendarTagLocalizationRepository,
		BenefitLocalizationRepository $benefitLocalizationRepository,
		BenefitTagLocalizationRepository $benefitTagLocalizationRepository,
		StoryLocalizationRepository $storyLocalizationRepository,
		StoryTagLocalizationRepository $storyTagLocalizationRepository,
		EducationLocalizationRepository $educationLocalizationRepository,
		EducationTagLocalizationRepository $educationTagLocalizationRepository,
		OfferLocalizationRepository $offerLocalizationRepository,
		NewsletterLocalizationRepository $newsletterLocalizationRepository
	): void
	{
		$this->blogLocalizationRepository = $blogLocalizationRepository;
		$this->blogTagLocalizationRepository = $blogTagLocalizationRepository;
		$this->productLocalizationRepository = $productLocalizationRepository;
		$this->seoLinkLocalizationRepository = $seoLinkLocalizationRepository;
		$this->profileLocalizationRepository = $profileLocalizationRepository;
		$this->calendarLocalizationRepository = $calendarLocalizationRepository;
		$this->calendarTagLocalizationRepository = $calendarTagLocalizationRepository;
		$this->benefitLocalizationRepository = $benefitLocalizationRepository;
		$this->benefitTagLocalizationRepository = $benefitTagLocalizationRepository;
		$this->storyLocalizationRepository = $storyLocalizationRepository;
		$this->storyTagLocalizationRepository = $storyTagLocalizationRepository;
		$this->educationLocalizationRepository = $educationLocalizationRepository;
		$this->educationTagLocalizationRepository = $educationTagLocalizationRepository;
		$this->offerLocalizationRepository = $offerLocalizationRepository;
		$this->newsletterLocalizationRepository = $newsletterLocalizationRepository;
	}

	public function __toString(): string
	{
		return $this->isPersisted() ? $this->alias : '';
	}

	protected function getterParent(): Tree|BlogLocalization|CalendarLocalization|BenefitLocalization|BlogTagLocalization|ProductLocalization|SeoLinkLocalization|ProfileLocalization|StoryLocalization|StoryTagLocalization|EducationLocalization|EducationTagLocalization|OfferLocalization|NewsletterLocalization|null
	{
		$repository = $this->module . 'Repository';
		return $this->$repository->getById($this->referenceId);
	}

	protected function getterName(): string
	{
		return $this->alias;
	}

}
