<?php declare(strict_types = 1);

namespace App\Model;

use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use Tracy\Debugger;

/**
 * @property-read ?CommonTree $lostPassword
 * @property-read ?CommonTree $resetPassword
 * @property-read ?CommonTree $reactivation
 * @property-read ?CommonTree $search
 * @property-read ?CommonTree $blog
 * @property-read ?CommonTree $userSection
 * @property-read ?CommonTree $userLogin
 * @property-read ?CommonTree $userProfil
 * @property-read ?CommonTree $userDeactivate
 * @property-read ?CommonTree $userVerification
 * @property-read ?CommonTree $registration
 * @property-read ?CatalogTree $eshop
 * @property-read ?CommonTree $title
 * @property-read ?CommonTree $precart
 * @property-read ?CommonTree $cart
 * @property-read ?CommonTree $step1
 * @property-read ?CommonTree $step2
 * @property-read ?CommonTree $step3
 * @property-read ?CommonTree $userOrderHistory
 * @property-read ?CommonTree $calendar
 * @property-read ?CommonTree $absolvents
 * @property-read ?CommonTree $benefit
 * @property-read ?CommonTree $story
 * @property-read ?CommonTree $education
 * @property-read ?CommonTree $classmateTip
 * @property-read ?CommonTree $socialsTip
 * @property-read ?CommonTree $communityTip
 * @property-read ?CommonTree $podcastTip
 */
final class Pages
{

	private array $cache = [];

	private ?array $uidList = null;

	public function __construct(
		private readonly Orm $orm,
		private readonly Mutation $mutation,
	)
	{
	}


	public function __get(string $uid): ?Tree
	{
		$cacheUid = $this->mutation->getRealRootId() . '-' . $uid;
		if (!isset($this->cache[$cacheUid])) {
			if ($uid === 'title') {
				$ret = $this->orm->tree->getById($this->mutation->getRealRootId());
			} else {
				$cond = [
				'rootId' => $this->mutation->getRealRootId(),
						'uid' => $uid,
				];
				$cond = array_merge($cond, $this->orm->tree->getPublicOnlyWhere());
				$ret = $this->orm->tree->getBy($cond);
			}

			$this->cache[$cacheUid] = $ret;
		}

		return $this->cache[$cacheUid];
	}


	public function __isset(string $name): bool
	{
		if ($this->uidList === null) {
			$this->uidList = $this->orm->tree->findBy([
				'uid!=' => '',
				'rootId' => $this->mutation->getRealRootId(),
			])->fetchPairs('uid', 'uid');
		}

		return isset($this->uidList[$name]);
	}

}
