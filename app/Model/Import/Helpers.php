<?php

namespace App\Model\Import;

use Nette\Utils\Strings;

final class Helpers
{
	private static function innerGenerate(array $parts): string
	{
		return hash('md5', implode('|',
			array_map(fn(int|string $part) => is_string($part) ? Strings::webalize($part) : $part, $parts)
		));
	}

	public static function generateProgrammeId(int|string $facultyId, string $programmeName): string
	{
		return self::innerGenerate(['programme', $facultyId, $programmeName]);
	}

	public static function generateBranchId(int|string $facultyId, string $programmeName, string $branchName): string
	{
		return self::innerGenerate(['branch', $facultyId, $programmeName, $branchName]);
	}
}
