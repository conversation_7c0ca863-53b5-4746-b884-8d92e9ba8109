<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\All\Consumer;

use App\Model\ElasticSearch\All\ConvertorProvider;
use App\Model\ElasticSearch\All\ElasticAll;
use App\Model\ElasticSearch\Service;
use App\Model\Messenger\Elasticsearch\All\Message\ReplaceAllMessage;
use App\Model\Messenger\Elasticsearch\ConsumerHelper;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\ProductRepository;
use App\PostType\Benefit\Model\Orm\BenefitLocalizationRepository;
use App\PostType\BenefitTag\Model\Orm\BenefitTag\BenefitTagLocalizationRepository;
use App\PostType\Calendar\Model\Orm\CalendarLocalizationRepository;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalizationRepository;
use App\PostType\Education\Model\Orm\EducationLocalizationRepository;
use App\PostType\EducationTag\Model\Orm\EducationTag\EducationTagLocalizationRepository;
use App\PostType\Faculty\Model\Orm\FacultyLocalizationRepository;
use App\PostType\Offer\Model\Orm\OfferLocalizationRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\Profile\Model\Orm\ProfileLocalizationRepository;
use App\PostType\ProfileTag\Model\Orm\ProfileTag\ProfileTagLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use App\PostType\Story\Model\Orm\StoryLocalizationRepository;
use App\PostType\StoryTag\Model\Orm\StoryTag\StoryTagLocalizationRepository;
use LogicException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Throwable;

#[AsMessageHandler]
class ReplaceAllConsumer extends Consumer
{

	public function __construct(
		private readonly EsIndexRepository $esIndexRepository,
		private readonly Service $elasticService,
		private readonly ConvertorProvider $convertorProvider,
		private readonly ConsumerHelper $consumerHelper,
		TreeRepository $treeRepository,
		BlogTagLocalizationRepository $blogTagLocalizationRepository,
		BlogLocalizationRepository $blogLocalizationRepository,
		ProductRepository $productRepository,
		private readonly MutationHolder $mutationHolder,
		private readonly Orm $orm,
		SeoLinkLocalizationRepository $seoLinkLocalizationRepository,
		ProfileLocalizationRepository $profileLocalizationRepository,
		ProfileTagLocalizationRepository $profileTagLocalizationRepository,
		CalendarLocalizationRepository $calendarLocalizationRepository,
		CalendarTagLocalizationRepository $calendarTagLocalizationRepository,
		BenefitLocalizationRepository $benefitLocalizationRepository,
		BenefitTagLocalizationRepository $benefitTagLocalizationRepository,
		FacultyLocalizationRepository $facultyLocalizationRepository,
		StoryLocalizationRepository $storyLocalizationRepository,
		StoryTagLocalizationRepository $storyTagLocalizationRepository,
		EducationLocalizationRepository $educationLocalizationRepository,
		EducationTagLocalizationRepository $educationTagLocalizationRepository,
		OfferLocalizationRepository $offerLocalizationRepository,
	)
	{
		parent::__construct(
			treeRepository: $treeRepository,
			blogTagLocalizationRepository: $blogTagLocalizationRepository,
			blogLocalizationRepository: $blogLocalizationRepository,
			productRepository: $productRepository,
			seoLinkLocalizationRepository: $seoLinkLocalizationRepository,
			profileLocalizationRepository: $profileLocalizationRepository,
			profileTagLocalizationRepository: $profileTagLocalizationRepository,
			calendarLocalizationRepository: $calendarLocalizationRepository,
			calendarTagLocalizationRepository: $calendarTagLocalizationRepository,
			benefitLocalizationRepository: $benefitLocalizationRepository,
			benefitTagLocalizationRepository: $benefitTagLocalizationRepository,
			facultyLocalizationRepository: $facultyLocalizationRepository,
			storyLocalizationRepository: $storyLocalizationRepository,
			storyTagLocalizationRepository: $storyTagLocalizationRepository,
			educationLocalizationRepository: $educationLocalizationRepository,
			educationTagLocalizationRepository: $educationTagLocalizationRepository,
			offerLocalizationRepository: $offerLocalizationRepository,
		);
	}

	public function __invoke(ReplaceAllMessage $message): void
	{
		$esIndex = $this->esIndexRepository->getById($message->getEsIndexId());

		if ($esIndex === null) {
			throw new LogicException(sprintf('EsIndex %s not found', $message->getEsIndexId()));
		}

		try {
			$this->orm->setMutation($esIndex->mutation);
			$this->mutationHolder->setMutation($esIndex->mutation);

			$object = $this->getObjectByClass($message->getClass(), $message->getId());
			$convertors = array_map(function ($convertorClass) {
				return $this->convertorProvider->get($convertorClass);
			}, $message->getConvertors());

			$this->elasticService->replaceDoc($esIndex, new ElasticAll($object, $convertors));

			if (($signals = $message->getSignals()) !== []) {
				$this->consumerHelper->handleSignals($esIndex, $signals);
			}
		} catch (Throwable $e) {
			$this->consumerHelper->handleError($esIndex, $e, $message->getId(), $message->getClass());
		}
	}

}
