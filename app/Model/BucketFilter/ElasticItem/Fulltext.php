<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\QueryBuilder;

class Fulltext implements ElasticItem, QuestionableElasticItem
{

	public function __construct(
		private readonly string $fulltext,
	)
	{
	}

	public function getCondition(): AbstractQuery
	{
		$fields = [];
		$fields[] = 'name.dictionary^80';
		$fields[] = 'fulltext-name.customEdgeNgram^3';
		$fields[] = 'annotation.dictionary^40';
		$fields[] = 'content.dictionary^40';

		$b = new QueryBuilder();

		return $b->query()->multi_match()
			->setType('best_fields') //phrase_prefix phrase, cross_fields best_fields
			->setQuery($this->fulltext)
			->setFuzziness(2)
			->setOperator('OR')
			->setFields($fields);
	}

	public function getElasticKey(): string
	{
		return '';
	}

}
