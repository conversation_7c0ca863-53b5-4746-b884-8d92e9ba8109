<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common\Convertor;

use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\Model\ElasticSearch\Common\Convertor;
use App\Model\ElasticSearch\ConvertorHelper;
use App\PostType\Story\Model\Orm\StoryLocalization;

class StoryData implements Convertor
{

	public const TYPE = 'story';

	public function convert(object $object): array
	{
		assert($object instanceof StoryLocalization);
		$parent = $object->getParent();

		$data = [
			'id' => $object->id,
			'public' => (bool) $object->public,
			'isPublic' => (bool) $object->public,
			'name' => $object->name,
			'fulltext-name' => $object->name,
			'nameTitle' => $object->nameTitle,
			'nameAnchor' => $object->nameAnchor,
			'content' => $object->getEsContent(),
			'type' => self::TYPE,
			'annotation' => $object->annotation,
			'publicFrom' => $object->publicFrom !== null ? ConvertorHelper::convertTime($object->publicFrom) : null,
			'publicTo' => $object->publicTo !== null ? ConvertorHelper::convertTime($object->publicTo) : null,
			'sort' => $object->sort,
		];

		$data['tags'] = [];
		if ($parent->tags->count()) {
			foreach ($object->getParent()->tags as $tag) {
				if ($tag->isPublished()) {
					$data['tags'][] = $tag->id;
				}
			}
		}

		$data['faculties'] = [];
		if ($parent->faculties->count()) {
			foreach ($object->getParent()->faculties as $faculty) {
				if ($faculty->isPublished()) {
					$data['faculties'][] = $faculty->id;
				}
			}
		}

		return $data;
	}

}
