<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common\Convertor;

use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\Model\ElasticSearch\Common\Convertor;
use App\Model\ElasticSearch\ConvertorHelper;
use App\PostType\Education\Model\Orm\EducationLocalization;
use App\PostType\Offer\Model\Orm\OfferLocalization;
use App\PostType\Story\Model\Orm\StoryLocalization;

class OfferData implements Convertor
{

	public const TYPE = 'offer';

	public function convert(object $object): array
	{
		assert($object instanceof OfferLocalization);
		$parent = $object->getParent();

		$data = [
			'id' => $object->id,
			'public' => (bool) $object->public,
			'isPublic' => (bool) $object->public,
			'name' => $object->name,
			'fulltext-name' => $object->name,
			'faculty' => $parent->faculty !== null && $parent->faculty->isPublished() ? $parent->faculty->id : null,
			'nameTitle' => $object->nameTitle,
			'nameAnchor' => $object->nameAnchor,
			'content' => $object->getEsContent(),
			'type' => self::TYPE,
			'annotation' => $object->annotation,
			'publicFrom' => $object->publicFrom !== null ? ConvertorHelper::convertTime($object->publicFrom) : null,
			'publicTo' => $object->publicTo !== null ? ConvertorHelper::convertTime($object->publicTo) : null,
			'sort' => $object->sort,
		];



		return $data;
	}

}
