<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common;

use App\Model\ElasticSearch\Entity;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Benefit\Model\Orm\BenefitLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\Education\Model\Orm\EducationLocalization;
use App\PostType\Offer\Model\Orm\OfferLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\PostType\Story\Model\Orm\StoryLocalization;
use LogicException;

/**
 * @param Convertor[] $convertors
 */
class ElasticCommon implements Entity
{

	public const TYPE_TREE = 'tree';
	public const TYPE_BLOG = 'blog';
	public const TYPE_CALENDAR = 'calendar';
	public const TYPE_BENEFIT = 'benefit';
	public const TYPE_PROFILE = 'profile';
	public const TYPE_STORY = 'story';
	public const TYPE_EDUCATION = 'education';
	public const TYPE_OFFER = 'offer';

	public function __construct(
		private object $object,
		private array $convertors = [],
	)
	{
	}

	public function getId(): string
	{
		$class = get_class($this->object);
		if (isset($this->object->id)) {
			return match ($class) {
				ProfileLocalization::class => self::TYPE_PROFILE . '-' . $this->object->id,
				BlogLocalization::class => self::TYPE_BLOG . '-' . $this->object->id,
				CalendarLocalization::class => self::TYPE_CALENDAR . '-' . $this->object->id,
				BenefitLocalization::class => self::TYPE_BENEFIT . '-' . $this->object->id,
				StoryLocalization::class => self::TYPE_STORY . '-' . $this->object->id,
				EducationLocalization::class => self::TYPE_EDUCATION . '-' . $this->object->id,
				OfferLocalization::class => self::TYPE_OFFER . '-' . $this->object->id,
				Tree::class => self::TYPE_TREE . '-' . $this->object->id,
				CatalogTree::class, CommonTree::class => self::TYPE_TREE . '-' . $this->object->id,
				default => throw new LogicException(sprintf("Missing common definition for '%s' class", $class))
			};

		} else {
			throw new LogicException('Missing primary key for entity');
		}
	}

	public function getData(Mutation $mutation): array
	{
		$convertedData = [];
		foreach ($this->convertors as $convertor) {
			$convertedData[] = $convertor->convert($this->object);
		}

		return array_merge(...$convertedData);
	}

}
