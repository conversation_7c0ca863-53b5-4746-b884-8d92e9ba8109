<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\PostType\ProfileTag\Model\Orm\ProfileTag\ProfileTagLocalization;
use App\Model\ElasticSearch\All\Convertor;

class ProfileTagData implements Convertor
{

	public function convert(object $object): array
	{
		assert($object instanceof ProfileTagLocalization);
		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'profileTag',
			'langCode' => $object->mutation->langCode,
			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'nameTitle' => $object->nameTitle,
			'description' => $object->description,
			'annotation' => '',
		];
		$ret['kind'] = 'profileTag';

		return $ret;
	}

}
