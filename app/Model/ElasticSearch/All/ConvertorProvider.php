<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All;

use App\Model\ElasticSearch\All\Convertor\BenefitData;
use App\Model\ElasticSearch\All\Convertor\BenefitTagData;
use App\Model\ElasticSearch\All\Convertor\BlogData;
use App\Model\ElasticSearch\All\Convertor\BlogTagData;
use App\Model\ElasticSearch\All\Convertor\BranchData;
use App\Model\ElasticSearch\All\Convertor\CalendarData;
use App\Model\ElasticSearch\All\Convertor\CalendarTagData;
use App\Model\ElasticSearch\All\Convertor\EducationData;
use App\Model\ElasticSearch\All\Convertor\EducationTagData;
use App\Model\ElasticSearch\All\Convertor\FacultyData;
use App\Model\ElasticSearch\All\Convertor\NewsletterData;
use App\Model\ElasticSearch\All\Convertor\OfferData;
use App\Model\ElasticSearch\All\Convertor\ProductData;
use App\Model\ElasticSearch\All\Convertor\ProfileData;
use App\Model\ElasticSearch\All\Convertor\ProfileTagData;
use App\Model\ElasticSearch\All\Convertor\ProgrammeData;
use App\Model\ElasticSearch\All\Convertor\SeoLinkData;
use App\Model\ElasticSearch\All\Convertor\StoryData;
use App\Model\ElasticSearch\All\Convertor\StoryTagData;
use App\Model\ElasticSearch\All\Convertor\TreeData;
use App\Model\Orm\Product\Product;
use App\PostType\Benefit\Model\Orm\Benefit;
use App\PostType\Benefit\Model\Orm\BenefitLocalization;
use App\PostType\BenefitTag\Model\Orm\BenefitTag\BenefitTagLocalization;
use App\PostType\Branch\Model\Orm\BranchLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;
use App\PostType\Education\Model\Orm\EducationLocalization;
use App\PostType\EducationTag\Model\Orm\EducationTag\EducationTagLocalization;
use App\PostType\Faculty\Model\Orm\FacultyLocalization;
use App\PostType\Newsletter\Model\Orm\NewsletterLocalization;
use App\PostType\Offer\Model\Orm\OfferLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Profile\Model\Orm\ProfileLocalization;
use App\PostType\ProfileTag\Model\Orm\ProfileTag\ProfileTagLocalization;
use App\PostType\Programme\Model\Orm\ProgrammeLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\Story\Model\Orm\StoryLocalization;
use App\PostType\StoryTag\Model\Orm\StoryTag\StoryTagLocalization;
use LogicException;

class ConvertorProvider
{

	private array $map;

	public function __construct(
		private readonly TreeData $treeData,
		private readonly BlogData $blogData,
		private readonly CalendarData $calendarData,
		private readonly FacultyData $facultyData,
		private readonly ProgrammeData $programmeData,
		private readonly BranchData $branchData,
		private readonly BlogTagData $blogTagData,
		private readonly CalendarTagData $calendarTagData,
		private readonly ProductData $productData,
		private readonly SeoLinkData $seoLinkData,
		private readonly ProfileData $profileData,
		private readonly ProfileTagData $profileTagData,
		private readonly BenefitData $benefitData,
		private readonly BenefitTagData $benefitTagData,
		private readonly StoryData $storyData,
		private readonly StoryTagData $storyTagData,
		private readonly EducationData $educationData,
		private readonly EducationTagData $educationTagData,
		private readonly OfferData $offerData,
		private readonly NewsletterData $newsletterData,
	)
	{
		$this->map = [];
		foreach (func_get_args() as $convertor) {
			$this->map[$convertor::class] = $convertor;
		}
	}


	public function get(string $class): Convertor
	{
		return $this->map[$class] ?? throw new LogicException(sprintf("Missing convertor for '%s' class", $class));
	}


	public function getAll(string $class): array
	{
		return match ($class) {
			FacultyLocalization::class => [
				$this->facultyData,
			],
			ProgrammeLocalization::class => [
				$this->programmeData,
			],
			BranchLocalization::class => [
				$this->branchData,
			],
			BlogLocalization::class => [
				$this->blogData,
			],
			BlogTagLocalization::class => [
				$this->blogTagData,
			],
			CalendarLocalization::class => [
				$this->calendarData,
			],
			CalendarTagLocalization::class => [
				$this->calendarTagData,
			],
			Product::class => [
				$this->productData,
			],
			SeoLinkLocalization::class => [
				$this->seoLinkData,
			],
			Tree::class, CatalogTree::class, CommonTree::class => [
				$this->treeData,
			],
			ProfileLocalization::class => [
				$this->profileData,
			],
			ProfileTagLocalization::class => [
				$this->profileTagData,
			],
			BenefitLocalization::class => [
				$this->benefitData,
			],
			BenefitTagLocalization::class => [
				$this->benefitTagData,
			],
			StoryLocalization::class => [
				$this->storyData,
			],
			StoryTagLocalization::class => [
				$this->storyTagData,
			],
			EducationLocalization::class => [
				$this->educationData,
			],
			EducationTagLocalization::class => [
				$this->educationTagData,
			],
			OfferLocalization::class => [
				$this->offerData,
			],
			NewsletterLocalization::class => [
				$this->newsletterData,
			],
			default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
		};
	}

	public function getAllLikeStrings(string $class): array
	{
		return array_map(fn(object $item) => $item::class, $this->getAll($class));
	}

}
