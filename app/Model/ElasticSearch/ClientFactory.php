<?php

namespace App\Model\ElasticSearch;

use Elasticsearch\Client;
use Elasticsearch\ClientBuilder;

final class ClientFactory
{
	public function __construct(
		private readonly string $host = 'localhost',
		private readonly int $port = 9200,
	)
	{

	}

	public function create(): Client
	{
		error_reporting(E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED);
		$clientBuilder = ClientBuilder::create();
		$clientBuilder->setHosts([$this->host . ':' . $this->port]);

		$client = $clientBuilder->build();
//		error_reporting(E_ALL);
		return $client;
	}
}
