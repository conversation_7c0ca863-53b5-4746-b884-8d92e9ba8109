<!DOCTYPE html>
<html lang="cs" class="no-js">
	<head>
		<meta charset="utf-8" />
		<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
		<meta name="author" content="HTML by SuperKodéři (<EMAIL>)" />
		<meta name="keywords" content="" />
		<meta name="description" content="" />
		<!-- <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no"> -->

		<title>{$title}</title>
		<link rel="stylesheet" href="/admin/new/dist/css/style.css?v={$webVersion}" />

		<link rel="shortcut icon" href="/admin/favicon.ico?v={$webVersion}" />

		<script>
			(function () {
				var className = document.documentElement.className;
				className = className.replace('no-js', 'js');

				(function() {
					var mediaHover = window.matchMedia('(hover: none), (pointer: coarse), (pointer: none)');
					mediaHover.addListener(function(media) {
						document.documentElement.classList[media.matches ? 'add' : 'remove']('no-hoverevents');
						document.documentElement.classList[!media.matches ? 'add' : 'remove']('hoverevents');
					});
					className += (mediaHover.matches ? ' no-hoverevents' : ' hoverevents');
				})();

				// var supportsCover = 'CSS' in window && typeof CSS.supports === 'function' && CSS.supports('object-fit: cover');
				// className += (supportsCover ? ' ' : ' no-') + 'objectfit';

				// fix iOS zoom issue: https://docs.google.com/document/d/1KclJmXyuuErcvit-kwCC6K2J7dClRef43oyGVCqWxFE/edit#heading=h.sgbqg5nzhvu9
				var ua = navigator.userAgent.toLowerCase();
				var isIOS = /ipad|iphone|ipod/.test(ua) && !window.MSStream;

				if (isIOS === true) {
					var viewportTag = document.querySelector("meta[name=viewport]");
					viewportTag.setAttribute("content", "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no");
				}

				document.documentElement.className = className;
			}());
		</script>

		{var $scripts = [
			'/admin/new/dist/js/jquery-3.4.1.min.js?t=' . $webVersion,
			'https://cdn.polyfill.io/v3/polyfill.min.js?features=default,Array.prototype.includes,Object.values,Array.prototype.find,AbortController,fetch',
			'/admin/new/dist/js/tinymce/tinymce.min.js?t=' . $webVersion,
			'/admin/new/dist/js/tinymce/jquery.tinymce.min.js?t=' . $webVersion,
			'/admin/new/dist/js/app.js?t=' . $webVersion,
		] }
		{foreach $scripts as $script}
			<link rel="preload" as="script" href="{$script}">
		{/foreach}

		{if isset($dataGridShown)}
			<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous">
		{/if}
	</head>

	<body class="{foreach $mutations as $mutation}lang-{$mutation->langCode}{sep} {/sep}{/foreach}" data-controller="Naja">
		<header class="header">
			<p class="header__logo">
				<img src="/admin/new/dist/img/logo-superadmin-white.png" width="240" height="48" alt="SUPERADMIN" />
			</p>
			{if isset($envName) && $envName}<div class="u-font-md">({$envName}){if $envName == "dev"} [nový]{/if}</div>{/if}
			{if $envName !== 'production'}<span style="color: red; font-weight: bold">&nbsp;jste na testovací verzi webu</span>{/if}
			<div class="header__search">
				{include './part/form/search.latte'}
			</div>
			<div class="header__user">
				{include './part/menu/user.latte'}
			</div>
		</header>

		<main
			id="main"
			class="main{if isset($otherArticle) && $otherArticle} main--lang{/if}"
			data-controller="Templates ModalClose"
			data-action="List:newItem@window->Templates#newItem DeliveryPrice:remove@window->Templates#remove ProductVariant:remove@window->Templates#remove ImageList:newItem@window->Templates#newItem ImageList:remove@window->Templates#remove RemoveItem:remove@window->Templates#remove ImageLibrary:newItem@window->Templates#newItem"
		>
			<div class="main__side scroll scroll--white">
				{include './part/menu/main.latte'}
			</div>
			{block #tree}{/block}
			{include #content}
		</main>

		{foreach $scripts as $script}
			<script src="{$script}"></script>
		{/foreach}
		<script>
			App.run({
				sessionID: {session_id()},
				dataGridAjaxClass: {(isset($dataGridShown)) && $dataGridShown}
			});
		</script>
		<script src="/admin/js/jquery.cookie.js"></script>
		<script src="/admin/js/jquery.nette.js"></script>
		<script src="/admin/js/jquery.jstree.js"></script>
		<script src="/admin/js/tree.js?v={$webVersion}"></script>
	</body>
</html>
