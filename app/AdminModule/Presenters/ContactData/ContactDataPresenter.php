<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\ContactData;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\ContactData\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\ContactData\Components\DataGrid\DataGridFactory;
use App\Model\Orm\Log\Log;
use App\Model\Orm\User\User;
use App\Model\TranslatorDB;
use Nette\Bridges\ApplicationLatte\Template;
use Nette\Utils\Arrays;
use Nette\Utils\Json;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\VarDumper\Cloner\VarCloner;
use Symfony\Component\VarDumper\Dumper\HtmlDumper;

final class ContactDataPresenter extends BasePresenter
{
	private const string PARAMETER_REASONS_UID = 'deactivateReasons';

	private array $componentIdNameMap = [
		'classmateTipForm-form' => 'classmateTip_form',
		'socialsTipForm-form' => 'socialsTip_form',
		'communityTipForm-form' => 'communityTip_form',
		'podcastTipForm-form' => 'podcastTip_form',
		'profileForm-profile-profileForm' => 'profile_form',
		'profileForm-avatar-avatarForm' => 'avatar_form',
		'profileInvites-form' => 'profileInvites_form',
		'userDeactivation-form' => 'userDeactivation_form'
	];

	private array $componentsForAdmin = [
		'classmateTipForm-form',
		'socialsTipForm-form',
		'communityTipForm-form',
		'podcastTipForm-form',
		'userDeactivation-form',
	];

	private ?Log $object = null;

	public function __construct(

		private readonly DataGridFactory $dataGridFactory,
		private readonly TranslatorDB $translatorDB,
	)
	{
		parent::__construct();
	}

	/**
	 * Mapping ID of component from tree to fixed translatable name
	 *
	 * @param string $componentIdMessage
	 * @return string|null
	 */
	public function mapComponentIdToName(string $componentIdMessage): ?string
	{
		foreach ($this->componentIdNameMap as $componentId => $componentName) {
			if (str_contains($componentIdMessage, $componentId)) {
				return $componentName;
			}
		}

		return null;
	}

	public function actionDetail(int $id): void
	{
		$this->object = $this->orm->log->getById($id);

		$this->object->viewedAt = new DateTimeImmutable();
		$this->orm->log->persistAndFlush($this->object);

		if ($this->object === null) {
			$this->redirect('default');
		}

		if (!$this->user->isDeveloper()) {
			if (!in_array($this->object->message, $this->componentsForAdmin, true)) {
				$this->redirect('default');
			}
		}
	}

	public function renderDetail(int $id): void
	{
		/** @var Template $template */
		$template = $this->template;
		$template->object = $this->object;
		$template->developerView = $this->user->isDeveloper();
		$template->componentsForAdmin = $this->componentsForAdmin;

		$template->addFilter('contextFormData', function (Log $log): array {
				$contextDecoded = array_map(
					callback: fn($val) => is_array($val) ? Arrays::flatten($val) : $val,
					array: (array) ($log->contextDecoded->context->formData ?? []),
				);

				return array_filter(
					array: $contextDecoded,
					callback: fn(mixed $val, string $key): bool => !preg_match('/antispam|password|secret/', $key),
					mode: ARRAY_FILTER_USE_BOTH,
				);
			}
		);

		$template->addFilter('componentName', fn(Log $log): string =>
			strval($this->mapComponentIdToName($log->message))
		);

		$template->addFilter('dump', function ($value): string {
			$dumper = new HtmlDumper();
			$dumper->setTheme('light');
			$dumper->setStyles(['default' => '']);

			ob_start();
			$dumper->dump((new VarCloner())->cloneVar($value));
			return strval(ob_get_clean());
		});

		$template->addFilter('translateAsFront', fn(string $msg): string => $this->translatorDB->translate($msg));

		$template->addFilter('userById', fn(int $userId): ?User => $this->orm->user->getById($userId));

		$template->addFilter('translateReason', function (string $reason): ?string {
			$parameter = $this->orm->parameter->findBy(['uid' => self::PARAMETER_REASONS_UID])->fetch();
			return $parameter?->optionsValuePairs[$reason] ?? null;
		});
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(
			mapComponentIdToName: $this->mapComponentIdToName(...),
			// Normal administrators have limited view of listed component logs...
			onlyForComponents: $this->user->isDeveloper() ? null : $this->componentsForAdmin,
		);
	}

}
