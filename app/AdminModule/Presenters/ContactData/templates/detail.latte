{layout $templates . '/@layout-new.latte'}

{varType App\Model\Orm\Log\Log $object}

{block #content}

<div class="main__main main__main--noside">
	<div class="main__header">
		{include $templates . '/part/box/header.latte',
			props: [
				title: $translator->translate('FormLog'),
				isPageTitle: true,
				hrefClose: "default"
			]
		}
	</div>

	<div class="main__content scroll">
		{var $bgColorAdmin = '#3355DD'}
		{var $bgColorDeveloper = '#dd4b39'}

		<h2>{$object|componentName|translate}</h2>
		<p>Vytvořeno <strong>{$object->createdAt|date:'d.m.Y H:i:s'}</strong></p>

		{if in_array($object->message, $componentsForAdmin, true)}
			<h3>{_'message_log_for_admins'}</h3>
			<div class="table-wrap">
				<table class="table table-hover table-striped table-bordered table-sm">
					<thead>
					<tr>
						<th style="width: 20%; background-color: {$bgColorAdmin|noescape}; color:#fff;"><PERSON><PERSON><PERSON><PERSON></th>
						<th style="width: 70%; background-color: {$bgColorAdmin|noescape}; color:#fff;">Hodnota</th>
					</tr>
					</thead>
					<tbody>
					<tr n:foreach="($object|contextFormData) as $key => $value">
						<td><strong>{$key|translate}</strong></td>
						<td>
							{if is_array($value)}
								<table>
									<tr n:foreach="$value as $valueItem">
										<td>
											{if ($object|componentName) === 'userDeactivation_form' && $key === 'reasons'}
												{$valueItem|translateReason}
											{else}
												{$valueItem}
											{/if}
										</td>
									</tr>
								</table>
							{else}
								{$value}
								{if $key === 'userId'}
									{var $userInLog = (intval($value)|userById)}
									{if $userInLog}
										| <a n:href=":Admin:User:edit $userInLog->id">
											{$userInLog->email}
										</a>
									{/if}
								{/if}
							{/if}
						</td>
					</tr>
					</tbody>
				</table>
			</div>
		{/if}

		{if $developerView}
			<h3>{_'message_log_for_developers'}</h3>
			<div class="table-wrap">
				<table class="table table-hover table-striped table-bordered table-sm">
					<thead>
					<tr>
						<th style="width: 20%; background-color: {$bgColorDeveloper|noescape}; color:#fff;">Klíč</th>
						<th style="width: 70%; background-color: {$bgColorDeveloper|noescape}; color:#fff;">Hodnota</th>
					</tr>
					</thead>
					<tbody>
					<tr n:foreach="$object->contextDecoded as $key => $value">
						<td><strong>{$key}</strong></td>
						<td>{$value|dump|noescape}</td>
					</tr>
					</tbody>
				</table>
			</div>
		{/if}
	</div>
</div>

