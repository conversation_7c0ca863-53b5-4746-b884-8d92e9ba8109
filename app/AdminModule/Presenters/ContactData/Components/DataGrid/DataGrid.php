<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\ContactData\Components\DataGrid;

use App\Model\Orm\Log\Log;
use App\Model\Orm\Log\LogRepository;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Security\User;
use App\Model\Translator;
use Nette\Application\Responses\FileResponse;
use Nette\Application\UI\Control;
use Nette\Utils\Json;
use Nextras\Orm\Collection\ICollection;
use Tracy\Dumper;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{
	/** @var callable(string $componentId): ?string */
	private $mapComponentIdToName;

	/**
	 * @param callable(string $componentId): ?string $mapComponentIdToName
	 * @param array|null $onlyForComponents
	 * @param LogRepository $logRepository
	 * @param Translator $translator
	 */
	public function __construct(
		callable $mapComponentIdToName,
		private readonly ?array $onlyForComponents,
//		private readonly string $fileLogDirectoryPath,
		private readonly LogRepository $logRepository,
//		private readonly MutationRepository $mutationRepository,
		private readonly Translator $translator,
	)
	{
		$this->mapComponentIdToName = $mapComponentIdToName;
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$source = $this->logRepository->findForDataGrid($this->onlyForComponents !== null ? $this->onlyForComponents : null);
		$grid->setDefaultPerPage(40);
		$grid->setDataSource($source);

		$grid->addColumnText('message', 'message_from_component')
			->setFilterText();
		$grid->addColumnText('messageType', 'message_from_title')->setRenderer(
			fn(Log $log): string => $this->translator->translate(strval(($this->mapComponentIdToName)($log->message)))
		);
		$grid->addColumnDateTime('createdAt', 'message_createdAt')->setRenderer(
			fn(Log $log): string => $log->createdAt->format('j.n.Y H:i:s')
		)->setSortable()->setFilterDateRange();
		$grid->addColumnDateTime('viewedAt', 'message_viewedAt')->setRenderer(
			fn(Log $log): ?string => $log->viewedAt?->format('j.n.Y H:i:s')
		)->setSortable()->setFilterDateRange();

		$grid->addAction('Detail', 'Detail', ':detail')->setClass('btn btn-xs btn-primary');
		$grid->setDefaultSort(['createdAt' => 'DESC']);
		$grid->setTranslator($this->translator);

		/*
	$grid->addColumnDateTime('files', 'files')->setRenderer(
		function (Log $log): string {
			$links = [];
			$data = Json::decode($log->context);
			if (isset($data->context->formData->files)) {
				foreach ($data->context->formData->files as $file) {
					if (file_exists($file->backup)) {
						$pathinfo = pathinfo($file->backup);
						$links[] = '<a target="_blank" href="' . $this->link('download', ['fileName' => $pathinfo['basename']]) . '">' . $pathinfo['basename'] . '</a>';
					}
				}
			}
			return implode('<br>', $links);
		}
	)->setTemplateEscaping(false);*/

		return $grid;
	}


	/*public function handleDownload(string $fileName): void
	{
		$pathToFile = $this->fileLogDirectoryPath . '/' . $fileName;
		$this->presenter->sendResponse(new FileResponse($pathToFile));
	}*/

}
