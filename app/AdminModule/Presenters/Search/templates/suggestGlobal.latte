{if isset($esResult)}
	<ul class="b-suggest__list reset">
		{var $kindToLinkMap = [
			blog => ':Blog:Admin:Blog:edit',
			blogTag => ':BlogTag:Admin:BlogTag:edit',
			benefit => ':Benefit:Admin:Benefit:edit',
			benefitTag => ':BenefitTag:Admin:BenefitTag:edit',
			calendar => ':Calendar:Admin:Calendar:edit',
			calendarTag => ':CalendarTag:Admin:CalendarTag:edit',
			education => ':Education:Admin:Education:edit',
			educationTag => ':EducationTag:Admin:EducationTag:edit',
			story => ':Story:Admin:Story:edit',
			storyTag => ':StoryTag:Admin:StoryTag:edit',
			faculty => ':Faculty:Admin:Faculty:edit',
			offer => ':Offer:Admin:Offer:edit',
			profile => ':Profile:Admin:Profile:edit',
			tree => ':Page:Admin:Page:default',
			seoLink => ':Admin:SeoLink:edit',
		]}

		{foreach $esResult->getResults() as $i}
			{varType Elastica\Result $i}
			<li class="b-suggest__item item" data-id="{$i->id}" n:ifset="$kindToLinkMap[$i->kind]">
				<a class="b-suggest__link" n:href="$kindToLinkMap[$i->kind] $i->id">{$i->kind|firstUpper|translate} | {$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{/foreach}
	</ul>
{/if}
