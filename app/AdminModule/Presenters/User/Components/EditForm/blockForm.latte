{varType App\Model\Orm\User\User $object}

{snippet flash}
	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
{/snippet}

{snippet editForm}
	{form blockForm  autocomplete => 'off', novalidate=>'novalidate'}
		<h1>{$object->name}</h1>
		<h3>{$object->email} {if $object->isBlocked()} | <strong>{_'blocked'} {$object->blockedTime|date:'d.m.Y H:i'}</strong>{/if}</h3>
		<p n:if="$object->blockedReason">{$object->blockedReason}</p>
		<ul class="message message-error" n:if="$form->hasErrors()">
			{foreach $form->errors as $key=>$error}
				{breakIf $key > 0}
				<li>{_$error}</li>
			{/foreach}
		</ul>
		<div class="grid-row">
			<p class="grid-1-4" n:if="isset($form['blockReason'])">
				{label blockReason /}<br>
				<span class="inp-fix">
					<textarea n:name="blockReason" class="inp-text w-full{if $form['blockReason']->hasErrors()} error{/if}">
					</textarea>
				</span>
				<br>
			</p>
		</div>

		<button n:if="!$object->isBlocked()" class="btn btn-red btn-icon-before">
			<span><span class="icon icon-blocked"></span> {_block_button}</span>
		</button>
		<button n:if="$object->isBlocked()" class="btn btn-primary btn-icon-before">
			<span><span class="icon icon-unlocked"></span> {_unblock_button}</span>
		</button>
	{/form}
{/snippet}
