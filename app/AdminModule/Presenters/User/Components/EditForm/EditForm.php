<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\User\Components\EditForm;

use App\Model\Email\CommonFactory;
use App\Model\Image\ImageObjectFactory;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserHash\UserHashModel;
use App\PostType\Profile\Model\Orm\ProfileRepository;
use Nette\Application\AbortException;
use Nette\Application\LinkGenerator;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use App\Model\Translator;
use Nextras\Dbal\Drivers\Exception\UniqueConstraintViolationException;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Throwable;

/**
 * @property-read DefaultTemplate $template
 */
final class EditForm extends UI\Control
{

	private bool $isEdit;

	public function __construct(
		private User $user,
		private array $mutations,
		private readonly User $userEntity,
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly UserModel $userModel,
		private readonly ProfileRepository $profileRepository,
		private readonly ImageObjectFactory $imageObjectFactory,
		private readonly CommonFactory $commonEmailFactory,
		private readonly LinkFactory $linkFactory,
		private readonly UserHashModel $userHashModel,
		private readonly MutationsHolder $mutationsHolder,
	)
	{
		$this->isEdit = isset($this->user->id);
	}


	public function render(): void
	{
		$this->template->object = $this->user;
		$this->template->fileUploadLink = $this->presenter->link('File:upload');
		$this->template->userEntity = $this->userEntity;
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->template->isEdit = $this->isEdit;
		$this->template->states = $this->user->mutation->states->toCollection()->fetchPairs('id', 'name');
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/editForm.latte');
	}

	public function renderBlock(): void
	{
		$this->template->object = $this->user;
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/blockForm.latte');
	}


	protected function createComponentEditForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addEmail('email', 'email')->setRequired('email_required');
		if (!$this->user->active) {
			$form['email']->setDisabled();
		}

		$form->addText('firstname', 'firstname');
		$form->addText('lastname', 'lastname');
		$form->addPassword('password', 'password');
		$form->addPassword('passwordVerify', 'password_verify');
		$form->addSelect('profile', 'profile', $this->profileRepository->findAll()->fetchPairs('id', 'internalName'))
			->setPrompt('---');

		$form['password']
			->setRequired(false)
			->addRule(function ($field, $form) {
				return $form['passwordVerify']->value === $field->value;
			}, 'msg_password_not_same', $form);

		$form['passwordVerify']
			->setRequired(false)
			->addRule(function ($field, $form) {
				return $form['password']->value === $field->value;
			}, 'msg_password_not_same', $form);

		if (!$this->isEdit) {
			$form['password']->setRequired('set_password');
		}

		$states = $this->isEdit ? $this->user->mutation->states->toCollection()->fetchPairs('id', 'name') : $this->orm->state->findAll()->fetchPairs('id', 'name');

		$user = $this->presenter->user;
		assert($user instanceof \App\Model\Security\User);
		$form->addSelect('role', 'role', $user->getAllowedRoles());

		/*$form->addText('firstname', 'user_firstname');
		$form->addText('lastname', 'user_lastname');
		$form->addText('phone', 'phone');
		$form->addText('street', 'street');
		$form->addText('city', 'city');
		$form->addText('zip', 'zip');
		$form->addSelect('state', 'state', $states);//->setPrompt('select_prompt_states');
		$form->addText('ic', 'company_id');
		$form->addText('dic', 'vat_number');
		$form->addText('company', 'company');*/

		if ($this->isEdit) {
			$values = [];
			$form->addHidden('userMutations', $this->user->mutation->id);
			$values['email'] = $this->user->email;
			$values['role'] = $this->user->role;

			$values['firstname'] = $this->user->firstname;
			$values['lastname'] = $this->user->lastname;
			/*$values['phone'] = $this->user->phone;
			$values['street'] = $this->user->street;
			$values['city'] = $this->user->city;
			$values['zip'] = $this->user->zip;
			$values['state'] = $this->user->state;
			$values['ic'] = $this->user->ic;
			$values['dic'] = $this->user->dic;
			$values['company'] = $this->user->company;*/

			$values['userMutations'] = $this->user->mutation ? $this->user->mutation->id : null; // verze s 1 mutaci per User
			$values['state'] = $this->user->state && isset($states[$this->user->state->id]) ? $this->user->state->id : null;
			$values['profile'] = $this->user->profile?->id;

			$form->setDefaults($values);
		} else {
			$form->addSelect('userMutations', 'label_mutation', $this->mutations);
		}

		$form->addSubmit('save', 'Save');

		$form->onError[] = [$this, 'editFormError'];
		$form->onValidate[] = [$this, 'editFormValidate'];
		$form->onSuccess[] = [$this, 'editFormSucceeded'];

		return $form;
	}


	public function editFormError(UI\Form $form): void
	{
		$this->redrawControl();
	}

	public function editFormValidate(UI\Form $form, ArrayHash $values): void
	{
		if ($this->user->active) {
			if (!$this->isEdit || $this->user->email !== $values->email) { // pri add nebo zmene emailu
				$mutation = $this->orm->mutation->getById($values['userMutations']);
				$user = $this->orm->user->getByEmail($values->email, $mutation);

				if ($user) {
					$form['email']->addError('mail_exist');
					$form->addError('mail_exist');
				}
			}
		}

		if (!$this->isEdit && $values['state']) { // stat musi patrit zvolene mutaci
			$mutation = $this->orm->mutation->getById($values['userMutations']);

			if (!$mutation->states->toCollection()->getById($values['state'])) {
				$form['state']->addError('state_msg_error_unknown_for_mutation');
				$form->addError('state_msg_error_unknown_for_mutation');
			}
		}
	}

	public function editFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();
		try {
			if (isset($this->user->id)) {
				$action = 'edit';
			} else {
				$action = 'create';
			}

			if (empty($valuesAll['q'])) {
				$valuesAll['profile'] = null;
			}

//			$valuesAll['showPriceBrutto'] = $this->user->showPriceBrutto;
//			$valuesAll['showPriceMy'] = $this->user->showPriceMy;
//			$valuesAll['isBlock'] = $this->user->isBlock;

			$this->userModel->save($this->user, $valuesAll, $this->presenter->getUser()->getId(), $action);
//				if ($this->presenter->getUser()->getId() === $this->user->id) {
//					//edituji svuj profil
//					foreach ($valuesAll as $attribute => $value) {
//						$this->presenter->getUser()->getIdentity()->$attribute = $value;
//					}
//				}
			$this->flashMessage('OK', 'ok');

			if ($action === 'edit') {
				if ( ! $this->presenter->isAjax()) {
					$this->presenter->redirect('this', $this->user->id);
				} else {
					$this->redrawControl();
				}
			} else {
				$this->presenter->redirect('edit', $this->user->id);
			}
		} catch (AbortException $e) {
			throw $e;
		} catch (Throwable $e) {
			if ($e instanceof UniqueConstraintViolationException) {
				$form->addError('msg_error_profile_duplicate');
			} else {
				$form->addError($e->getMessage());
			}
		}
	}

	protected function createComponentBlockForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		if (!$this->user->isBlocked()) {
			$form->addTextArea('blockReason', 'block_reason', 5, 5);
		}
		$form->addSubmit('block', 'block');

		$form->onError[] = [$this, 'blockFormError'];
//		$form->onValidate[] = [$this, 'blockFormValidate'];
		$form->onSuccess[] = [$this, 'blockFormSucceeded'];

		return $form;
	}

	public function blockFormError(UI\Form $form): void
	{
		$this->redrawControl();
	}

	public function blockFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		try {
			if (!$this->user->isBlocked()) {
				$this->user->blockedTime = new DateTimeImmutable('now');
				$this->user->blockedBy = $this->userEntity->id;
				$this->user->blockedReason = $values->blockReason;

				$this->commonEmailFactory->create()->send(
					from: '',
					to: $this->user->email,
					dbTemplate: 'userEmailBlocked',
					data: [
						'REASON' => $values->blockReason ?? '',
					],
				);
			} else {
				$this->user->blockedTime = null;
				$this->user->blockedBy = null;
				$this->user->blockedReason = null;

				$userLoginPage = $this->orm->tree->getByUid('userLogin', $this->orm->getMutation());
				$userLoginLink = $this->linkFactory->linkTranslateToNette($userLoginPage);
				$this->commonEmailFactory->create()->send(
					from: '',
					to: $this->user->email,
					dbTemplate: 'userEmailUnblocked',
					data: [
						'LINK' => $userLoginLink,
					],
				);
			}

			$this->orm->user->persistAndFlush($this->user);

			$this->flashMessage('OK', 'ok');

			$this->presenter->redirect('this', $this->user->id);
		} catch (AbortException $e) {
			throw $e;
		} catch (Throwable $e) {
			$form->addError($e->getMessage());
		}
	}


	public function handleDelete(): never
	{
		$this->userModel->delete($this->user);
		$this->presenter->redirect('default');
	}

	public function handleActivate(): never
	{
		$this->userModel->activate($this->user);
		$this->userModel->update($this->user);
		$this->presenter->redirect('this', $this->user->id);
	}

	public function handleDeactivate(): never
	{
		$this->userModel->deactivate($this->user);
		$this->userModel->update($this->user);
		$this->presenter->redirect('this', $this->user->id);
	}

	public function handleRegenPassword(): never
	{
		$this->flashMessage($this->translator->translate('invoke_password_regenerated'));

		$userHash = $this->userHashModel->generateHashForUser($this->user, UserHash::TYPE_LOST_PASSWORD, [$this->user->email], 1);
		$mutation = $this->mutationsHolder->getDefault();

		$linkTarget = $this->linkFactory->translate($mutation->pages->resetPassword, ['hashToken' => $userHash->hash], $mutation);
		$link = $this->linkFactory->link($mutation, ...$linkTarget);

		$this->commonEmailFactory->create()->send(
			from: '',
			to: $this->user->email,
			dbTemplate: 'regenPassword',
			data: [
				'LINK' => $this->linkFactory->link($mutation, ...$linkTarget),
			],
		);

		$this->presenter->redirect('this', $this->user->id);
	}

}
