{varType App\Model\Orm\User\User $object}

{snippet flash}
	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
{/snippet}

{snippet editForm}
	{form editForm  autocomplete => 'off', novalidate=>'novalidate'}
		<h1>{if $isEdit}{$object->name}{*$object->firstname} {$object->lastname*}{else}{_"Create new user"}{/if}</h1>
		<h3>{$object->email} {if $object->isBlocked()} | <strong>{_'blocked'} {$object->blockedTime|date:'d.m.Y H:i'}</strong>{/if}</h3>
		<h4 n:if="$object->profile !== null">Nav<PERSON><PERSON><PERSON> profil -
			<a href="{plink :Profile:Admin:Profile:edit $object->profile->id}">{$object->profile->internalName}</a>
		</h4>
		<hr>

		<p n:if="$object->blockedReason">{$object->blockedReason}</p>
		<ul class="message message-error" n:if="$form->hasErrors()">
			{foreach $form->errors as $key=>$error}
				{breakIf $key > 0}
				<li>{_$error}</li>
			{/foreach}
		</ul>
		<div class="grid-row">
			<p class="grid-1-4">
				{label firstname /}<br>
				<span class="inp-fix">
					<input n:name="firstname" class="inp-text w-full{if $form['firstname']->hasErrors()} error{/if}">
				</span>
			</p>
			<p class="grid-1-4">
				{label lastname /}<br>
				<span class="inp-fix">
					<input n:name="lastname" class="inp-text w-full{if $form['lastname']->hasErrors()} error{/if}">
				</span>
			</p>
		</div>
		<div class="grid-row">
			<p class="grid-1-4">
				{label email /}<br>
				<span class="inp-fix">
					<input n:name="email" class="inp-text w-full{if $form['email']->hasErrors()} error{/if}">
				</span>
			</p>
			<p class="grid-1-4">
				{label role /}<br>
				<span class="inp-fix inp-fix-select">
					<select n:name="role" class="inp-text w-full{if $form['role']->hasErrors()} error{/if}">
					</select>
				</span>
			</p>
			<p class="grid-1-4">
				{if $isEdit}
					<label>{_'label_mutation'}</label><br>
					<span class="icon icon-globe"></span>
					<span class=""><strong>{$object->mutation->name}</strong></span><br>

				{else}
					{label userMutations /}
					<span class="inp-fix  inp-fix-select">
						{input userMutations class => 'inp-text'}
					</span>
				{/if}
			</p>
		</div>
		<div class="grid-row">
			<p class="grid-1-4 reset">
				{label password /}<br>
				<span class="inp-fix">
					<input n:name="password" autocomplete="new-password" class="inp-text w-full{if $form['password']->hasErrors()} error{/if}">
				</span>
			</p>
			<p class="grid-1-4 reset">
				{label passwordVerify /}<br>
				<span class="inp-fix">
					<input n:name="passwordVerify" class="inp-text w-full{if $form['passwordVerify']->hasErrors()} error{/if}">
				</span>
			</p>
			<p class="grid-1-4 reset" data-type='["profile"]'>
				{label profile /}
				<span class="inp-fix inp-fix-suggest">
					<input type="text" name="q" class="inp-text inp-suggest w-full"
						   data-suggest="{plink Search:profile}" n:attr="value => $object->profile !== null ? $object->profile->internalName" />
					<input n:name="profile" type="hidden" class="suggest-value" n:attr="value => $object->profile !== null ? $object->profile->id" />
				</span>
			</p>
		</div>
		{*<div class="menu-tabs">
			<ul class="reset">
				<li><a href="#tab-personal">Osobní údaje</a></li>
				<li><a href="#tab-addresses">Adresy</a></li>
				<li><a href="#tab-companies">Firemní údaje</a></li>
				<li n:if="$object && $object->isPersisted() && $object->getCfScheme()"><a href="#tab-customfields">{_tab_customfields}</a></li>
			*   <li><a href="#tab-delivery">Dodací údaje</a></li>*
				*<li><a href="#tab-orders">Objednávky</a></li>*
			</ul>
		</div>*}

		{*<div id="tab-personal" class="tab-fragment">
			<div class="grid-row">
				<p class="grid-1-4">
					{label firstname /}<br>
					<span class="inp-fix">
						<input n:name="firstname" class="inp-text w-full{if $form['firstname']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-4">
					{label lastname /}<br>
					<span class="inp-fix">
						<input n:name="lastname" class="inp-text w-full{if $form['lastname']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-4">
					{label phone /}<br>
					<span class="inp-fix">
						<input n:name="phone" class="inp-text w-full{if $form['phone']->hasErrors()} error{/if}">
					</span>
				</p>
			</div>
			<div class="grid-row">
				<p class="grid-1-4">
					{label street /}<br>
					<span class="inp-fix">
						<input n:name="street" class="inp-text w-full{if $form['street']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-4">
					{label city /}<br>
					<span class="inp-fix">
						<input n:name="city" class="inp-text w-full{if $form['city']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-4 reset">
					{label zip /}<br>
					<span class="inp-fix">
						<input n:name="zip" class="inp-text w-full{if $form['zip']->hasErrors()} error{/if}">
					</span>
				</p>

				<p class="grid-1-4 reset">
					{label state /}<br>
					<span class="inp-fix inp-fix-select">
						<select n:name="state" class="inp-text w-full{if $form['state']->hasErrors()} error{/if}">
						</select>
					</span>
				</p>
			</div>
		</div>*}
		{*<div id="tab-addresses" class="tab-fragment" n:if="$isEdit">
			<div class="grid-row" n:if="$object->customAddress">
				<div n:foreach="$object->customAddress as $k => $i" class="grid-1-3">
					<h4>Fakturační</h4>
					<hr>
					{_'user_firstname'}: <strong>{$i->invFirstname ?? null}</strong><br>
					{_'user_lastname'}: <strong>{$i->invLastname ?? null}</strong><br>
					{_'phone'}: <strong>{$i->invPhone ?? null}</strong><br>
					{_'street'}: <strong>{$i->invStreet ?? null}</strong><br>
					{_'city'}: <strong>{$i->invCity ?? null}</strong><br>
					{_'zip'}: <strong>{$i->invZip ?? null}</strong><br>
					{_'state'}: <strong>{isset($i->invState) && isset($states[$i->invState]) ? $states[$i->invState] : ''}</strong><br>
					<br>
					{_'company'}: <strong>{$i->invCompany ?? null}</strong><br>
					{_'company_id'}: <strong>{$i->invIc ?? null}</strong><br>
					{_'vat_number'}: <strong>{$i->invDic ?? null}</strong><br>

					<h4>Dodací</h4>
					<hr>
					{_'user_firstname'}: <strong>{$i->delFirstname ?? null}</strong><br>
					{_'user_lastname'}: <strong>{$i->delLastname ?? null}</strong><br>
					{_'company'}: <strong>{$i->delCompany ?? null}</strong><br>
					{_'phone'}: <strong>{$i->delPhone ?? null}</strong><br>
					{_'street'}: <strong>{$i->delStreet ?? null}</strong><br>
					{_'city'}: <strong>{$i->delCity ?? null}</strong><br>
					{_'zip'}: <strong>{$i->delZip ?? null}</strong><br>
					{_'state'}: <strong>{isset($i->delState) && isset($states[$i->delState]) ? $states[$i->delState] : ''}</strong><br>
				</div>
			</div>
		</div>*}
		{*<div id="tab-companies" class="tab-fragment">
			<div class="message message-info">
				<p>{_msg_info_firm}</p>
			</div>
			<div class="grid-row">
				<p class="grid-1-2 reset">
					{label company /}<br>
					<span class="inp-fix">
						<input n:name="company" class="inp-text w-full{if $form['company']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-2 reset">
					{label ic /}<br>
					<span class="inp-fix">
						<input n:name="ic" class="inp-text w-full{if $form['ic']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-2 reset">
					{label dic /}<br>
					<span class="inp-fix">
						<input n:name="dic" class="inp-text w-full{if $form['dic']->hasErrors()} error{/if}">
					</span>
				</p>
			</div>
		</div>*}

		{* @todo user future vypis dodacich customAddress
		<div id="tab-delivery" class="tab-fragment">
			<div class="message message-info">
				<p>{_msg_info_delivery}</p>
			</div>
			<div class="grid-row">

			</div>
		</div>
		*}




		<div id="tab-customfields" class="tab-fragment" n:if="$object  && $object->isPersisted() && $object->getCfScheme()">
			<div class="c-custom-fields">
				{var $mutation = $object->mutation}
				{var $langCode = $mutation->langCode}

				<div data-controller="CustomFields"
					 data-action="CustomField:updateValue->CustomFields#updateValue CustomFieldImage:updateValue->CustomFields#updateValue CustomFieldFile:updateValue->CustomFields#updateValue CustomFieldList:addListItem->CustomFields#addListItem CustomFieldList:removeListItem->CustomFields#removeListItem CustomFieldList:updateListOrder->CustomFields#updateListOrder"
					 data-customfields-lang-value="{$langCode}"
					 data-customfields-scheme-value="{$object->getCfSchemeJson()}"
					 data-customfields-values-value='{$object->getCfContent()}'
					 data-customfields-uploadurl-value="{$fileUploadLink}"
					 data-customfields-mutationid-value="{$mutation->id}"

				>

					<div data-customfields-target="content"></div>
					<input type="hidden" data-customfields-target="values" name="customFields">
				</div>
			</div>
		</div>

		<div class="fixed-bar">
			<button class="btn btn-green btn-icon-before">
				<span><span class="icon icon-checkmark"></span> {_save_button}</span>
			</button>
			{if isset($object->id) && $object->id != $userEntity->id}
				<a n:href="delete!" class="btn btn-red btn-icon-before btn-delete ajax">
					<span><span class="icon icon-close"></span> {_delete_button}</span>
				</a>
				<a n:if="$object->active" n:href="deactivate!" class="btn btn-red btn-icon-before btn-delete ajax">
					<span><span class="icon icon-remove"></span> {_deactivate_button}</span>
				</a>
				<a n:if="!$object->active" n:href="activate!" class="btn btn-primary btn-icon-before ajax">
					<span><span class="icon icon-undo"></span> {_activate_button}</span>
				</a>
			{/if}
			{if $isEdit && $presenter->isBlockingAllowed($object)}
				<a n:if="!$object->isBlocked()" href="{plink block $object->id}" class="btn btn-red btn-icon-before">
			 		<span><span class="icon icon-blocked"></span> {_block_button}</span>
				</a>
				<a n:if="$object->isBlocked()" href="{plink block $object->id}" class="btn btn-primary btn-icon-before">
					<span><span class="icon icon-unlocked"></span> {_unblock_button}</span>
				</a>
			{/if}
			<a n:href="regenPassword!" class="btn btn-icon-before">
				<span><span class="icon icon-ticket"></span> {_regen_password_button}</span>
			</a>
		</div>
	{/form}
{/snippet}
