<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\User;

use App\AdminModule\Presenters\User\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\User\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\User\Components\EditForm\EditForm;
use App\AdminModule\Presenters\User\Components\EditForm\EditFormFactory;
use App\AdminModule\Presenters\User\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\User\Components\ShellForm\ShellFormFactory;
use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use Nextras\Orm\Collection\ICollection;

/**
 * @property User $object
 */
final class UserPresenter extends BasePresenter
{

	private User $object;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly EditFormFactory $editFormFactory,
		private readonly ShellFormFactory $shellFormFactory,
	)
	{
		parent::__construct();
	}

	private function getUsers(): ICollection
	{
		if ($this->user->isDeveloper()) {
			return $this->orm->user->findBy([]);
		} else {
			return $this->orm->user->findBy([
				'role!=' => User::ROLE_DEVELOPER,
			]);
		}
	}

	public function isBlockingAllowed(User $user): bool
	{
		return ($this->user->isAdmin() || $this->user->isDeveloper()) && !$user->isDeveloper() && !$user->isAdmin();
	}


	public function actionEdit(int $id): void
	{
		/** @var User|null $user */
		$user = $this->getUsers()->getById($id);
		if ($user === null) {
			$this->redirect('default');
		}

		$this->object = $user;
	}

	public function actionBlock(int $id): void
	{
		/** @var User|null $user */
		$user = $this->getUsers()->getById($id);
		if ($user === null || !$this->isBlockingAllowed($user)) {
			$this->redirect('default');
		}

		$this->object = $user;
	}

	public function renderEdit(int $id): void
	{
		$this->template->object = $this->object;
	}

	public function renderBlock(int $id): void
	{
		$this->template->object = $this->object;
	}

	protected function createComponentEditForm(): EditForm
	{
		$mutations = $this->orm->mutation->findAll()->fetchPairs('id', 'name');
		return $this->editFormFactory->create($this->object, $mutations, $this->userEntity);
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create($this->userEntity);
	}


	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

}
