<?php

namespace App\Infrastructure\Ublaboo\Export;

use App\Infrastructure\Ublaboo\DataSource\ElasticsearchDataSource;
use Ublaboo\DataGrid\Row;

/**
 * Exports all pages from grid data source (ignores limit)
 */
class ExportCsvFull extends \Ublaboo\DataGrid\Export\ExportCsv
{
	public function invoke(iterable $data): void
	{
		$grid = clone $this->grid;
		$dataSource = $this->grid->getDataSource();
		assert($dataSource instanceof ElasticsearchDataSource);

		$grid->setDataSource($dataSource->limit(offset: 0, limit: 10000));

		$data = $dataSource->getData();
		foreach ($data as $key => $item) {
			$data[$key] = new Row($grid, $item, 'id');
		}

		parent::invoke($data);
	}
}
