<?php declare(strict_types=1);

namespace App\Infrastructure\Ublaboo\DataGrid;


use App\Infrastructure\Ublaboo\Export\ExportCsvFull;
use Ublaboo\DataGrid\Export\ExportCsv;

class DataGrid extends \Ublaboo\DataGrid\DataGrid
{
	public function addExportCsv(
		string $text,
		string $csvFileName,
		string $outputEncoding = 'utf-8',
		string $delimiter = ';',
		bool $includeBom = false,
		bool $filtered = false,
		bool $full = false,
	): ExportCsv
	{
		$exportClass = $full ? ExportCsvFull::class : ExportCsv::class;
		$exportCsv = new $exportClass($this, $text, $csvFileName, $filtered, $outputEncoding, $delimiter, $includeBom);

		$this->addToExports($exportCsv);

		return $exportCsv;
	}
}
