<?php declare(strict_types = 1);

namespace App\Infrastructure\Latte;

use App\Exceptions\LogicException;
use App\Model\Orm\Mutation\Mutation;
use Brick\Money\Money;
use Latte\ContentType;
use Latte\Runtime\FilterInfo;
use Nette\Application\UI\Form;
use Nette\Application\UI\Link;
use Nette\Localization\Translator;
use Nette\SmartObject;
use Nette\Utils\Html;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Stringable;
use Texy\Texy;
use function is_array;

/**
 * Useful template helpers
 */
final class Filters
{

	use SmartObject;

	public static Mutation $mutation;

	public static mixed $version;

	public static Translator $translator;

	public static function parseVideoId(string $url): ?string
	{
		$urlParsed = parse_url($url);

		if (is_array($urlParsed)) {
			if (isset($urlParsed['host'])) {
				if (in_array($urlParsed['host'], ['www.youtube.com', 'youtube.com'])) {
					if ( ! isset($urlParsed['query'])) {
						return null;
					}

					parse_str($urlParsed['query'], $parsedQuery);
					if ( ! isset($parsedQuery['v'])) {
						return null;
					}

					if (is_array($parsedQuery['v'])) {
						return null;
					}

					return $parsedQuery['v'];
				}

				if ($urlParsed['host'] === 'youtu.be' && isset($urlParsed['path'])) {
					return ltrim($urlParsed['path'], '/');
				}
			}
		}

		return null;
	}

	/**
	 * Czech date formatting
	 */
	public static function skDate(DateTimeImmutable $dateTime, string $format = 'short'): string
	{
		$time = $dateTime->getTimestamp();

		if ($format === 'long') {
			// j month Y

			if (self::$mutation->langCode === 'en') {
				$month = self::$translator->translate('date_month_' . date('n', $time));
				return date('j', $time) . ' ' . $month . ', ' . date('Y', $time);

			} elseif (self::$mutation->langCode === 'sk') {
				$month = self::$translator->translate('date_month_' . date('n', $time));
				return date('j', $time) . '. ' . $month . ', ' . date('Y', $time);

			} else {
				// cs
				//$days = array(0 => 'Neděle', 1 => 'Pondělí', 2 => 'Úterý', 3 => 'Středa', 4 => 'Čtvrtek', 5 => 'Pátek', 6 => 'Sobota');
//				$months = array(1 => 'ledna', 2 => 'února', 3 => 'března', 4 => 'dubna', 5 => 'května', 6 => 'června', 7 => 'července', 8 => 'srpna', 9 => 'září', 10 => 'října', 11 => 'listopadu', 12 => 'prosince');
				//$day = $days[date('w', $time)];
				$month = self::$translator->translate('date_month_' . date('n', $time));
				return date('j', $time) . '. ' . $month . ', ' . date('Y', $time);
			}
		} else {
			// short - j/n/Y
			if (self::$mutation->langCode === 'en') {
				if ($format !== 'short') {
					$formatDate = $format;
				} else {
					$formatDate = 'n/j/Y';
				}

//				return date('n/j/Y', $time); // american
				return date('j/n/Y', $time); // british

			} else {
				// cs, sk
				if ($format !== 'short') {
					$formatDate = $format;
				} else {
					$formatDate = 'j/n/Y';
				}

				return date($formatDate, $time);
			}
		}
	}


	public static function stock(int|float $n): string
	{
		return match (true) {
			$n > 10 => 'stock_over_10',
			$n > 5 => 'stock_over_5',
			$n > 2 => 'stock_over_2',
			$n === 2 => 'stock_two_pieces',
			$n === 1 => 'stock_one_piece',
			default => 'stock_zero',
		};
	}


	/**
	 * Czech date formatting - used in administration
	 */
	public static function niceDate(DateTimeImmutable $time): string
	{
		$ts = $time->getTimestamp();
		$days = [0 => 'Neděle', 1 => 'Pondělí', 2 => 'Úterý', 3 => 'Středa', 4 => 'Čtvrtek', 5 => 'Pátek', 6 => 'Sobota'];
		$months = [1 => 'ledna', 2 => 'února', 3 => 'března', 4 => 'dubna', 5 => 'května', 6 => 'června', 7 => 'července', 8 => 'srpna', 9 => 'září', 10 => 'října', 11 => 'listopadu', 12 => 'prosince'];

		$day = $days[date('w', $ts)];
		$month = $months[date('n', $ts)];

		return $day . ', ' . date('j', $ts) . '. ' . $month . ' ' . date('Y', $ts) . ', ' . date('H:i', $ts);
	}


	/**
	 * Plural: three forms, special cases for 1 and 2, 3, 4.
	 * (Slavic family: Slovak, Czech)
	 */
	public static function plural(int $n, string ...$plurals): string
	{
		return $plurals[($n === 1) ? 0 : (($n >= 2 && $n <= 4) ? 1 : 2)];
	}


	/**
	 * @static
	 * @param string $text
	 * @return string
	 */
	public static function parseUrl(string $text): string
	{
		$text = preg_replace('!(\s|^)((https?://|www\.)+[a-z0-9_./?=&-]+)!i', ' <a href="$2">$2</a> ', $text);
		$text = str_replace('href="www.', 'href="http://www.', $text);
		// todo fix regex $text = preg_replace("/([\w-?&;#~=\.\/]+\@(\[?)[a-zA-Z0-9\-\.]+\.([a-zA-Z]{2,3}|[0-9]{1,3})(\]?))/i", "<a href=\"mailto:$1\" rel=\"nofollow\">$1</a>", $text);
		return $text;
	}


	/**
	 * Escape xml value
	 */
	public static function cdata(string $string, ?string $tag = null): string
	{
		$exclude = ['URL', 'IMGURL', 'EAN', 'PRODUCTNO', 'ITEM_TYPE', 'SHOP_DEPOTS'];
		$string = trim($string);

		if (!is_numeric($string) && ($tag && !in_array($tag, $exclude) || !$tag)) {
			$string = '<![CDATA[' . $string . ']]>';
		}

		return $string;
	}


	public static function formatNumber(float $number): string
	{
		return number_format($number, 0, ',', ' '); // ! ctvrtym parametrem je nezlomitelna mezera
	}


	public static function prepareStrJs(string $s): string
	{
		return str_replace(['"', "'"], ['', ''], $s);
	}


	public static function formatMoney(Money $money): string
	{
		return $money->formatTo(self::$mutation->langCode);
	}


	public static function texy(string $text, bool $clean = false): string
	{
		$texy = new Texy();
		$texy->mergeLines = false;
		$parts = explode(PHP_EOL, $text);
		foreach ($parts as $key => $part) {
			$part = trim($part);
			if ($part) {
				if ($clean) {
					$texy->process($part, true);
					$part = $texy->toText();
				} else {
					$part = $texy->process($part, true) . '<br>';
				}
			} else {
				if ($clean) {
					$part = '';
				} else {
					$part = '<br>';
				}
			}

			$parts[$key] = $part;
		}

		return implode(' ' . PHP_EOL, $parts);
	}



	/**
	 * Czech helper time ago in words.
	 */
	public static function timeAgoInWords(DateTimeImmutable $time): string
	{
		$delta = time() - $time->getTimestamp();

		if ($delta < 0) {
			$delta = (int) round(abs($delta) / 60);
			if ($delta === 0)
				return 'za okamžik';
			if ($delta === 1)
				return 'za minutu';
			if ($delta < 45)
				return 'za ' . $delta . ' ' . self::plural($delta, 'minuta', 'minuty', 'minut');
			if ($delta < 90)
				return 'za hodinu';
			if ($delta < 1440)
				return 'za ' . round($delta / 60) . ' ' . self::plural((int) round($delta / 60), 'hodina', 'hodiny', 'hodin');
			if ($delta < 2880)
				return 'zítra';
			if ($delta < 43200)
				return 'za ' . round($delta / 1440) . ' ' . self::plural((int) round($delta / 1440), 'den', 'dny', 'dní');
			if ($delta < 86400)
				return 'za měsíc';
			if ($delta < 525960)
				return 'za ' . round($delta / 43200) . ' ' . self::plural((int) round($delta / 43200), 'měsíc', 'měsíce', 'měsíců');
			if ($delta < 1051920)
				return 'za rok';
			return 'za ' . round($delta / 525960) . ' ' . self::plural((int) round($delta / 525960), 'rok', 'roky', 'let');
		}

		$delta = (int) round($delta / 60);
		if ($delta === 0)
			return 'před okamžikem';
		if ($delta === 1)
			return 'před minutou';
		if ($delta < 45)
			return "před $delta minutami";
		if ($delta < 90)
			return 'před hodinou';
		if ($delta < 1440)
			return 'před ' . round($delta / 60) . ' hodinami';
		if ($delta < 2880)
			return 'včera';
		if ($delta < 43200)
			return 'před ' . round($delta / 1440) . ' dny';
		if ($delta < 86400)
			return 'před měsícem';
		if ($delta < 525960)
			return 'před ' . round($delta / 43200) . ' měsíci';
		if ($delta < 1051920)
			return 'před rokem';
		return 'před ' . round($delta / 525960) . ' lety';
	}


	/**
	 * Czech helper time ago in words.
	 */
	public static function age(DateTimeImmutable $time): ?string
	{
		$delta = time() - $time->getTimestamp();

		if ($delta < 0) {
			return null;
		}

		$day = 86400;
		$month = 30 * $day;
		$year = 365 * $day;

		$y = (int) round($delta / $year);
		$z = $delta % $year;
		$m = (int) ceil($z / $month);

		$ret = '';

		if ($y > 0) {
			$ret .= $y . ' ' . self::plural($y, 'rok', 'roky', 'let');
			if ($m > 0) {
				$ret .= ', ' . $m . ' ' . self::plural($m, 'měsíc', 'měsíce', 'měsíců');
			}
		} else {
			$ret .= $m . ' ' . self::plural($m, 'měsíc', 'měsíce', 'měsíců');
		}

		return $ret;
	}


	/**
	 * Czech helper time ago in words.
	 */
	public static function days(DateTimeImmutable $time): string
	{
		$delta = time() - $time->getTimestamp();

		$x = (int) round($delta / 86400) * (-1);

		if ($x < 0) {
			return 'před ' . abs($x) . ' ' . self::plural($x, 'dnem', 'dny', 'dny');
		} else {
			return 'za ' . abs($x) . ' ' . self::plural($x, 'den', 'dny', 'dnů');
		}
	}


	public static function clear(string $str): string
	{
		return str_replace("\n", ' ', $str);
	}


	public static function copyright(int|string $year): string
	{
		$year = (int) $year;
		$actYear = (int) date('Y');

		if ($year === $actYear) {
			return (string) $year;
		}

		if ($year < $actYear) {
			return $year . ' - ' . $actYear;
		}

		return '';
	}


	public static function icon(string $id, string $modificator = ''): Stringable
	{
		$ret = '
			<span class="icon-svg icon-svg--' . $id . ' ' . $modificator . '">
				<svg class="icon-svg__svg">
					<use xlink:href="/static/img/icons.svg?version=' . (self::$version == "DEV" ? time() : self::$version) . '#icon-' . $id . '" x="0" y="0" width="100%" height="100%" />
				</svg>
			</span>
		';
		return Html::el()->setHtml($ret);
	}


	/**
	 * @param non-empty-string $delimiter
	 */
	public static function lineExploder(string $s, string $delimiter = "\n"): array
	{
		if ($s) {
			$ret = explode($delimiter, $s);
			$ret = array_map(function ($item) {
				return trim($item);
			}, $ret);
			return $ret;
		} else {
			return [];
		}
	}


	/**
	 * @param non-empty-string $delimiter
	 */
	public static function exploder(string $s, string $delimiter): array
	{
		return explode($delimiter, $s);
	}


	/**
	 * formátování tel. čísla
	 * vstupy:
	 * 00420123456789 --> +420 123 45 67 89
	 * +420123456789 --> +420 123 45 67 89
	 * 603432580 --> 603 43 25 80
	 * 59 691 11 89 --> 596 91 11 89
	 */
	public static function phoneFormat(string $phone): string
	{
		if (preg_match('/^[0-9\+ ]+$/', $phone)) {
			// obsahuje jen cislice, mezery a +
			$phone = str_replace(' ', '', $phone);
			$subv = [$phone];
		} else {
			return $phone;
		}

		$ret = '';
		foreach ($subv as $sv) {
			$sv = preg_replace('/^00/', '+', $sv);
			if (isset($sv[0]) && $sv[0] === '+') {
				$plus = '+';
			} else {
				$plus = '';
			}

			$sv = str_replace(' ', '', $sv);
			$sv = str_replace('+', '', $sv);

			if ($sv) {
//				$ret .=  $plus.number_format($sv, 0, ", ", " ");
//				$newstring = substr($sv, -6);
				$result = '';
				if (strlen($sv) > 9 && preg_match('/^(\d{3})/', $sv, $matches)) {
					$result .= $matches[1] . ' '; // predcisli statu
				}

				if (preg_match('/(\d{3})(\d{2})(\d{2})(\d{2})$/', $sv, $matches)) {
					$result .= $matches[1] . ' ' . $matches[2] . ' ' . $matches[3] . ' ' . $matches[4];
				}

				$ret .= $plus . $result;
			} else {
				$ret .= $plus;
			}
		}

		return rtrim($ret, ', ');
	}


	public static function formatNumberPrecision(int|float $number, int $decimals = 0): string
	{
		$negation = ($number < 0) ? (-1) : 1;
		$coefficient = 10 ** $decimals;
		return strval($negation * floor((abs($number) * $coefficient)) / $coefficient);
	}


	public static function tables(string $htmlString): string
	{
		$htmlString = str_replace('<table', '<div class="u-table-responsive"><table', $htmlString);
		return str_replace('</table>', '</table></div>', $htmlString);
	}

	public static function lazyLoading(string $htmlString): string
	{
		return str_replace('<img ', '<img loading="lazy" ', $htmlString);
	}

	public static function externalLink(string $htmlString): string
	{
		if (str_starts_with($htmlString, 'http')) {
			return $htmlString;
		} else {
			return '//'.$htmlString;
		}
	}

	public static function obfuscateEmailAddresses(FilterInfo $info, string $htmlString): string
	{
		if ( ! in_array($info->contentType, [null, ContentType::Html], true)) {
			throw new LogicException("Filter |obfuscateEmailAddresses used in incompatible content type $info->contentType");
		}

		return preg_replace_callback(
			'/(\S+)@(\S+)/U',
			static fn(array $match) => $match[1] . '&#64;' . $match[2],
			$htmlString,
		);
	}

	/**
	 * Hooks form HTML id as bookmark into form target action URL
	 *
	 * @param Form $form
	 * @return void
	 */
	public static function actionAddFormId(Form $form): void
	{
		// TODO causes issue with lazy evaluation - changed URL if resolved to string later...
		$form->action .= '#' . $form->getElementPrototype()->id;
	}

}
