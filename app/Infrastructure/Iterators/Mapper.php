<?php declare(strict_types=1);

namespace App\Infrastructure\Iterators;

use Nette\Iterators\Mapper as NetteMapper;
use Traversable;
use Countable;

/**
 * Extends original Nette class with countable implementation
 */
class Mapper extends NetteMapper implements Countable
{
	private mixed $count;

	/**
	 * @param Traversable $iterator
	 * @param callable(mixed $current, string $key): mixed $callback
	 * @param int|callable(): mixed|null $count Default (NULL) for using count from inner iterator
	 */
	public function __construct(Traversable $iterator, callable $callback, int|callable $count = null)
	{
		parent::__construct($iterator, $callback);
		$this->count = $count;
	}

	public function count(): int
	{
		if (!isset($this->count)) {
			$this->count = iterator_count($this->getInnerIterator());
		} elseif (is_callable($this->count)) {
			$this->count = ($this->count)();
		}

		return $this->count;
	}
}
