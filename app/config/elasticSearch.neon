parameters:

	esBaseName: %config.projectName%_%stageName%

	config:
		elasticSearch:
			host: localhost
			port: 9200
			enabled: true
			pathToSynonyms: %config.APP_DIR%/../documents/es/synonyms/

			index:
				all:
					name: '%esBaseName%_all'
					enabled: true
				common:
					name: '%esBaseName%_common'
					enabled: true
				product:
					name: '%esBaseName%_product'
					enabled: true

elastica:
	debug: %debugMode%
	config:
		host: %config.elasticSearch.host%
		port: %config.elasticSearch.port%

services:
	-
		# Alternative client...
		class: Elasticsearch\Client
		factory: Elasticsearch\ClientBuilder::fromConfig(
			config: [hosts: [%config.elasticSearch.host%:%config.elasticSearch.port%]]
		)

	- App\Model\ElasticSearch\ConfigurationHelper

	- App\Model\Orm\EsIndex\EsIndexModel(%config.elasticSearch.index%)
	- App\Model\Orm\EsIndex\EsIndexFacade

	- \App\Model\ElasticSearch\IndexModel(%esBaseName%)
	- \App\Model\ElasticSearch\Repository()

	- \App\Model\ElasticSearch\All\Repository
	- \App\Model\ElasticSearch\Common\Repository

	- App\Model\ElasticSearch\AliasModel(%config.elasticSearch.index%)



## new version
	- App\Model\ElasticSearch\Service

	# index of ALL
	- App\Model\ElasticSearch\All\Facade

	- App\Model\ElasticSearch\All\ConvertorProvider
	- App\Model\ElasticSearch\All\Convertor\TreeData
	- App\Model\ElasticSearch\All\Convertor\BlogData
	- App\Model\ElasticSearch\All\Convertor\CalendarData
	- App\Model\ElasticSearch\All\Convertor\BenefitData
	- App\Model\ElasticSearch\All\Convertor\StoryData
	- App\Model\ElasticSearch\All\Convertor\EducationData
	- App\Model\ElasticSearch\All\Convertor\OfferData
	- App\Model\ElasticSearch\All\Convertor\FacultyData
	- App\Model\ElasticSearch\All\Convertor\ProgrammeData
	- App\Model\ElasticSearch\All\Convertor\BranchData
	- App\Model\ElasticSearch\All\Convertor\BlogTagData
	- App\Model\ElasticSearch\All\Convertor\BenefitTagData
	- App\Model\ElasticSearch\All\Convertor\StoryTagData
	- App\Model\ElasticSearch\All\Convertor\EducationTagData
	- App\Model\ElasticSearch\All\Convertor\CalendarTagData
	- App\Model\ElasticSearch\All\Convertor\ProductData
	- App\Model\ElasticSearch\All\Convertor\SeoLinkData
	- App\Model\ElasticSearch\All\Convertor\ProfileData
	- App\Model\ElasticSearch\All\Convertor\ProfileTagData
	- App\Model\ElasticSearch\All\Convertor\NewsletterData

	# index of COMMON
	- App\Model\ElasticSearch\Common\Facade
	- App\Model\ElasticSearch\Common\ResultReader
	- App\Model\ElasticSearch\Common\ConvertorProvider

	- App\Model\ElasticSearch\Common\Convertor\TreeData
	- App\Model\ElasticSearch\Common\Convertor\BlogData
	- App\Model\ElasticSearch\Common\Convertor\CalendarData
	- App\Model\ElasticSearch\Common\Convertor\BenefitData
	- App\Model\ElasticSearch\Common\Convertor\ProfileData
	- App\Model\ElasticSearch\Common\Convertor\StoryData
	- App\Model\ElasticSearch\Common\Convertor\EducationData
	- App\Model\ElasticSearch\Common\Convertor\OfferData


	#index of PRODUCT
	- App\Model\ElasticSearch\Product\ConvertorProvider
	- App\Model\ElasticSearch\Product\Facade
	- App\Model\ElasticSearch\Product\ResultReader

	- App\Model\ElasticSearch\Product\Convertor\BaseData
	- App\Model\ElasticSearch\Product\Convertor\CategoryData
	- App\Model\ElasticSearch\Product\Convertor\ParameterData
	- App\Model\ElasticSearch\Product\Convertor\StoreData
	- App\Model\ElasticSearch\Product\Convertor\PriceData
	- App\Model\ElasticSearch\Product\Convertor\TopScoreData


extensions:
	elastica: Contributte\Elastica\DI\ElasticaExtension
