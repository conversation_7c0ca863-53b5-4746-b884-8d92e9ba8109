includes:
	- environment.dev.neon

parameters:
	stageName: local

	admin:
		allowedIpRanges:
			- "**********/16" # default docker bridge range
			- "***********/16" # default orbstack range

	config:
		domainUrl: https://vut.superkoders.test/
		mutations:
			cs:
				domain: vut.superkoders.test
			en:
				domain: vut.superkoders.test

	database:
		host: vutabsolvent_db
		database: vutabsolvent
		user: root
		password: root

	redis:
		host: vutabsolvent_redis

elastica:
	config:
		host: vutabsolvent_es

messenger:
	transport:
		elasticFront:
			dsn: "sync://"
		elasticPriorityFront:
			dsn: "sync://"
		clonerFront:
			dsn: "sync://"
		failure:
			dsn: "sync://"

http:
	proxy:
		- *********/8

mail:
	host: vutabsolvent_mailcatcher
	port: 1025
