@import 'newsletter/variables';

// Bug fix for inline CSS
.inline-css-fix {
	display: none;
}

// Normalize
@import 'newsletter/normalize';

// Typography
@import 'newsletter/typography';

// Layout
@import 'newsletter/wrap';
@import 'newsletter/preheader';
@import 'newsletter/container';
@import 'newsletter/header';
@import 'newsletter/main';
@import 'newsletter/footer';

// Components
@import 'newsletter/helper';
@import 'newsletter/btn';
@import 'newsletter/article';

// Utilities
@import 'newsletter/utilities';
