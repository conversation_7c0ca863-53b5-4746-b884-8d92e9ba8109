@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-socials {
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 1rem;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		display: table-cell;
		vertical-align: middle;
		width: 5rem;
		height: 5rem;
		border: 0.1rem solid variables.$colorBd;
		text-align: center;
		transition: border-color variables.$t;
		.hoverevents &:focus,
		.hoverevents &:hover {
			border-color: variables.$colorBdDarken;
		}
	}
	&__icon.icon-svg {
		width: 2rem;
		height: 2rem;
		&--facebook {
			color: variables.$colorSecondary;
		}
		&--twitter {
			color: variables.$colorTwitter;
		}
		&--youtube {
			color: #e4002b;
		}
		&--instagram {
			color: #c13584;
		}
		&--linkedin {
			color: #0077b5;
		}
	}
}
