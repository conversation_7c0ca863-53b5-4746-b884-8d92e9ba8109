@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-gallery {
	$s: &;
	&__inner {
		padding: 1rem;
		border: 0.1rem solid variables.$colorBd;
	}
	&__link {
		position: relative;
		display: block;
	}
	&__img img {
		transition: transform variables.$t;
	}
	&__content {
		position: absolute;
		inset: 0;
		z-index: 1;
		align-content: center;
		text-align: center;
	}

	// MODIF
	&__link--overlay &__img::before {
		position: relative;
		z-index: 1;
		background: rgba(#0c1627, 0.7);
	}

	// HOVERS
	.hoverevents &__link:hover img {
		transform: scale(1.1);
	}
}
