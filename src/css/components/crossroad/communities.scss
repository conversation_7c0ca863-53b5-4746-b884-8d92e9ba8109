@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-communities {
	--gap: 2rem;
	&__faculties {
		@extend %reset-ul;
		display: flex;
		flex-direction: column;
		gap: 4rem;
	}
	&__faculty {
		@extend %reset-ul-li;
		padding: var(--gap);
		border: 0.1rem solid variables.$colorBd;
	}
	&__name-wrap {
		display: flex;
		gap: var(--gap);
		align-items: center;
		letter-spacing: 0.08em;
		.faculty {
			flex: 0 0 auto;
		}
	}
	&__title {
		letter-spacing: 0.14em;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		flex-direction: column;
		gap: 1.2rem;
	}
	&__item {
		@extend %reset-ul-li;
		a {
			color: variables.$colorSecondary;
			font-weight: 600;
		}
		.item-icon {
			--icon-size: 1.3rem;
			--gap: 0.8rem;
			--icon-color: #bbc1c2;
			margin-right: 0.6rem;
			font-size: 1.4rem;
		}
	}

	// MQ
	@media (config.$md-up) {
		--gap: 2.6rem;
		&__list {
			margin: 0 0 0 calc(5.5rem + var(--gap));
		}
	}
}
