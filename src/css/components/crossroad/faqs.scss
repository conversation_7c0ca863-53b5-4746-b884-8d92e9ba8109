@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-faqs {
	$s: &;
	&__list {
		@extend %reset-ul;
		border: 0.1rem solid variables.$colorBd;
	}
	&__item {
		@extend %reset-ul-li;
		& + & {
			border-top: 0.1rem solid variables.$colorBd;
		}
	}
	&__title {
		margin: 0;
		font-family: variables.$font;
		font-weight: bold;
		font-size: 1.4rem;
		line-height: calc(20 / 14);
	}
	&__link {
		position: relative;
		display: block;
		width: 100%;
		padding: 1.4rem 4rem 1.4rem 1.4rem;
		text-decoration: none;
	}
	&__icon {
		position: absolute;
		top: 2.1rem;
		right: 1.4rem;
		color: variables.$colorRockBlue;
		transition: transform variables.$t;
	}
	&__content {
		padding: 0 1.4rem 1.9rem;
	}
	&__more {
		display: none;
	}

	// STATES
	&__item.is-open {
		#{$s} {
			&__link {
				color: variables.$colorText;
			}
			&__icon {
				transform: scale(-1);
			}
			&__more {
				display: block;
			}
		}
	}

	// HOVERS
	.hoverevents &__link:focus {
		text-decoration: underline;
	}

	// MQ
	@media (config.$md-up) {
		&__link {
			padding: 1.9rem 8.9rem 1.9rem 1.9rem;
		}
		&__icon {
			top: 2.6rem;
			right: 2.4rem;
		}
		&__content {
			padding: 0 1.9rem 2.3rem;
		}
	}
}
