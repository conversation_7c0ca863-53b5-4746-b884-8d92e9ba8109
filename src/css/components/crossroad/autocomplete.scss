@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-autocomplete {
	position: absolute;
	top: calc(var(--menu-height) - 0.1rem);
	right: 0;
	left: 0;
	z-index: 1;
	border: 0.1rem solid variables.$colorBd;
	background: variables.$colorWhite;
	box-shadow: 0 0.5rem 3rem rgba(#0c1627, 0.1);
	&__list {
		@extend %reset-ul;
		&--persons {
			padding: 1rem 0;
		}
	}
	&__list + &__list {
		border-top: 0.1rem solid variables.$colorBd;
	}
	&__item {
		@extend %reset-ul-li;
		padding: 1rem 1.9rem;
		line-height: calc(20 / 14);
		cursor: pointer;
		transition: background-color variables.$t;
		.hoverevents &:hover,
		.hoverevents &:focus {
			background: #fbfbfb;
		}
		&--person {
			padding: 0;
		}
	}

	@media (config.$md-up) {
		right: 6.6rem;
		&__item {
			padding-right: 2.9rem;
			padding-left: 2.9rem;
		}
		&__item--person {
			padding: 0;
		}
	}
}
