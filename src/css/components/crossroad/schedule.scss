@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-schedule {
	font-size: 1.4rem;
	&__list {
		--grid-y-spacing: 2.5rem;
	}
	&__item {
		border-bottom-width: 0;
	}
	&__label {
		position: relative;
		display: flex;
		margin: 0;
		padding: 0 0 var(--grid-y-spacing) 3rem;
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 0.3rem;
		}
		&::before {
			bottom: 0;
			left: 0.5rem;
			width: 0.1rem;
			background: variables.$colorBd;
		}
		&::after {
			left: -0.2rem;
			width: 1.5rem;
			height: 1.5rem;
			border: 0.4rem solid variables.$colorWhite;
			border-radius: 0.5rem;
			background: variables.$colorRockBlue;
		}
		& > .item-icon {
			--icon-size: 1.3rem;
			--gap: 1rem;
			--icon-offset: 0.3em;
			--icon-color: #{variables.$colorSecondary};
			align-items: flex-start;
		}
	}
	&__item:last-child &__label::before {
		content: none;
	}
	&__link {
		margin: 0 0 0.5rem;
		color: variables.$colorSecondary;
	}
	&__info {
		display: flex;
		flex-wrap: wrap;
		gap: 1.5rem;
		align-items: center;
		.item-icon {
			--gap: 1rem;
			--icon-color: #{variables.$colorBdDarken};
		}
	}

	// HOVERS
	.hoverevents &__link:hover {
		color: variables.$colorSecondaryDarken;
	}

	// MQ
	@media (config.$md-up) {
		&__item:nth-last-child(2) &__label::before {
			content: none;
		}
	}
}
