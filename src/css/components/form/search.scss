@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-search {
	position: relative;
	z-index: 3;
	border-bottom: 0.1rem solid variables.$colorWhite;
	visibility: hidden;
	transition: transform variables.$t ease-in-out, visibility variables.$t;
	transform: translateX(100%);

	// STATES
	&.is-open {
		visibility: visible;
		transform: translateX(0);
	}
	.no-js &:hover,
	.no-js .header:has(.m-main__item--search:hover) & {
		visibility: visible;
		transform: translateX(0);
	}
}
