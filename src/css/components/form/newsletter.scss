@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-newsletter {
	&__title {
		max-width: 41rem;
		margin-bottom: 2rem;
	}

	// MQ
	@media (config.$lg-up) {
		&__title {
			font-size: 2.4rem;
			.b-newsletter-form__wrap & {
				text-align: center;
			}
		}
	}
	@media (config.$md-down) {
		&__title {
			font-size: 2.4rem;
		}
	}
	@media (config.$md-up) {
		&__title {
			margin-bottom: 0;
		}
	}
	@media (config.$md-up) and (config.$xl-down) {
		max-width: 64rem;
		.inp__fix {
			max-width: 37rem;
		}
		&__title {
			margin-bottom: 2.5rem;
		}
	}
}
