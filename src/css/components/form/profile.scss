@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-profile {
	&__remove {
		position: relative;
		top: 0.7rem;
	}
	&__cell {
		&--items {
			flex: 0 0 auto;
			width: calc(100% - 3.6rem - var(--grid-x-spacing));
		}
		&--remove {
			flex: 0 0 auto;
			width: calc(3.6rem + var(--grid-x-spacing));
		}
	}
	&__submit {
		position: sticky;
		bottom: 1.5rem;
		z-index: 5;
	}
}
