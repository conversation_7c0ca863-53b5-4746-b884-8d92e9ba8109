/* stylelint-disable declaration-no-important */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-main {
	$s: &;
	position: relative;
	font-family: variables.$fontSecondary;
	&::before,
	&::after {
		content: '';
		position: absolute;
		top: 0;
		z-index: 1;
		width: 3.5rem;
		height: 100%;
		opacity: 0;
		visibility: hidden;
		pointer-events: none;
		transition: opacity variables.$t, visibility variables.$t;
	}
	&::before {
		left: 0;
		background: linear-gradient(to right, variables.$colorWhite, variables.$colorWhite 10px, rgba(variables.$colorWhite, 0));
	}
	&::after {
		right: 0;
		background: linear-gradient(to left, variables.$colorWhite, variables.$colorWhite 10px, rgba(variables.$colorWhite, 0));
	}

	.b-profile__nav &,
	.b-hero-header__nav & {
		display: inline-block;
		vertical-align: top;
		max-width: 100%;
		box-shadow: 0 0.5rem 3rem rgba(#0c1627, 0.1);
	}
	&--header &__link {
		padding-top: 2.2rem;
		padding-bottom: 2.2rem;
	}
	&__toggle {
		position: relative;
		float: right;
		width: 12.7rem;
		padding: 2rem 3rem 1.9rem 5.5rem;
		border-left: 0.1rem solid variables.$colorBd;
		text-transform: uppercase;
		text-decoration: none;
		&-icon,
		&-icon:first-child {
			position: absolute;
			top: 50%;
			left: 3rem;
			margin-top: -0.6rem;
			color: variables.$colorSecondary;
			transition: opacity variables.$t, visibility variables.$t;
		}
		&-icon--close {
			left: 3.2rem;
			opacity: 0;
			visibility: hidden;
		}
		&-label {
			color: variables.$colorText;
			letter-spacing: 0.08em;
		}
		&.is-open {
			background: #fbfbfb;
			#{$s}__toggle-icon {
				&--close {
					opacity: 1;
					visibility: visible;
				}
				&--open {
					opacity: 0;
					visibility: hidden;
				}
			}
		}
	}
	&__toggle-sub {
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		padding: 2.9rem 2rem;
		color: variables.$colorSecondary;
		.hoverevents &:hover,
		.hoverevents &:focus {
			color: variables.$colorSecondaryDarken;
		}
	}
	&__toggle-sub-icon {
		display: block;
		margin-left: auto;
		transition: transform variables.$t;
		#{$s}__item.is-open & {
			transform: scaleY(-1);
		}
	}
	&__wrap {
		white-space: nowrap;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
	}
	&__list {
		@extend %reset-ul;
		display: table;
	}
	&__list + &__list {
		border-left: 0.1rem solid variables.$colorBd;
	}
	&__item {
		@extend %reset-ul-li;
		position: relative;
		display: table-cell;
		vertical-align: top;
	}
	&__item + &__item {
		border-left: 0.1rem solid variables.$colorBd;
	}
	&__item + &__item &__submenu {
		left: -0.1rem;
	}
	&__link {
		display: block;
		padding: 2rem 2.8rem;
		background: variables.$colorWhite;
		color: variables.$color;
		line-height: calc(20 / 14);
		letter-spacing: 0.08em;
		text-align: center;
		text-transform: uppercase;
		text-decoration: none;
		transition: background-color variables.$t, color variables.$t;
		.hoverevents &:focus,
		.hoverevents &:hover,
		&.is-active {
			background: #fbfbfb;
			color: variables.$colorSecondary;
		}
		&--icon {
			padding-right: 2.2rem;
			padding-left: 2.1rem;
		}
		&--lang {
			padding-right: 2.2rem;
			padding-left: 2.2rem;
			background: variables.$colorBlack;
			color: variables.$colorWhite;
		}
		&--sm {
			padding: 1.5rem 2rem;
		}
		&--primary {
			background: variables.$colorPrimary;
			color: variables.$colorWhite;
			.hoverevents &:focus,
			.hoverevents &:hover {
				background: variables.$colorPrimaryDarken;
			}
		}
	}
	&__icon {
		vertical-align: top;
		width: 2rem;
		height: 2rem;
	}
	&__badge {
		position: absolute;
		top: 1.6rem;
		right: 1.4rem;
		color: variables.$colorGray;
	}

	// MODIF
	&--primary &__link {
		.hoverevents &:focus,
		.hoverevents &:hover,
		&[aria-current],
		&[aria-selected='true'] {
			background: variables.$colorPrimary;
			color: variables.$colorWhite;
			&::before {
				border-color: variables.$colorPrimary;
			}
		}
	}
	&--sub &__link {
		.hoverevents &:focus,
		.hoverevents &:hover,
		&[aria-current],
		&[aria-selected='true'] {
			background: variables.$colorSecondary;
			color: variables.$colorWhite;
			&::before {
				border-color: variables.$colorSecondary;
			}
		}
	}
	&--bd {
		#{$s} {
			&__link::before {
				content: '';
				position: absolute;
				top: 0;
				right: -0.1rem;
				bottom: 0;
				left: 0;
				border: 0.1rem solid variables.$colorBd;
				transition: border-color variables.$t;
			}
			&__item + #{$s}__item #{$s}__link:before {
				border-left: 0;
			}
			&__item:last-child #{$s}__link:before {
				right: 0;
			}
		}
	}

	// STATES
	&.is-left-scrollable::before,
	&.is-right-scrollable::after {
		opacity: 1;
		visibility: visible;
	}

	// MQ
	@media (config.$xxl-down) {
		&__link {
			padding-right: 1.8rem;
			padding-left: 1.8rem;
		}
	}
	@media (config.$xl-down) {
		&__list {
			max-height: calc(100dvh - var(--menu-height));
			overflow: hidden;
			overflow-y: auto;
		}
		&__submenu--m {
			position: absolute;
			top: 100%;
			left: 0;
			z-index: 1;
			min-width: 24.5rem;
			border: 0.1rem solid variables.$colorBd;
			background: variables.$colorWhite;
			opacity: 0;
			visibility: hidden;
			transition: opacity variables.$t, visibility variables.$t;
			box-shadow: 0 5px 30px rgba(#0c1627, 0.1);
			.hoverevents #{$s}__item:hover &,
			#{$s}__item.is-focused &,
			#{$s}__item.is-open & {
				opacity: 1;
				visibility: visible;
			}
		}

		&--header.is-open &__list--main {
			opacity: 1;
			visibility: visible;
		}
		&--header &__list {
			&--main {
				position: absolute;
				top: 100%;
				right: 0;
				z-index: 1;
				display: block;
				margin-top: 1px;
				background: variables.$colorWhite;
				opacity: 0;
				visibility: hidden;
				transition: opacity variables.$t, visibility variables.$t;
				box-shadow: 0 0.5rem 3rem rgba(#0c1627, 0.1);
				#{$s} {
					&__item {
						display: block;
						border-bottom: 0.1rem solid variables.$colorBd;
						&:last-child {
							border-bottom: 0;
						}
					}
					&__item + #{$s}__item {
						border-left: 0;
					}
					&__link {
						padding-right: 5.2rem;
						text-align: left;
						&--lang {
							padding-right: 3rem;
							padding-left: 3rem;
						}
					}
				}
			}
			&--side {
				float: right;
			}
		}
		&__submenu {
			height: 0;
			overflow: hidden;
		}
		&__item.is-open &__submenu {
			height: auto;
		}
	}
	@media (config.$md-down) {
		&--header &__list--main {
			left: -6.5rem;
		}
	}
	@media (config.$md-up) and (config.$xl-down) {
		&--header &__lsit--main {
			width: 32rem;
			overflow-y: auto;
		}
	}
	@media (config.$xl-up) {
		&--header {
			display: flex;
		}
		&--header &__list {
			float: left;
			&--main {
				flex: 1;
				max-height: none !important;
			}
		}

		&__toggle,
		&__toggle-sub {
			display: none;
		}
		&__submenu {
			position: absolute;
			top: 100%;
			left: 0;
			z-index: 1;
			min-width: 30rem;
			border: 0.1rem solid variables.$colorBd;
			background: variables.$colorWhite;
			opacity: 0;
			visibility: hidden;
			transition: opacity variables.$t, visibility variables.$t;
			box-shadow: 0 0.5rem 3rem rgba(#0c1627, 0.1);
			.js & {
				height: auto;
				overflow: visible;
			}
			.hoverevents #{$s}__item:hover &,
			#{$s}__item.is-focused & {
				opacity: 1;
				visibility: visible;
			}
			&--m {
				right: 0;
				left: auto;
			}
		}
		&__item.is-open &__submenu {
			opacity: 1;
			visibility: visible;
		}
	}
}
