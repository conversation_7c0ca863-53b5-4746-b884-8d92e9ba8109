@use 'config';
@use 'base/variables';
@use 'base/extends';
@use 'base/mixins';

.m-breadcrumb {
	$s: &;
	position: relative;
	height: 4.5rem;
	padding: 0 3rem;
	border-bottom: 0.1rem solid variables.$colorBd;
	font-family: variables.$fontSecondary;
	text-transform: uppercase;
	overflow: hidden;
	&__wrap {
		padding: 1.4rem 0;
		white-space: nowrap;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 1rem 2rem;
	}
	&__item {
		@extend %reset-ul-li;
		&::before {
			display: none;
		}
	}
	&__item + &__item {
		position: relative;
		padding-left: 2.6rem;
	}
	&__link {
		display: block;
		color: variables.$colorSecondary;
		line-height: calc(16 / 12);
		letter-spacing: 0.08em;
		text-decoration: none;

		&[aria-current] {
			color: variables.$colorGray;
			cursor: default;
		}

		@at-root a#{&} {
			.hoverevents &:focus {
				text-decoration: underline;
			}
			.hoverevents &:hover,
			.hoverevents &:focus {
				color: variables.$colorSecondaryDarken;
			}
		}
	}
	&__separator {
		position: absolute;
		top: 0.2rem;
		left: 0;
		color: variables.$colorRockBlue;
	}
	&__nav {
		position: absolute;
		top: 0;
		bottom: 0;
		width: 3rem;
		padding: 1.6rem 1.1rem;
		border: solid variables.$colorBd;
		border-width: 0 0.1rem;
		background: variables.$colorWhite;
		color: variables.$colorSecondary;
		opacity: 0;
		visibility: hidden;
		transition: opacity variables.$t, visibility variables.$t, color variables.$t;
		&.is-visible {
			opacity: 1;
			visibility: visible;
		}
		&::before {
			content: '';
			position: absolute;
			top: 0;
			z-index: 1;
			width: 3.5rem;
			height: 100%;
			pointer-events: none;
		}
		&--prev {
			left: 0;
			&::before {
				left: 100%;
				margin-left: 0.1rem;
				background: linear-gradient(to right, variables.$colorWhite, variables.$colorWhite 1rem, rgba(variables.$colorWhite, 0));
			}
		}
		&--next {
			right: 0;
			&::before {
				right: 100%;
				margin-right: 0.1rem;
				background: linear-gradient(to left, variables.$colorWhite, variables.$colorWhite 1rem, rgba(variables.$colorWhite, 0));
			}
		}
		&-icon {
			display: block;
		}
	}
}
