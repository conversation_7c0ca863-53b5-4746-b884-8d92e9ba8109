@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-sub {
	$s: &;
	display: flex;
	> :last-child {
		margin-bottom: 0;
	}
	&__list {
		@extend %reset-ul;
		margin-bottom: 1.5rem;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		position: relative;
		display: block;
		padding: 1rem 0 1rem 1.6rem;
		color: variables.$colorText;
		font-size: 1.3rem;
		line-height: 1.5;
		letter-spacing: 0.08em;
		text-transform: uppercase;
		text-decoration: none;
		&::before {
			content: '';
			position: absolute;
			top: 1.6rem;
			left: 0;
			width: 0.6rem;
			height: 0.6rem;
			border-radius: 0.1rem;
			background: variables.$colorSecondary;
		}

		.hoverevents &:hover,
		.hoverevents &:focus,
		&.is-active {
			color: variables.$colorText;
			text-decoration: underline;
		}
		&--icon {
			padding-left: 2.6rem;
			&::before {
				display: none;
			}
		}
	}
	&__col {
		flex: 1 1 auto;
		padding: 0 3rem 3rem;
		> :last-child {
			margin-bottom: 0;
		}
		&--highlighted {
			flex-grow: 0;
			width: 26rem;
			min-width: 26rem;
			padding: 3.5rem 4rem 5rem;
			border: 0.1rem solid variables.$colorBd;
			background: #fbfbfb;
		}
		&--sub {
			position: relative;
			flex-grow: 0;
			width: 32rem;
			min-width: 32rem;
			margin: -1rem 0;
			padding: 3.5rem 4.5rem;
			border-left: 0.1rem solid variables.$colorBd;
			&::before,
			&::after {
				@include mixins.triangle('right', 2rem, 4rem, variables.$colorBd);
				content: '';
				position: absolute;
				top: 2.5rem;
			}
			&::before {
				left: 0;
			}
			&::after {
				left: -0.1rem;
				border-color: transparent transparent transparent variables.$colorWhite;
			}
		}
	}
	&__col + &__col {
		margin-left: -0.1rem;
	}
	&__delimiter {
		margin: 0 -4rem 1rem -3rem;
	}
	&__delimiter + &__list {
		margin-bottom: -2rem;
	}
	&__icon {
		position: absolute;
		top: 1.2rem;
		left: 0;
		color: variables.$colorGray;
	}

	// MODIF
	&__item:has(&__list) {
		& > #{$s}__link {
			padding-left: 0;
			font-weight: bold;
			&::before {
				display: none;
			}
		}
		& > #{$s}__list {
			margin: 0;
		}
	}

	// MQ
	@media (config.$xl-down) {
		&__col {
			&--highlighted,
			&--sub {
				display: none;
			}
			.m-main__submenu--m & {
				padding-top: 2rem;
			}
		}
		&__delimiter {
			margin-right: -3rem;
		}
	}

	@media (config.$xl-up) {
		padding: 1rem;
		&__col {
			padding: 1.5rem 3rem 2.5rem 2rem;
		}
		&__delimiter + &__list {
			margin-bottom: -2.5rem;
		}
	}
}
