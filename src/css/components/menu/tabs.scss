@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-tabs {
	$s: &;
	margin: 0 auto 4rem;
	font-family: variables.$fontSecondary;
	&__list {
		@extend %reset-ul;
		display: flex;
		& > * {
			flex: 1 1 100%;
		}
	}
	&__item {
		@extend %reset-ul-li;
		display: flex;
		border: 0.1rem solid variables.$colorBd;
		& > * {
			flex: 1 1 auto;
		}
	}
	&__item + &__item {
		border-left: 0;
	}
	&__link {
		position: relative;
		display: flex;
		flex-direction: column;
		text-decoration: none;
		& > * {
			flex: 1 1 auto;
		}
		&::before {
			content: '';
			position: absolute;
			top: -0.1rem;
			right: -0.1rem;
			bottom: -0.1rem;
			left: -0.1rem;
			border: 0.1rem solid variables.$colorBdDarken;
			opacity: 0;
			transition: opacity variables.$t;
			box-shadow: inset 0 0 0 0.1rem variables.$colorBg;
		}
	}
	&__inner {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 1.2rem 2rem 1.1rem;
		color: variables.$colorSecondary;
		letter-spacing: 0.08em;
		text-align: center;
		text-transform: uppercase;
		transition: color variables.$t, background-color variables.$t;
		& > * {
			flex: 0 1 auto;
		}
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 100%;
			left: 50%;
			margin-left: -1.1rem;
			opacity: 0;
		}
		&::before {
			@include mixins.triangle('down', 2.2rem, 1.1rem, variables.$colorBdDarken);
			margin-top: 0.1rem;
		}
		&::after {
			@include mixins.triangle('down', 2.2rem, 1.1rem, variables.$colorBg);
			margin-top: 0;
		}
	}

	// HOVERS
	.hoverevents &__link:hover,
	&__link[aria-selected='true'] {
		&::before {
			opacity: 1;
		}
		#{$s}__inner {
			background: variables.$colorBg;
			color: variables.$colorText;
		}
		#{$s}__desc {
			color: variables.$colorText;
		}
	}

	// STATES
	&__link[aria-selected='true'] {
		&::before {
			top: -0.2rem;
			bottom: -0.2rem;
		}
		#{$s}__inner::before,
		#{$s}__inner::after {
			opacity: 1;
		}
	}

	// MQ
	@media (config.$md-down) {
		position: relative;
		margin-bottom: 2rem;
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 0;
			z-index: 1;
			width: 3.5rem;
			height: 100%;
			opacity: 0;
			visibility: hidden;
			pointer-events: none;
			transition: opacity variables.$t, visibility variables.$t;
		}
		&::before {
			left: 0;
			background: linear-gradient(to right, variables.$colorWhite, variables.$colorWhite 1rem, rgba(variables.$colorWhite, 0));
		}
		&::after {
			right: 0;
			background: linear-gradient(to left, variables.$colorWhite, variables.$colorWhite 1rem, rgba(variables.$colorWhite, 0));
		}
		// 	&.is-left-scrollable:before
		// 	&.is-right-scrollable:after
		// 		opacity 1
		// 		visibility visible
		&__wrap {
			padding: 0.1rem 0 2rem;
			overflow: hidden;
			overflow-x: auto;
			-webkit-overflow-scrolling: touch;
		}
	}
	@media (config.$md-up) and (config.$xl-down) {
		&__inner {
			padding-right: 1.5rem;
			padding-left: 1.5rem;
			letter-spacing: 0.04em;
		}
	}
}
