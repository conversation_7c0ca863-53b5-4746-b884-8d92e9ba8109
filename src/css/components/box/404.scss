@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-404 {
	padding-top: 4rem;
	padding-bottom: 4rem;
	background: url(variables.$imgPath + 'bg/dots-pattern.svg') repeat #e6efec;
	text-align: center;
	&__wrap {
		max-width: 88rem;
		margin: 0 auto;
		padding: 3rem 2rem;
		background: variables.$colorWhite;
		box-shadow: 0 0.5rem 3rem rgba(#0c1627, 0.2);
	}
	&__icon {
		width: 5.2rem;
		margin-bottom: 1.2rem;
	}
	&__title {
		margin: 0 0 1rem;
	}
	&__status {
		margin-bottom: 1.5rem;
		font-family: variables.$fontSecondary;
		font-size: 1.2rem;
		letter-spacing: 0.08em;
		text-transform: uppercase;
	}
	&__text {
		max-width: 40rem;
		margin: 0 auto;
	}

	// MQ
	@media (config.$md-up) {
		&__wrap {
			padding: 7rem 14rem 6rem;
		}
		&__text {
			margin-bottom: 3.5rem;
		}
	}
	@media (config.$xl-up) {
		padding-top: 8rem;
		padding-bottom: 8.5rem;
	}
}
