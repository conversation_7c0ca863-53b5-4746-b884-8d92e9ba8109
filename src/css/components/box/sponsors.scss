@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-sponsors {
	padding-top: 4rem;
	border-top: 0.1rem solid variables.$colorBd;
	&__imgs {
		display: flex;
		flex-wrap: wrap;
		gap: 2rem 1rem;
		justify-content: center;
		align-items: center;
		padding: 4rem 0;
		border: 0.1rem solid variables.$colorBd;
		border-width: 0.1rem 0;
		img {
			width: 15rem;
			height: 7rem;
			object-fit: contain;
		}
	}

	// MQ
	@media (config.$md-up) {
		padding-top: 6rem;
		&__imgs {
			padding: 6rem 0;
		}
	}
}
