@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-autocomplete-person {
	$s: &;
	display: block;
	padding: 1rem 1.9rem 1rem 7.9rem;
	text-decoration: none;
	.hoverevents &:hover,
	.hoverevents &:focus {
		#{$s}__name {
			color: variables.$colorSecondaryDarken;
			text-decoration: none;
		}
	}
	&__img {
		float: left;
		width: 5rem;
		margin-left: -6rem;
		padding: 0.4rem;
		border: 0.1rem solid variables.$colorBd;
	}
	&__content {
		padding-top: 0.5rem;
		line-height: calc(20 / 14);
	}
	&__name {
		margin-bottom: 0.1rem;
		color: variables.$colorSecondary;
		text-decoration: underline;
		transition: color variables.$t;
	}
	&__position {
		margin-bottom: 0;
		color: variables.$colorGray;
		font-size: 1.2rem;
	}

	@media (config.$md-up) {
		padding-right: 2.9rem;
		padding-left: 8.9rem;
	}
}
