@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-article {
	$s: &;
	display: flex;
	flex: 1 1 auto;
	flex-direction: column;
	.grid__cell > & {
		height: 100%;
	}
	&__link {
		display: flex;
		flex: 1 1 auto;
		flex-direction: column;
		padding: 3rem;
		border: 0.1rem solid variables.$colorBd;
		border-radius: 0.1rem;
		background: variables.$colorWhite;
		text-align: center;
		text-decoration: none;
		transition: box-shadow variables.$t;
		& > * {
			flex: 0 1 auto;
		}
	}
	&__img {
		width: calc(100% + 4rem);
		margin: 0 -2rem 2.5rem;
		img {
			transition: transform variables.$t;
		}
	}
	&__title {
		// max-width: 24rem;
		margin: 0 auto;
		color: variables.$colorSecondary;
		font-family: variables.$font;
		font-weight: bold;
		font-size: 1.8rem;
		line-height: calc(30 / 18);
		text-wrap: balance;
		text-decoration: underline;
		transition: color variables.$t;
		.icon-svg {
			top: -0.1rem;
			width: 1.4rem;
			margin-left: 0.8rem;
		}
	}
	&__content {
		display: flex;
		flex: 1;
		flex-direction: column;
		gap: calc(25em / 18);
		& > * {
			flex: 0 1 auto;
			margin-bottom: 0;
		}
	}
	&__meta {
		margin: auto 0 0;
		color: variables.$colorGray;
		font-family: variables.$fontSecondary;
		font-size: 1.2rem;
		line-height: calc(16 / 12);
		letter-spacing: 0.08em;
		text-transform: uppercase;
	}
	&__author {
		margin: auto 0 calc(8em / 18);
		color: variables.$colorText;
		font-size: 1.8rem;
		letter-spacing: 0.01em;
	}
	&__author + &__meta {
		margin-top: 0;
	}

	// MODIF
	&:has(&__img) &__link {
		padding-top: 1rem;
	}
	&:has(&__img) &__meta {
		order: 1;
	}

	// HOVERS
	.hoverevents &__link:hover,
	&__link:focus {
		box-shadow: 0 0.5rem 3rem rgba(#0c1627, 0.2);
		#{$s}__title {
			color: variables.$colorSecondaryDarken;
			text-decoration: none;
		}
	}
	.hoverevents &__link:hover &__img:not(&__img--logo) img {
		transform: scale(1.1);
	}
}
