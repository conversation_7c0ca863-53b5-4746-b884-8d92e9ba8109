@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-highlight {
	$s: &;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 4rem;
	padding-bottom: 2rem;
	text-align: center;
	&__title {
		margin-bottom: calc(7em / 44);
		font-family: variables.$fontSecondary;
		font-size: 4.4rem;
		line-height: calc(60 / 44);
	}
	&__text {
		flex: 1;
		width: 100%;
		max-width: 18rem;
		margin: 0 auto;
	}

	// MODIf
	&:has(.b-cta) {
		padding: 0;
	}

	// MQ
	@media (config.$md-up) {
		padding-top: 6rem;
		padding-bottom: 6rem;
	}
	@media (config.$xl-up) {
		min-height: 36rem;
		padding-top: 11.5rem;
	}
}
