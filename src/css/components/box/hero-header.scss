@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-hero-header {
	$s: &;
	position: relative;
	display: flex;
	margin-bottom: functions.spacing('md');
	&__img {
		flex: 0 0 auto;
		width: 100%;
		&::before {
			position: relative;
			z-index: 1;
			height: 100%;
			padding-top: percentage(calc(540 / 1440));
			background: rgba(#0c1627, 0.5);
		}
	}
	&__content {
		z-index: 1;
		display: flex;
		flex: 0 0 auto;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 100%;
		padding-top: 5rem;
		padding-bottom: 5rem;
		color: variables.$colorWhite;
		text-align: center;
		& > * {
			max-width: 70rem;
		}
	}
	&__img + &__content {
		position: relative;
		margin-left: -100%;
	}
	&__title {
		justify-content: center;
		margin: 0 0 1.5rem;
	}
	&__subtitle {
		margin: 0 0 2rem;
		font-weight: bold;
		line-height: calc(24 / 18);
	}
	&__btns {
		display: flex;
		flex-wrap: wrap;
		gap: 1rem;
		justify-content: center;
		margin: 0;
	}
	&__next {
		position: absolute;
		bottom: 0;
		left: 50%;
		margin: 0;
		padding: 1.4rem;
		color: inherit;
		transform: translate(-50%);
		.icon-svg {
			width: 2.2rem;
		}
	}

	// STATES
	// odsazení podle navazujícího CC
	&:has(+ #content .c-features:first-child) {
		margin-bottom: 0;
	}

	// HOVERS
	.hoverevents &__next:hover {
		color: variables.$colorPrimary;
	}

	// MQ
	@media (config.$md-up) {
		margin-bottom: functions.spacing('xl');
		&__content.holder {
			padding-top: 10rem;
			padding-bottom: 10rem;
		}
		&__title {
			margin: 0 0 2rem;
		}
		&__subtitle {
			margin: 0 0 3.5rem;
			font-size: 1.8rem;
		}
		&__btns .btn__text {
			min-width: 18rem;
		}
		&__next {
			bottom: 3rem;
		}
	}
	@media (config.$lg-up) {
		&__next {
			bottom: 4.5rem;
		}
	}
}
