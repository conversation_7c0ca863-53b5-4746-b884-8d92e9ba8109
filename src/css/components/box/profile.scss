@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-profile {
	font-size: 1.4rem;
	&__head {
		background-color: #2b313d;
		color: variables.$colorWhite;
	}
	&__info {
		padding-top: 4rem;
		padding-bottom: 4rem;
	}
	&__img {
		display: flex;
		justify-content: center;
		align-items: center;
		&::before {
			position: relative;
			z-index: 1;
			padding-top: percentage(calc(650 / 820));
			background: rgba(variables.$colorBlack, 0.5);
		}
	}
	&__title {
		margin-bottom: 2.5rem;
		& > * {
			margin-bottom: 0;
		}
		.title--xs .title__item {
			padding: 1.1rem 2rem 1rem;
		}
	}
	&__text,
	&__position {
		margin-bottom: 1.5rem;
		font-family: variables.$fontSecondary;
		letter-spacing: 0.08em;
		text-transform: uppercase;
	}
	&__contact {
		font-weight: bold;
		font-size: 1.6rem;
		line-height: clac(30 / 16);
		a {
			color: inherit;
		}
	}
	&__id {
		display: block;
		margin-top: 0.2rem;
	}
	&__nav {
		position: absolute;
		right: 2rem;
		left: 2rem;
		z-index: 1;
		margin-top: -2.5rem;
	}
	&__body {
		padding-top: 2.5rem;
		.holder .holder {
			padding: 0;
		}
	}
	&__side,
	&__content {
		padding-top: 5rem;
		padding-bottom: 4rem;
	}
	&__section--location:has(input[type='checkbox']:checked) {
		.grid__cell:not(:first-child) {
			display: none;
		}
		.inp {
			margin: 0;
		}
	}

	// HOVERS
	.hoverevents &__contact a:hover {
		color: inherit;
	}

	// MQ
	@media (config.$md-down) {
		&__info.holder {
			padding-bottom: 4rem;
		}
		&__img::before {
			padding-top: percentage(calc(640 / 820));
		}
		&__content {
			border-top: 0.1rem solid variables.$colorBd;
			border-image: none;
		}
	}
	@media (config.$md-up) {
		&__head {
			position: relative;
			display: flex;
			min-height: 64rem;
			& > * {
				flex: 0 1 auto;
			}
		}
		&__info {
			width: percentage(calc(620 / 1440));
		}
		&__img {
			width: percentage(calc(820 / 1440));
		}
		&__contact {
			font-size: 1.8rem;
		}
		&__nav {
			right: 4rem;
			left: 4rem;
		}
		&__content {
			border-left: 0.1rem solid variables.$colorBd;
			border-image: none;
		}
	}
	@media (config.$md-up) and (config.$xl-down) {
		&__title {
			.title:not(.title--xs) {
				--offset: 1rem;
				font-size: 3rem;
			}
		}
	}
	@media (config.$xl-up) {
		&__info {
			padding-top: 8rem;
			padding-bottom: 8rem;
		}
		&__side,
		&__content {
			padding-top: 9rem;
			padding-bottom: 8rem;
		}
	}
	@media (config.$xxl-up) {
		&__nav {
			right: 8rem;
			left: 8rem;
		}
	}
}
