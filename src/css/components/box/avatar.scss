@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-avatar {
	$s: &;
	position: relative;
	&__btn {
		position: absolute;
		.icon-svg {
			width: 1.4rem;
		}
		&--remove {
			top: -1.8rem;
			right: -1.8rem;
		}
		&--upload {
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
	}
	&__upload {
		position: absolute;
		inset: 0;
		cursor: pointer;
	}
	&__inp {
		display: flex;
		width: 100%;
		padding: 2rem;
		white-space: normal;
	}

	// STATES
	.no-js &__inp::-webkit-file-upload-button,
	.no-js &:has(&__inp:checked) &__btn--remove {
		display: none;
	}
	.no-js &:has(&__inp:checked) &__img:nth-child(2) {
		opacity: 0;
	}
}
