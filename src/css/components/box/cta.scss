@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-cta {
	--color: #{variables.$colorText};
	$s: &;
	position: relative;
	display: flex;
	margin: 0 0 -0.1rem;
	border: 0.1rem solid variables.$colorBd;
	border-width: 0.1rem 0;
	color: var(--color);
	text-align: center;
	text-decoration: none;
	&__img {
		flex: 0 0 auto;
		width: 100%;
		&::before {
			position: relative;
			z-index: 1;
			height: 100%;
			padding-top: percentage(calc(540 / 1440));
			background: rgba(#0c1627, 0.8);
		}
		img {
			transition: transform 0.6s;
		}
	}
	&__content {
		display: flex;
		flex: 0 0 auto;
		flex-direction: column;
		justify-content: center;
		width: 100%;
		padding-top: 4rem;
		padding-bottom: 4rem;
	}
	&__img + &__content {
		z-index: 1;
		margin-left: -100%;
	}
	&__title {
		justify-content: center;
		max-width: 52rem;
		margin: 0 auto calc(23em / 44);
	}
	&__subtitle {
		margin-bottom: calc(30em / 18);
		font-weight: bold;
	}

	// HOVERS
	.hoverevents &:hover {
		color: var(--color);
		img {
			transform: scale(1.1);
		}
	}

	// MODIF
	&:has(&__img) {
		--color: #{variables.$colorWhite};
		margin: 0;
		border: none;
	}
	.b-highlight & {
		flex: 1;
		width: 100%;
		border: none;
		#{$s}__title {
			margin: 0 0 1.2rem;
		}
	}

	// MQ
	@media (config.$md-up) {
		&__subtitle {
			font-size: 1.8rem;
		}
		&__content {
			padding-top: 8rem;
			padding-bottom: 8rem;
		}
		.b-highlight &__subtitle {
			font-family: variables.$fontSecondary;
			font-weight: 400;
			font-size: 1.4rem;
		}
	}
}
