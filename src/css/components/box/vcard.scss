@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-vcard {
	$s: &;
	position: relative;
	max-width: 34rem;
	height: 100%;
	margin: 0 auto;
	padding: 2rem;
	background: variables.$colorWhite;
	font-size: 1.4rem;
	transition: box-shadow variables.$t;
	box-shadow: 0 0.5rem 3rem rgba(#0c1627, 0.2);
	&__title {
		margin-bottom: 1.2rem;
		& > * {
			margin: 0;
		}
	}
	&__link {
		margin: 0;
		color: inherit;
		text-decoration: none;
	}
	&__title + &__contacts {
		margin-top: 4.6rem;
	}
	&__text {
		margin-bottom: 1.4rem;
	}
	&__position {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
		margin-bottom: calc(16em / 12);
		font-family: variables.$fontSecondary;
		font-size: 1.2rem;
		line-height: calc(16 / 12);
		letter-spacing: 0.08em;
		text-transform: uppercase;
	}
	&__img::before {
		padding-top: percentage(calc(266 / 207));
	}
	&__contacts {
		padding: 0.5rem 1rem;
		border: 0.1rem solid variables.$colorBd;
		font-size: 1.6rem;
		line-height: calc(35 / 16);
		a[href^='tel'] {
			color: inherit;
			font-weight: normal;
		}
		.item-icon {
			--icon-color: #{variables.$colorGray};
			display: flex;
		}
	}
	&__btn {
		margin: 1.5rem 0 0;
		.btn {
			--btn-padding-x: 1rem;
			--btn-padding-y: 1rem;
		}
	}
	&__validated {
		position: absolute;
		top: 2.2rem;
		right: 2.2rem;
		bottom: auto;
		left: auto;
		cursor: pointer;
	}

	// MODIF
	&--condensed {
		// flex-direction: column;
		max-width: none;
		#{$s} {
			&__validated {
				top: 1.2rem;
				right: 1.2rem;
			}
		}
	}

	// HOVERS
	.hoverevents &:has(&__link):hover {
		box-shadow: 0 0.5rem 3rem rgba(#0c1627, 0.5);
	}

	// MQ
	@media (config.$md-down) {
		&__side {
			margin-bottom: 2rem;
		}
	}
	@media (config.$md-up) {
		display: flex;
		gap: 2rem;
		align-items: flex-start;
		max-width: 64rem;
		&__side {
			flex: 0 0 auto;
			width: 20.7rem;
		}
		&__content {
			flex: 1;
		}
		&__contacts {
			padding: 0.9rem 2rem;
		}

		// MODIF
		&--condensed {
			flex-direction: column;
			max-width: none;
			#{$s}__side {
				width: 100%;
			}
		}
	}
}
