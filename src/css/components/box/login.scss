@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-login {
	background: url(variables.$imgPath + 'bg/dots-pattern.svg') repeat #e6efec;
	&__title {
		margin-bottom: 2rem;
		text-align: center;
	}
	&__wrap {
		max-width: 88rem;
		margin: 0 auto;
		padding: 3rem 2rem;
		background: variables.$colorWhite;
		box-shadow: 0 0.5rem 3rem rgba(#0c1627, 0.2);
	}

	// MQ
	@media (config.$xl-up) {
		&__wrap {
			padding: 8rem 5rem;
		}
	}
}
