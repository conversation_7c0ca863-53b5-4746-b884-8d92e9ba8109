@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-intro {
	position: relative;
	padding-top: 4rem;
	padding-bottom: 4rem;
	&::before {
		content: '';
		position: absolute;
		inset: 0;
		z-index: 1;
		background: rgba(#0c1627, 0.5);
	}
	&__bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	&__content {
		position: relative;
		z-index: 2;
	}
	&__annot span {
		padding: 0.7rem 1rem 0.8rem;
		background: darken(variables.$colorPrimary, 10%);
		color: variables.$colorWhite;
		font-family: variables.$fontSecondary;
		letter-spacing: 0.04em;
		box-decoration-break: clone;
	}
	&__search-menu a {
		position: relative;
		z-index: 1;
		display: block;
		min-height: 2.5rem;
		margin-left: -3.3rem;
		padding-left: 3.3rem;
	}

	// MODIF
	&:not(:has(&__bg)) {
		background: variables.$colorBg url(variables.$imgPath + 'bg/dots-pattern.svg') repeat;
		&::before {
			content: none;
		}
	}

	// MQ
	@media (config.$md-up) {
		padding-top: 7rem;
		padding-bottom: 7rem;
		&__annot {
			margin-top: 0.3rem;
			span {
				padding: 0.7rem 2rem 0.8rem;
			}
		}
	}
}
