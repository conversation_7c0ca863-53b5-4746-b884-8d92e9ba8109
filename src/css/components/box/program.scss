@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-program {
	$s: &;
	display: flex;
	flex-direction: column;
	max-width: 34rem;
	height: 100%;
	margin: 0 auto;
	font-size: 1.4rem;
	.b-profile & {
		max-width: 36rem;
	}
	& > * {
		flex: 0 1 auto;
	}
	&__date {
		display: block;
		margin-bottom: 0.3rem;
		color: variables.$colorGray;
		font-size: 1.2rem;
		text-transform: uppercase;
	}
	&__title {
		margin-bottom: 0;
		padding: 3.4rem 2rem;
		border: 0.1rem solid variables.$colorBdDarken;
		background: variables.$colorBg;
	}
	&__img {
		border: 0.1rem solid variables.$colorBd;
		border-width: 0 0.1rem;
	}
	&__content {
		display: flex;
		flex-direction: column;
		flex-grow: 1;
		padding: 3rem 2rem;
		border: 0.1rem solid variables.$colorBd;
		border-width: 0 0.1rem 0.1rem;
		background: variables.$colorWhite;
		& > * {
			margin-bottom: 2rem;
		}
	}
	&__btn {
		margin-top: auto;
		padding-top: 2rem;
	}
	&__btn:first-child {
		margin-top: auto;
		padding-top: 0;
	}

	// MODIF
	&--center {
		text-align: center;
	}

	@at-root a#{&} {
		color: inherit;
		text-decoration: none;
		&__subtitle {
			text-decoration: underline;
		}
		.hoverevents &:hover,
		&:focus {
			color: inherit;
			#{$s}__subtitle {
				text-decoration: none;
			}
		}
	}
}
