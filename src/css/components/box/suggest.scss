@use 'base/variables';

.b-suggest {
	position: absolute;
	top: 100%;
	right: 0;
	left: 0;
	z-index: 50;
	padding: 2rem;
	border: 0.1rem solid variables.$colorBd;
	background: variables.$colorWhite;
	opacity: 0;
	visibility: hidden;
	transition: opacity variables.$t, visibility 0s variables.$t;
	&__list {
		margin: 2rem * -1;
		&:first-child {
			margin-top: 2rem * -1;
		}
	}
	&__item {
		border-top: 0.1rem solid variables.$colorBd;
	}
	&__link {
		display: block;
		padding: 1rem 2rem;
		transition: color variables.$t, background-color variables.$t;
	}

	// STATEs
	&.is-visible {
		opacity: 1;
		visibility: visible;
		transition-delay: 0s, 0s;
	}

	.is-selected &__link,
	.hoverevents &__link:hover {
		background: variables.$colorBg;
	}
}
