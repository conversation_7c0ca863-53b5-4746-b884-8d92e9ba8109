@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-subject {
	&__title {
		margin-bottom: 0.3rem;
		font-family: variables.$font;
		font-weight: bold;
	}
	.tag {
		margin-left: 0.4rem;
	}
	&__link {
		color: variables.$colorSecondary;
		&:visited {
			color: variables.$colorSecondaryVisited;
		}
	}
	&__annot {
		@include mixins.inline-list(auto, variables.$fontSize);
		margin-bottom: 0;
	}
	&__content a {
		color: variables.$colorSecondary;
	}

	// HOVERS
	.hoverevents &__link:hover,
	.hoverevents &__content a:hover {
		color: variables.$colorSecondaryDarken;
	}
}
