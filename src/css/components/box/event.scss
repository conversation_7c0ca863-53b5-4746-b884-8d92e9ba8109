@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-event {
	$s: &;
	position: relative;
	display: flex;
	padding: 2rem;
	background: variables.$colorBg;
	text-align: center;
	text-decoration: none;
	.grid__cell > & {
		height: 100%;
	}
	& > * {
		flex: 1;
	}
	&__img {
		position: absolute;
		inset: 0;
		img {
			transition: transform 0.6s;
		}
		&::before {
			position: relative;
			z-index: 1;
			height: 100%;
			background: rgba(variables.$colorBlack, 0.5);
		}
	}
	&__wrap {
		position: relative;
		z-index: 1;
		padding: 4rem 2rem 3.5rem;
		border-radius: 0.1rem;
		background: variables.$colorWhite;
		box-shadow: 0 0.5rem 3rem rgba(#0c1627, 0.2);
	}
	&__date {
		margin: 0 auto 2rem;
	}
	&__title {
		margin: 0 auto;
		color: variables.$colorSecondary;
		text-wrap: balance;
		text-decoration: underline;
		.icon-svg {
			vertical-align: baseline;
			width: 1.5rem;
			margin-left: 0.5rem;
		}
	}

	// MODIF
	&:has(&__img) {
		border: none;
		background: none;
		#{$s} {
			&__title {
				color: variables.$colorWhite;
			}
			&__wrap {
				padding-top: 4.5rem padding-bottom 4rem;
				background: none;
				box-shadow: none;
			}
			&__img {
				top: 2rem;
				bottom: 2rem;
			}
		}
	}

	// HOVERS
	.hoverevents &:hover &__title {
		color: variables.$colorSecondaryDarken;
		text-decoration: none;
		transition: color variables.$t;
	}
	.hoverevents &:has(&__img):hover {
		#{$s}__title {
			color: variables.$colorWhite;
		}
		#{$s}__img img {
			transform: scale(1.1);
		}
	}

	// MQ
	@media (config.$md-up) {
		border: 0.1rem solid variables.$colorBd;
	}
}
