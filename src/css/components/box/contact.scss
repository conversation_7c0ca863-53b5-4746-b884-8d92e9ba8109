@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-contact {
	padding-top: 4rem;
	padding-bottom: 4rem;
	&__text {
		font-size: 1.6rem;
		line-height: calc(35 / 16);
		a[href^='tel'] {
			color: inherit;
		}
		.item-icon {
			--icon-color: #{variables.$colorBdDarken};
		}
	}

	&--primary {
		background: variables.$colorPrimary;
		color: variables.$colorWhite;
		.item-icon {
			--icon-color: inherit;
			color: inherit;
		}
	}

	@media (config.$md-up) {
		&--map {
			flex-grow: 1;
			height: 50%;
		}
	}

	@media (config.$lg-up) {
		padding-top: 7rem;
		padding-bottom: 7rem;
	}
}
