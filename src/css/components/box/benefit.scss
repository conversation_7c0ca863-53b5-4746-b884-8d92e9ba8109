@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-benefit {
	$s: &;
	display: flex;
	padding: 2rem;
	border: 0.1rem solid variables.$colorBd;
	color: inherit;
	font-size: 1.4rem;
	text-decoration: none;
	.grid__cell > & {
		height: 100%;
	}
	&__img {
		flex: 0 0 26%;
		min-width: 14rem;
		max-width: 25rem;
		aspect-ratio: 1/1;
		img {
			transition: transform variables.$t;
		}
	}
	&__title {
		color: variables.$colorSecondary;
		font-family: variables.$font;
		font-weight: bold;
		text-decoration: underline;
		transition: color variables.$t;
		&:first-child {
			margin: -0.3rem 0 0.5rem;
		}
	}

	// MODIF
	&__text--clamp {
		@include mixins.line-clamp(9);
	}

	// HOVERS
	.hoverevents &:hover {
		color: inherit;
		#{$s}__title {
			color: variables.$colorSecondaryDarken;
			text-decoration: none;
		}
		#{$s}__img img {
			transform: scale(1.1);
		}
	}

	// MQ
	@media (config.$md-down) {
		flex-direction: column;
		max-width: 34rem;
		margin: 0 auto;
		&__img {
			order: 1;
			margin: 0 auto;
			text-align: center;
		}
		&__content {
			flex: 1;
			width: 100%;
			margin: 0 0 2rem;
		}
	}
	@media (config.$md-up) {
		gap: 2rem;
		align-items: flex-start;

		&--center {
			align-items: center;
		}
	}
	@media (config.$md-up) and (config.$xl-down) {
		&__img {
			width: 36%;
			height: auto;
		}
	}
}
