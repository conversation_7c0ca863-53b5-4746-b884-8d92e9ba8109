@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-hero {
	position: relative;
	display: flex;
	font-size: 1.4rem;
	& > * {
		flex: 0 0 auto;
		width: 100%;
	}
	&__content {
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding-top: 3rem;
		padding-bottom: 3rem;
		background: url(variables.$imgPath + 'bg/dots-pattern.svg') repeat #e6efec;
	}
	&__inner {
		// position: relative;
	}
	&__title {
		margin: 0 0 calc(30em / 36);
		font-family: variables.$font;
		font-weight: 300;
	}
	&__subtitle {
		margin: 0;
		font-weight: bold;
		font-size: 1.8rem;
		line-height: calc(30 / 18);
	}
	&__annot {
		margin: 0;
	}
	&__playlink {
		position: absolute;
		top: 50%;
		left: 50%;
		z-index: 1;
		transform: translate(-50%, -50%);
	}

	// MQ
	@media (config.$md-down) {
		flex-wrap: wrap;
		&__content {
			order: 1;
		}
	}
	@media (config.$md-up) {
		& > * {
			width: 50%;
		}
		&__content {
			padding-top: 4rem;
			padding-bottom: 4rem;
			text-align: right;
		}
	}
	@media (config.$md-up) and (config.$xl-down) {
		flex-wrap: wrap;
	}
	@media (config.$xl-up) {
		&__content {
			padding-top: 8rem;
		}
		&__subtitle {
			position: relative;
			&::before {
				content: '';
				position: absolute;
				top: 1.4rem;
				right: -7rem;
				z-index: 1;
				width: 6rem;
				height: 0.2rem;
				background: variables.$colorSecondary;
			}
		}
		&__playlink {
			margin-top: -2rem;
		}
	}

	@media (config.$xl-up) {
		&__subtitle::before {
			right: -14rem;
			width: 12rem;
		}
	}
}
