@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-feature {
	$s: &;
	&__title {
		margin-bottom: calc(20em / 36);
	}
	&__content {
		padding-top: 4rem;
		padding-bottom: 4rem;
	}
	&__img {
		position: relative;
		padding-bottom: 4rem;
		text-align: center;
		&::before {
			content: '';
			position: absolute;
			inset: 0;
			margin-top: percentage(calc(240 / 720));
			background: url(variables.$imgPath + 'bg/dots-pattern.svg') repeat #e6efec;
		}
	}
	&__thumbnail {
		max-width: 48rem;
		margin: 0 auto;
		&--video::before {
			position: relative;
			z-index: 1;
			background: rgba(#0c1627, 0.5);
		}
	}
	&__playlink {
		position: absolute;
		top: 50%;
		left: 50%;
		z-index: 1;
		transform: translate(-50%, -50%);
	}

	// MODIF
	&--primary:nth-child(even) &__content,
	&--secondary:nth-child(odd) &__content {
		order: 1;
	}

	// MQ
	@media (config.$md-up) {
		display: flex;
		& > * {
			flex: 0 0 auto;
			width: 50%;
			padding-top: 4rem;
			padding-bottom: 4rem;
		}
		&__img::before {
			top: 0;
			bottom: auto;
			height: 0;
			margin-top: 0;
			padding-top: 60%;
		}
	}

	@media (config.$xl-up) {
		& > * {
			padding-top: 12rem;
			padding-bottom: 12rem;
		}
		&__content {
			padding-bottom: 8rem;
		}
		&__title {
			position: relative;
			padding-left: 8rem;
			&::before {
				content: '';
				position: absolute;
				bottom: 0;
				left: 0;
				z-index: 1;
				width: 6rem;
				height: 0.2rem;
				background: variables.$colorSecondary;
			}
		}

		// MODIF
		&--primary:nth-child(even) &__title,
		&--secondary:nth-child(odd) &__title {
			padding-left: 0;
			&::before {
				bottom: 0.7rem;
				left: -7rem;
			}
		}
	}
	@media (config.$xxl-up) {
		&__title {
			padding-left: 14rem;
			&::before {
				width: 12rem;
			}
		}

		// MODIF
		&--primary:nth-child(even) &__title::before,
		&--secondary:nth-child(odd) &__title::before {
			left: -14rem;
		}
	}
}
