@use 'config';
@forward 'utilities/clearfix';
@forward 'utilities/image';
@forward 'utilities/screen-reader';
@forward 'utilities/text';
@forward 'utilities/tracy';
@forward 'utilities/visibility';
@forward 'utilities/sizing';
@use 'base/mixins';
@use 'base/variables';
@use 'base/functions';

// Inspirujeme se z emmetího cheat-sheetu: https://docs.emmet.io/cheat-sheet/

$utilities: (
	'align': (
		property: vertical-align,
		class: va,
		values: (
			t: top,
			m: middle,
			b: bottom
		)
	),
	'display': (
		responsive: true,
		property: display,
		class: d,
		values: (
			n: none,
			b: block
		)
	),
	'font-family': (
		property: font-family,
		class: ff,
		values: (
			secondary: variables.$fontSecondary
		)
	),
	'float': (
		property: float,
		class: fl,
		values: (
			l: left,
			r: right
		)
	),
	'font-style': (
		property: font-style,
		class: fs,
		values: (
			i: italic
		)
	),
	'font-weight': (
		property: font-weight,
		class: fw,
		values: (
			l: 300,
			n: normal,
			b: bold
		)
	),
	'text-align': (
		property: text-align,
		class: ta,
		values: (
			l: left,
			r: right,
			c: center,
			j: justify
		)
	),
	'text-transform': (
		property: text-transform,
		class: tt,
		values: (
			l: lowercase,
			u: uppercase,
			c: capitalize
		)
	),
	'white-space': (
		property: white-space,
		class: whs,
		values: (
			nw: nowrap
		)
	),
	'margin-bottom': (
		property: margin-bottom,
		class: mb,
		responsive: true,
		values: (
			'0': 0,
			'xs': functions.spacing('xs'),
			'sm': functions.spacing('sm'),
			'md': functions.spacing('md'),
			'lg': functions.spacing('lg'),
			'xl': functions.spacing('xl'),
			'2xl': functions.spacing('2xl')
		)
	),
	'padding-top': (
		property: padding-top,
		responsive: true,
		class: pt,
		values: (
			'xs': functions.spacing('xs'),
			'sm': functions.spacing('sm'),
			'md': functions.spacing('md'),
			'lg': functions.spacing('lg'),
			'xl': functions.spacing('xl'),
			'2xl': functions.spacing('2xl')
		)
	),
	'padding-bottom': (
		property: padding-bottom,
		responsive: true,
		class: pb,
		values: (
			'xs': functions.spacing('xs'),
			'sm': functions.spacing('sm'),
			'md': functions.spacing('md'),
			'lg': functions.spacing('lg'),
			'xl': functions.spacing('xl'),
			'2xl': functions.spacing('2xl')
		)
	),
	'color': (
		property: color,
		class: c,
		values: (
			'red': variables.$colorRed,
			// 'text': variables.$colorText,
			'secondary': variables.$colorSecondary,
			'orange': variables.$colorOrange,
			'green': variables.$colorGreen,
			'gray': variables.$colorGray
		)
	),
	// 'background-color': (
	// 	property: background-color,
	// 	class: bgc,
	// 	values: (
	// 		default: variables.$color-bg,
	// 		primary: variables.$colorPrimary,
	// 		secondary: variables.$colorSecondary
	// 	)
	// ),
);

@include mixins.generate-utilities($utilities);

.u-mb-last-0 > *:last-child:not(.grid) {
	margin-bottom: 0;
}
.u-ml-auto {
	margin-left: auto;
}
.u-mt-0 {
	margin-top: 0;
}

.u-fz-lg {
	font-size: 1.8rem;
	line-height: calc(35 / 18);
}

#content:has(> *:is(.b-cta, .b-sponsors)),
*[class^='u-mb-']:has(+ *:is(.b-cta, .b-sponsors)) {
	margin: 0 0 -0.1rem;
}
