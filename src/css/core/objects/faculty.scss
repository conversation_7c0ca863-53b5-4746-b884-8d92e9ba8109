@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.faculty {
	display: inline-block;
	align-content: center;
	width: 5.5rem;
	height: 4rem;
	color: variables.$colorWhite;
	font-family: variables.$fontSecondary;
	font-size: 1.4rem;
	text-align: center;

	// MODIF
	// &--fa {
	// 	background: variables.$colorFA;
	// }
	// &--fekt {
	// 	background: variables.$colorFEKT;
	// }
	// &--fch {
	// 	background: variables.$colorFCH;
	// }
	// &--fit {
	// 	background: variables.$colorFIT;
	// }
	// &--fp {
	// 	background: variables.$colorFP;
	// }
	// &--fast {
	// 	background: variables.$colorFAST;
	// }
	// &--fsi {
	// 	background: variables.$colorFSI;
	// }
	// &--favu {
	// 	background: variables.$colorFaVU;
	// }
	// &--sti {
	// 	background: variables.$colorSTI;
	// }
	// &--usi {
	// 	background: variables.$colorUSI;
	// }
	// &--cesa {
	// 	background: variables.$colorCESA;
	// }
	// &--vutium {
	// 	background: variables.$colorVUTIUM;
	// }
}
