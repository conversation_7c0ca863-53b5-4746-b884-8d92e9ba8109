@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.tag {
	display: inline-block;
	vertical-align: middle;
	padding: 0.6rem 1rem 0.5rem;
	border-radius: 0.2rem;
	background: variables.$colorPrimary;
	color: variables.$colorWhite;
	font-family: variables.$fontSecondary;
	font-weight: normal;
	font-size: 1.4rem;
	line-height: variables.$lineHeight;
	letter-spacing: 0.08em;
	text-align: center;
	text-transform: uppercase;
	text-decoration: none;

	// &--xxs
	// 	font-size 10px
	// 	line-height unit(14/@font-size, '')
	// 	padding 3px 5px 2px
	// &--xxs&--outline
	// 	padding-top 2px
	// 	padding-bottom 1px

	// sizes
	&--xs {
		padding: 0.3rem 0.5rem 0.1rem;
		font-size: 1.2rem;
		line-height: calc(15 / 12);
	}
	&--sm {
		padding: 0.1rem 1rem 0;
	}
	// styles
	&--outline {
		border: 0.1rem solid variables.$colorBdDarken;
		background: variables.$colorWhite;
		color: variables.$colorText;
	}
	&--xs#{&}--outline {
		padding-top: 0.2rem;
		padding-bottom: 0;
	}
}
