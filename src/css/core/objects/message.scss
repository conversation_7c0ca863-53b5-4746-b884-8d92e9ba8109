@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.message {
	padding: 1.5rem 2rem;
	background: variables.$colorSecondary;
	color: variables.$colorWhite;
	font-weight: bold;
	> :last-child {
		margin-bottom: 0;
	}
	li::before {
		background-color: variables.$colorWhite;
	}
	a {
		color: variables.$colorWhite;
	}

	// VARIANT
	&--error {
		background: variables.$colorRed;
	}
	&--ok {
		background: variables.$colorGreen;
	}
	&--warning {
		background: variables.$colorOrange;
	}
	&--animate {
		transition: transform variables.$t ease;
		animation: shake 0.5s ease-in-out 0.4s 1; /* delay, play once */
	}

	// HOVERS
	.hoverevents & a:hover {
		color: inherit;
		text-decoration: none;
	}

	// MQ
	@media (config.$md-up) {
		padding: 2rem;
	}
}
