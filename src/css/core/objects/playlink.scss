@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.playlink {
	position: relative;
	display: block;
	padding: 2rem;
	&::before {
		content: '';
		display: block;
		width: 8rem;
		height: 8rem;
		margin: 0 auto;
		border-radius: 50%;
		background: variables.$colorWhite;
		transition: background variables.$t;
		box-shadow: 0 0 3rem rgba(#0c1627, 0.3);
	}
	&::after {
		@include mixins.triangle('right', 2.7rem, 3.5rem, variables.$colorSecondary);
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		margin: -1.7rem 0 0 -1rem;
		transition: border-color variables.$t;
	}

	// HOVERS
	.hoverevents &:hover,
	.hoverevents &:focus,
	.hoverevents a:hover &,
	.hoverevents a:focus & {
		&::before {
			background: variables.$colorSecondary;
		}
		&::after {
			border-color: transparent transparent transparent variables.$colorWhite;
		}
	}
}
