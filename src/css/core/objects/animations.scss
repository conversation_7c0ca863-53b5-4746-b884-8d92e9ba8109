// animation animation-rotate .8s infinite linear
@keyframes animation-rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(359deg);
	}
}

@keyframes shake {
	0% {
		transform: translateX(0);
	}
	20% {
		transform: translateX(-8px);
	}
	40% {
		transform: translateX(8px);
	}
	60% {
		transform: translateX(-6px);
	}
	80% {
		transform: translateX(6px);
	}
	100% {
		transform: translateX(0);
	}
}
