@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

add-to-calendar-button::part(atcb-button-wrapper) {
	padding: 0;
}
add-to-calendar-button::part(atcb-button) {
	margin: 0;
	padding: 1rem 3rem 1rem 2rem;
	border-width: 0.2rem;
	border-color: variables.$colorSecondary;
	border-radius: 0.1rem;
	background: variables.$colorWhite;
	color: variables.$colorSecondary;
	font-family: variables.$fontSecondary;
	font-weight: normal;
	font-size: variables.$fontSize;
	line-height: calc(16 / 14);
	letter-spacing: 0.08em;
	text-transform: uppercase;
	box-shadow: none;
}

add-to-calendar-button::part(atcb-list-wrapper) {
	top: 100%;
	left: 0;
	min-width: 100%;
	padding: 0;
}
add-to-calendar-button::part(atcb-list-item) {
	padding: 0.5rem 2rem;
}
