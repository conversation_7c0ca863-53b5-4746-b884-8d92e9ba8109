@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.paging {
	@extend %reset-ul;
	font-family: variables.$fontSecondary;
	&__list {
		@extend %reset-ul;
		display: inline-flex;
		flex-wrap: wrap;
		margin: 0.1rem 0 0 0.1rem;
	}
	&__item,
	&__item:first-child {
		@extend %reset-ul-li;
		margin: -0.1rem 0 0 -0.1rem;
		&--next {
			margin-left: -0.1rem;
		}
	}
	&__link {
		--size: 4rem;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		min-width: var(--size);
		height: var(--size);
		padding: 1rem;
		border: 0.1rem solid #dddddd;
		background: variables.$colorWhite;
		color: variables.$colorSecondary;
		letter-spacing: 0.08em;
		text-decoration: none;
		transition: color variables.$t, background-color variables.$t, border-color variables.$t;
		&[aria-current] {
			z-index: 1;
			height: calc(var(--size) + 0.2rem);
			margin-top: -0.1rem;
			border-color: variables.$colorBdDarken;
			background: variables.$colorBg;
			color: variables.$colorText;
		}
	}

	// HOVERS
	@at-root a#{&}__link {
		.hoverevents &:hover,
		.hoverevents &:focus {
			z-index: 1;
			border-color: variables.$colorBdDarken;
			background: variables.$colorBg;
			color: variables.$colorText;
		}
	}

	// MQ
	@media (config.$md-down) {
		&__item .btn__text {
			padding: 1rem 1.5rem;
		}
	}
	@media (config.$md-up) {
		&__link {
			--size: 5rem;
		}
	}
}
