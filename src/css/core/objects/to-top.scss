@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.to-top {
	display: none;

	// MQ
	@media (config.$md-up) {
		position: fixed;
		right: 1rem;
		bottom: 1rem;
		z-index: 10;
		display: block;
		opacity: 0;
		visibility: hidden;
		transition: background-color variables.$t, opacity variables.$t, visibility variables.$t;
		.icon-svg {
			position: absolute;
			top: 50%;
			left: 50%;
			margin: -0.4rem 0 0 -0.7rem;
		}

		// STATES
		.header.is-pinned ~ &,
		.header.is-unpinned ~ & {
			opacity: 1;
			visibility: visible;
		}

		// HOVERS
		.hoverevents &:hover,
		.hoverevents &:focus {
			background: variables.$colorSecondaryDarken;
			color: variables.$colorWhite;
		}
	}
	@media (config.$xl-up) {
		right: 50%;
		margin-right: -71rem;
	}
}
