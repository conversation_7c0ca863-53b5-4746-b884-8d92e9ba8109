@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.mark {
	--mark-bgc: #{variables.$colorBg};
	--mark-c: inherit;
	display: inline-block;
	// margin-right: 1rem;
	padding: 2rem;
	background: var(--mark-bgc);
	color: var(--mark-c);
	// font-family: variables.$fontSecondary;
	// letter-spacing: 0.04em;
	box-decoration-break: clone;

	// MODIF
	&--primary {
		--mark-bgc: #{mix(variables.$colorPrimary, variables.$colorWhite, 30%)};
	}
}
