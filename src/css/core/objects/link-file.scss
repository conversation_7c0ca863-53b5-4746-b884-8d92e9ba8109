@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.link-file {
	position: relative;
	display: inline-block;
	padding: 0.5rem 0 0.5rem 3.2rem;
	line-height: calc(25 / 14);
	text-align: left;
	text-decoration: none;
	&__name {
		margin-right: 0.5rem;
		text-decoration: underline;
	}
	&__icon {
		position: absolute;
		top: 0.6rem;
		left: 0;
		color: variables.$colorGray;
	}
	&__tag {
		position: relative;
		top: -0.4rem;
	}

	// MODIF
	&--lg {
		font-size: 1.6rem;
		line-height: calc(25 / 16);
	}
	&--lg &__tag {
		top: -0.2rem;
	}

	// STATES
	&:visited {
		color: variables.$colorPrimaryVisited;
	}

	// HOVER
	.hoverevents &:hover &__name {
		text-decoration: none;
	}
}
