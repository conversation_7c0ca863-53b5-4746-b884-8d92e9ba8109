@use 'config';
@use 'base/variables';
@use 'base/extends';
@use 'base/mixins';

%row,
.row {
	@include mixins.clearfix();
	position: relative;
	margin: 0;
	padding: 0;
}
.row-main {
	@extend %row;
	max-width: variables.$rowMainWidth;
	margin: 0 auto;
	&--xxs {
		max-width: 70rem;
	}
	&--xs {
		max-width: 86rem;
	}
	&--sm {
		max-width: 96rem;
	}
	&--md {
		max-width: 118rem;
	}
	&--lg {
		max-width: 120rem;
	}
}
.grid {
	--grid-x-spacing: #{variables.$gridGutter};
	--grid-y-spacing: #{variables.$gridGutter};
	$s: &;
	@extend %reset-ol;
	@extend %grid;
	margin-bottom: calc(var(--grid-y-spacing) * -1);
	margin-left: calc(var(--grid-x-spacing) * -1);
	&__cell {
		@extend %reset-ol-li;
		@extend %grid__cell;
		position: relative;
		border: var(--grid-x-spacing) solid transparent;
		border-width: 0 0 var(--grid-y-spacing) var(--grid-x-spacing);
		// hide the border in MS high contrast mode
		border-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3C/svg%3E");
	}

	// VARIANTs
	&--scroll {
		@extend %grid--scroll;
	}
	&--nowrap {
		flex-wrap: nowrap;
	}
	&--middle {
		align-items: center;
	}
	&--bottom {
		align-items: flex-end;
	}
	&--center {
		justify-content: center;
	}
	&--right {
		justify-content: flex-end;
	}
	&--space-between {
		justify-content: space-between;
	}

	// spacing
	&--x-0 {
		--grid-x-spacing: 0;
	}
	&--y-0 {
		--grid-y-spacing: 0;
	}
	&--x--1 {
		margin-left: 0;
		padding-left: 0.1rem;
		#{$s}__cell {
			margin-left: -0.1rem;
			border-left-width: 0;
		}
	}
	&--y--1 {
		margin-bottom: 0;
		padding-bottom: 0.1rem;
		#{$s}__cell {
			margin-bottom: -0.1rem;
			border-bottom-width: 0;
		}
	}

	@each $index, $value in variables.$utils-spacing {
		&--x-#{$index} {
			--grid-x-spacing: #{$value};
		}
		&--y-#{$index} {
			--grid-y-spacing: #{$value};
		}
	}

	@each $breakpoint, $value in config.$breakpoints {
		@include mixins.mq-checker($breakpoint) {
			@each $index, $value in variables.$utils-spacing {
				&--x-#{$index} {
					@include mixins.suffix($breakpoint) {
						--grid-x-spacing: #{$value};
					}
				}
				&--y-#{$index} {
					@include mixins.suffix($breakpoint) {
						--grid-y-spacing: #{$value};
					}
				}
			}
		}
	}
}
.size {
	@include mixins.generate-grid-size();
	// @include mixins.generate-grid-size(config.$breakpoints, 10);
}
// .pull {
// 	@include mixins.generate-grid-pull();
// }
// .push {
// 	@include mixins.generate-grid-push();
// 	&--auto {
// 		left: auto;
// 	}
// }
// .order {
// 	@include generate-grid-order();
// }
