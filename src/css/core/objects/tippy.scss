/* stylelint-disable declaration-no-important */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.tippy {
	&-box:first-child {
		max-width: 24rem;
		margin-top: -0.5rem;
	}
	&-content {
		padding: 1rem;
		border: 0.1rem solid variables.$colorBd;
		background: variables.$colorWhite;
		font-size: 1.2rem;
		line-height: calc(20 / 14);
		transition: opacity variables.$t, visibility variables.$t;
		box-shadow: 0 0 2rem rgba(#0c1627, 0.2);
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 1rem;
		}
	}

	// MODIf
	&-box[data-placement='right'] &-content,
	&-box[data-placement='right-start'] &-content {
		&::before,
		&::after {
			@include mixins.triangle('left', 1rem, 2rem, variables.$colorBd);
			right: 100%;
		}
		&::after {
			margin-right: -0.1rem;
			border-color: transparent variables.$colorWhite transparent transparent;
		}
	}
	&-box[data-placement='left'] &-content,
	&-box[data-placement='left-start'] &-content {
		&::before,
		&::after {
			@include mixins.triangle('right', 1rem, 2rem, variables.$colorBd);
			left: 100%;
		}
		&::after {
			margin-left: -0.1rem;
			border-color: transparent transparent transparent variables.$colorWhite;
		}
	}
	&-box[data-placement='bottom'] &-content {
		&::before,
		&::after {
			@include mixins.triangle('up', 2rem, 1rem, variables.$colorBd);
			top: -1rem;
			left: 50%;
			margin-left: -1rem;
		}
		&::after {
			margin-top: 0.1rem;
			border-color: transparent transparent variables.$colorWhite transparent;
		}
	}

	// STATES
	[aria-expanded='false'] + [data-tippy-root] &-content {
		opacity: 0;
		visibility: hidden;
	}

	// MQ
	@media (config.$md-up) {
		&-box {
			max-width: 40rem;
		}
		&-content {
			padding: 1.5rem 2rem;
		}
	}
}
