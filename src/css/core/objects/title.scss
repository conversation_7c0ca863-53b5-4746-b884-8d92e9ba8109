@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.title {
	--offset: 1rem;
	$s: &;
	display: flex;
	flex-wrap: wrap;
	margin: 0 0 calc(40em / 44) 0;
	padding-left: var(--offset);
	font-family: variables.$fontSecondary;
	font-size: 3rem;
	line-height: calc(60 / 44);
	letter-spacing: 0.04em;
	text-transform: uppercase;
	&__item {
		display: inline-block;
		margin-left: calc(var(--offset) * -1);
		padding: 0 var(--offset);
		background: variables.$colorPrimary;
		color: variables.$colorWhite;
	}

	// MODIF
	&--darken &__item {
		background: darken(variables.$colorPrimary, 10%);
	}
	&--secondary &__item {
		background: variables.$colorSecondary;
	}
	&--secondary-darken &__item {
		background: darken(variables.$colorSecondary, 10%);
	}
	&--xs {
		font-size: 1.4rem;
		text-transform: none;
		#{$s}__item {
			padding: 0.6rem 1rem 0.5rem;
		}
	}
	&--sm {
		font-size: 2.4rem;
		#{$s}__item {
			padding: 0.2rem 1rem 0.1rem;
		}
	}
	&--md {
		font-size: 3.4rem;
		line-height: calc(45 / 34);
	}

	// MQ
	@media (config.$md-up) {
		--offset: 2rem;
		font-size: 4.4rem;
		&--xs {
			--offset: 1rem;
			font-size: 1.4rem;
			text-transform: none;
			#{$s}__item {
				padding: 0.6rem 1rem 0.5rem;
			}
		}
		&--sm {
			--offset: 1rem;
			font-size: 2.4rem;
			#{$s}__item {
				padding: 0.2rem 1rem 0.1rem;
			}
		}
		&--md {
			--offset: 1rem;
			font-size: 3.4rem;
			line-height: calc(45 / 34);
		}
	}
}
