@use 'base/variables';

%inp {
	display: block;
	width: 100%;
	height: 5rem;
	padding: 1.4rem 1.9rem 1.5rem;
	border: 0.1rem solid #c8d0d0;
	border-radius: 0.2rem;
	background: variables.$colorWhite;
	color: variables.$colorText;
	font-size: 1.4rem;
	line-height: normal;
	appearance: none;
	transition: border-color variables.$t;

	// hide number arrows
	&::-webkit-outer-spin-button,
	&::-webkit-inner-spin-button {
		margin: 0;
		-webkit-appearance: none;
	}
	&[type='number'] {
		-moz-appearance: textfield;
	}

	// STATEs
	&::placeholder {
		color: variables.$colorGray;
		opacity: 1;
		transition: color variables.$t;
	}
	&[disabled],
	&:disabled {
		background: variables.$colorBd;
		opacity: 0.8;
		cursor: not-allowed;
	}
	&[placeholder] {
		text-overflow: ellipsis;
	}
	&:focus {
		&::placeholder {
			color: rgba(variables.$colorGray, 0.5);
		}
	}
	&:focus,
	&.active {
		border-color: variables.$colorSecondary;
	}
}
