@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.flatpickr {
	&-calendar {
		position: absolute;
		max-width: 41.4rem;
		margin-top: -0.3rem;
		padding: 2rem;
		border: 0.1rem solid variables.$colorSecondary;
		background: variables.$colorWhite;
		outline: 0;
		opacity: 0;
		visibility: hidden;
		transition: opacity variables.$t, visibility variables.$t;
		.numInput {
			-moz-appearance: textfield;
			&::-webkit-outer-spin-button,
			&::-webkit-inner-spin-button {
				-webkit-appearance: none;
			}
		}
		&.open {
			z-index: 10;
			opacity: 1;
			visibility: visible;
		}
	}
	&-months {
		position: relative;
	}
	&-monthDropdown-months {
		border: none;
		font-size: inherit;
		text-align: center;
		appearance: none;
	}
	&-month {
		position: relative;
		margin-bottom: 1rem;
		padding: 0 3rem;
		user-select: none;
		-webkit-tap-highlight-color: rgba(variables.$colorWhite, 0);
	}
	&-prev-month,
	&-next-month {
		position: absolute;
		top: 0;
		z-index: 1;
		display: block;
		width: 3rem;
		height: 4.6rem;
		border: 0.1rem solid variables.$colorBd;
		color: variables.$colorSecondary;
		text-align: center;
		cursor: pointer;
		transition: border-color variables.$t;
		.hoverevents &:hover,
		.hoverevents &:focus {
			border-color: variables.$colorSecondary;
		}
		.icon-svg {
			position: absolute;
			top: 50%;
			left: 50%;
			margin: -0.6rem 0 0 -0.3rem;
			pointer-events: none;
		}
	}
	&-prev-month {
		left: 0;
	}
	&-next-month {
		right: 0;
	}
	&-current-month {
		display: flex;
		gap: 0.5rem;
		justify-content: center;
		padding: 1.2rem 0 1.3rem;
		font-family: variables.$fontSecondary;
		font-size: 1.8rem;
		line-height: 1;
		letter-spacing: 0.01em;
		text-align: center;
		.numInputWrapper {
			display: inline-block;
		}
		.numInput {
			width: 4.5rem;
			padding: 0;
			border: 0;
			font-family: inherit;
			font-size: inherit;
			line-height: inherit;
		}
	}
	&-weekdaycontainer,
	&-rContainer .dayContainer {
		@include mixins.inline-list(calc(100% / 7), variables.$fontSize);
		text-align: center;
	}
	&-days {
		border: 0.1rem solid variables.$colorBd;
	}
	&-weekdays {
		border: 0.1rem solid transparent;
	}
	&-day,
	&-weekday {
		padding: 0.7rem;
		letter-spacing: 0.08em;
	}
	&-weekday {
		border: 0.1rem solid transparent;
		font-weight: bold;
	}
	&-day {
		border: 0.1rem solid variables.$colorBd;
		outline: 0;
		cursor: pointer;
		transition: background-color variables.$t, border-color variables.$t, color variables.$t;
		.hoverevents &:hover,
		.hoverevents &:focus {
			border-color: variables.$colorSecondary;
			color: variables.$colorSecondary;
		}
		&.selected {
			border-color: variables.$colorSecondary;
			background: variables.$colorSecondary;
			color: variables.$colorWhite;
		}
		&.prevMonthDay,
		&.nextMonthDay,
		&.flatpickr-disabled {
			color: variables.$colorBdDarken;
			pointer-events: none;
		}
	}
	&-time {
		@include mixins.inline-list(auto, variables.$fontSize);
		padding-top: 2rem;
		outline: 0;
		text-align: center;
		user-select: none;
		-webkit-tap-highlight-color: rgba(variables.$colorWhite, 0);
		& > * {
			vertical-align: middle;
		}
		.numInputWrapper {
			position: relative;
			width: 5rem;
			padding: 5.7rem 0 2.9rem;
		}
		.numInput {
			width: 100%;
			padding: 1.1rem 0.5rem;
			border: 0.1rem solid variables.$colorBd;
			border-radius: 0;
			font-size: 1.4rem;
			letter-spacing: 0.08em;
			text-align: center;
			appearance: none;
		}
		.label {
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			font-weight: bold;
			line-height: 1;
			letter-spacing: 0.08em;
		}

		.arrowUp,
		.arrowDown {
			position: absolute;
			right: 0;
			left: 0;
			height: 3rem;
			border: 0.1rem solid variables.$colorBd;
			cursor: pointer;
			transition: border-color variables.$t;
			.hoverevents &:hover,
			.hoverevents &:focus {
				border-color: variables.$colorSecondary;
			}
			.icon-svg {
				position: absolute;
				top: 50%;
				left: 50%;
				margin: -0.3rem 0 0 -0.6rem;
				color: variables.$colorSecondary;
				pointer-events: none;
			}
		}
		.arrowUp {
			top: 2.8rem;
		}
		.arrowDown {
			bottom: 0;
		}
		&-separator {
			width: 2.8rem;
			padding-top: 2.8rem;
			font-weight: bold;
			line-height: 1;
		}
	}

	// MQ
	@media (config.$md-down) {
		&-calendar {
			width: calc(100% - 4rem);
			max-width: 36rem;
		}
	}

	@media (config.$md-up) {
		&-calendar {
			padding: 3rem;
		}
		&-time {
			position: absolute;
			top: -0.1rem;
			bottom: -0.1rem;
			left: 100%;
			width: 19rem;
			padding: 101px 30px 3rem;
			border: 0.1rem solid variables.$colorSecondary;
			background: variables.$colorWhite;
		}
	}
}
