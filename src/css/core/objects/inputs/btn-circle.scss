@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.btn-circle {
	@include mixins.button-reset;
	align-content: center;
	width: 3.6rem;
	height: 3.6rem;
	border-radius: 50%;
	background: variables.$colorSecondary;
	color: variables.$colorWhite;
	font-size: 0;
	text-align: center;
	text-decoration: none;
	cursor: pointer;
	transition: background-color variables.$t;
	.icon-svg {
		width: 1.4rem;
	}

	// HOVERS
	.hoverevents &:hover,
	.hoverevents &:focus {
		background: variables.$colorSecondaryDarken;
		color: variables.$colorWhite;
	}
}
