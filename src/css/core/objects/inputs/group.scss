/* stylelint-disable declaration-no-important */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.inp-group {
	$s: &;
	position: relative;
	z-index: 5;
	&__list {
		@extend %reset-ul;
		.js & {
			position: absolute;
			top: 100%;
			right: 0;
			left: 0;
			display: none;
			max-height: 20rem;
			margin-top: -0.1rem;
			border: 0.1rem solid variables.$colorSecondary;
			background: variables.$colorWhite;
			overflow-x: hidden;
			overflow-y: auto;
		}
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__item-label {
		width: 100%;
		padding: 1rem 2rem 1rem 6.3rem;
		color: variables.$colorText;
		transition: color variables.$t, background-color variables.$t;
		.inp-item__text {
			color: inherit;
			font-family: variables.$font;
			letter-spacing: normal;
			text-transform: none;
			transition: none;
			&::before {
				top: 50%;
				left: 2rem;
				transform: translateY(-50%);
			}
			&::after {
				top: 50%;
				left: 3rem;
				transform: translateY(-50%) rotate(45deg);
			}
		}
	}
	&__inp {
		text-overflow: ellipsis;
		overflow: hidden;
	}
	&__link {
		color: inherit;
		text-decoration: none;
		transition: none;
	}

	// HOVERS
	.hoverevents &__item-label:hover {
		background: variables.$colorSecondary;
		color: variables.$colorWhite;
	}
	.hoverevents &__item-label:hover &__link {
		color: inherit;
	}

	// STATES
	&.is-open &__inp {
		border-color: variables.$colorSecondary;
	}
	&.is-open &__list {
		display: block;
	}
	&__item:has([disabled]) {
		pointer-events: none;
	}
	&__label:not(.is-active):not(:placeholder-shown) {
		transform: none !important;
	}
	&__label:not(.is-active) + &__inp {
		color: transparent;
	}
	.no-js & {
		height: 100%;
		#{$s} {
			&__list {
				display: flex;
				align-items: center;
				height: 100%;
			}
			&__label,
			&__inp {
				display: none;
			}
		}
	}
}
