@use 'config';
@use 'base/variables';
@use 'base/mixins';
@use 'core/objects/inputs/extends';

.inp {
	$s: &;
	&__text {
		@extend %inp;
		color: variables.$colorText;
		font-family: inherit;
		letter-spacing: 0.02em;
		&--sm {
			height: 4rem;
			padding: 0.9rem 1.5rem 1rem;
		}
		&--lg {
			height: 6.4rem;
			padding: 2rem 2.9rem;
			font-size: 1.6rem;
		}
		&--blank {
			border-color: rgba(variables.$colorWhite, 0.4);
			background: none;
			color: variables.$colorWhite;
			&::placeholder {
				color: inherit;
			}
			&:focus {
				border-color: variables.$colorWhite;
				outline-color: variables.$colorWhite;
				&::placeholder {
					color: rgba(variables.$colorWhite, 0.5);
				}
			}
		}
		&--bold {
			color: variables.$colorText;
			font-family: variables.$fontSecondary;
			letter-spacing: 0.08em;
			text-transform: uppercase;
			&::placeholder {
				color: inherit;
			}
			&:focus {
				&::placeholder {
					color: rgba(variables.$colorText, 0.5);
				}
			}
		}
		&--flat {
			border-radius: 0;
		}
		&[type='date'] {
			&::-webkit-calendar-picker-indicator {
				visibility: hidden;
			}
		}
		@at-root textarea#{&} {
			height: auto;
		}
	}
	&__label {
		display: inline-block;
		&.is-disabled {
			opacity: 0.8;
		}
		&--inside {
			@include mixins.text-overflow();
			position: absolute;
			top: 0;
			left: 0;
			z-index: 2;
			width: 100%;
			height: 100%;
			padding: 1.5rem 1.5rem 1.6rem 2rem;
			color: variables.$colorGray;
			font-size: 1.4rem;
			line-height: normal;
			text-align: left;
			pointer-events: none;
			transition: transform 0.2s ease-out;
			backface-visibility: hidden;
			transform-origin: 0 0;
		}
		&--inside.is-active,
		&--inside:has(~ #{$s}__text:focus),
		&--inside:has(~ #{$s}__text:not([type='hidden']):not(:placeholder-shown)) {
			transform: translate(0.2rem, -0.7rem) scale(0.9);
		}
	}
	&__label--inside ~ &__text,
	&__label--inside ~ .inp-select,
	&__label--inside ~ .choices .choices__list--single .choices__item {
		padding-top: 2.3rem;
		padding-bottom: 0.6rem;
		&::placeholder {
			color: transparent;
		}
	}
	&__fix {
		position: relative;
		display: block;
		width: 100%;
		&--btn-icon #{$s}__text {
			padding-right: 6.4rem;
		}
	}
	&__icon {
		position: absolute;
		top: 1.5rem;
		right: 1.6rem;
		left: auto;
		z-index: 1;
		color: variables.$colorRockBlue;
		opacity: 0;
		visibility: hidden;
		pointer-events: none;
		transition: opacity variables.$t, visibility variables.$t;
	}
	&:has(&__icon) &__text {
		padding-right: 4.8rem;
	}
	&__btn {
		display: block;
		white-space: nowrap;
	}
	&__btn-icon {
		position: absolute;
		top: 0;
		right: 0;
	}
	&__error {
		color: variables.$colorPrimary;
		line-height: calc(20 / 14);
	}
	&__hint {
		position: absolute;
		top: 50%;
		left: calc(100% + 1rem);
		transform: translateY(-50%);
	}

	// MODIF
	&--group--m {
		display: flex;
		width: 100%;
		#{$s} {
			&__fix {
				flex: 0 1 auto;
			}
			&__fix:not(:first-of-type) .choices__list--single .choices__item,
			&__fix:not(:first-of-type) > .inp-select,
			&__fix:not(:first-of-type) > #{$s}__text {
				border-left: 0;
				border-top-left-radius: 0;
				border-bottom-left-radius: 0;
			}
			&__fix:not(:last-of-type) .choices__list--single .choices__item,
			&__fix:not(:last-of-type) > .inp-select,
			&__fix:not(:last-of-type) > #{$s}__text {
				border-right: 0;
				border-top-right-radius: 0;
				border-bottom-right-radius: 0;
			}
		}
	}

	// STATES
	&__fix:has([data-controller='datepicker']) &__icon {
		opacity: 1;
		visibility: visible;
	}
	.has-error &__text,
	&__text.has-error {
		border-color: variables.$colorPrimary;
	}
	.has-error &__icon,
	&__text.has-error ~ &__icon {
		color: variables.$colorPrimary;
		opacity: 1;
		visibility: visible;
	}
	.has-error &__label--inside,
	&__text.has-error ~ &__label--inside {
		padding-right: 2.8rem;
	}

	// MQ
	@media (config.$md-down) {
		&__text--lg {
			padding-right: 2rem;
			padding-left: 2rem;
		}

		&--hint {
			padding-right: 3.5rem;
		}
	}
	@media (config.$sm-down) {
		&--group:not(&--group--m),
		&--group-spaced:not(&--group--m) {
			#{$s}__btn {
				margin-top: 1rem;
			}
		}

		&--multiple {
			.inp-item {
				margin: 0.5rem 0 1rem;
			}
			#{$s}__fix {
				margin-bottom: 0.5rem;
			}
			& > *:last-child {
				margin-bottom: 0;
			}
		}
	}

	@media (config.$sm-up) {
		&--group,
		&--multiple {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			width: 100%;
			#{$s} {
				&__fix {
					flex: 1;
					min-width: 20rem;
				}
				&__fix:not(:first-of-type) .choices__list--single .choices__item,
				&__fix:not(:first-of-type) > .inp-select,
				&__fix:not(:first-of-type) > #{$s}__text {
					border-top-left-radius: 0;
					border-bottom-left-radius: 0;
				}
				&__fix:not(:last-of-type) .choices__list--single .choices__item,
				&__fix:not(:last-of-type) > .inp-select,
				&__fix:not(:last-of-type) > #{$s}__text {
					border-top-right-radius: 0;
					border-bottom-right-radius: 0;
				}
			}
		}

		&--group {
			#{$s} {
				&__fix:not(:first-of-type) .choices__list--single .choices__item,
				&__fix:not(:first-of-type) > .inp-select,
				&__fix:not(:first-of-type) > #{$s}__text {
					border-left: 0;
				}
				&__fix:not(:last-of-type) .choices__list--single .choices__item,
				&__fix:not(:last-of-type) > .inp-select,
				&__fix:not(:last-of-type) > #{$s}__text {
					border-right: 0;
				}
			}
		}

		&--group-spaced {
			display: flex;
			gap: 1rem;
			min-width: 100%;
			#{$s}__fix {
				flex: 0 1 auto;
			}
		}

		&--multiple {
			flex-wrap: wrap;
			margin: 0 0 -0.5rem 0.1rem;
			#{$s} {
				&__fix {
					width: auto;
					margin: 0 0 0.5rem -0.1rem;
				}
				&__fix .choices.is-focused .choices__list--single .choices__item {
					position: relative;
					z-index: 1;
				}
				&__fix > .inp-select,
				&__fix > #{$s}__text {
					&:focus,
					&.active {
						position: relative;
						z-index: 1;
					}
				}
			}
			&-btn {
				width: auto;
			}
		}
	}
}
