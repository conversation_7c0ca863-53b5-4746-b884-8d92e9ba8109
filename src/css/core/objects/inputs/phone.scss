@use 'base/variables';
@use 'core/objects/inputs/extends';

.inp__label--inside:has(+ .js-phone-input__wrapper) {
	left: 14.6rem;
	transform: translate(0.2rem, -0.7rem) scale(0.9);
}
.js-phone-input {
	$input-padding-horizontal: 1.6rem;
	$input-padding-vertical: 2rem;
	$flag-padding-horizontal: 2rem;
	$flag-padding-vertical: 1.6rem;
	&__base {
		position: absolute;
		left: 0;
		opacity: 0;
		pointer-events: none;
	}
	&__wrapper {
		position: relative;
		z-index: 0;
		display: flex;
		flex-direction: row-reverse;
		font-size: 1.5rem;
		line-height: variables.$lineHeight;
		letter-spacing: -0.01em;
	}
	&__flag {
		display: flex;
		flex: 0 0 auto;
		align-items: center;
		width: 13.5rem;
		padding: 0 4rem 0 5rem;
		border: 0.1rem solid variables.$colorBdDarken;
		background-image: url(variables.$imgPath + 'bg/select.svg');
		background-position: top 50% right 1.8rem;
		background-repeat: no-repeat;
		pointer-events: none;
		img {
			position: absolute;
			top: 50%;
			left: 1.8rem;
			width: 2rem;
			height: auto;
			transform: translateY(-50%);
		}
		.has-error &,
		.validated & {
			border-color: variables.$colorBd;
		}
	}
	&__input {
		padding-top: 2.3rem;
		padding-bottom: 0.6rem;
		background-color: transparent;
		text-shadow: 0 0.1rem 0.1rem variables.$colorWhite, 0.1rem 0 0.1rem variables.$colorWhite, 0 -0.1rem 0.1rem variables.$colorWhite,
			-0.1rem 0 0.1rem variables.$colorWhite, -0.1rem -0.1rem 0.1rem variables.$colorWhite, 0.1rem 0.1rem 0.1rem variables.$colorWhite,
			0.1rem -0.1rem 0.1rem variables.$colorWhite, -0.1rem 0.1rem 0.1rem variables.$colorWhite;
		font-variant-numeric: tabular-nums;
		.validated &,
		.validated &:focus {
			border-color: variables.$colorGreen;
		}
	}
	&__placeholder {
		position: relative;
		top: 0.9rem;
		left: 0.2rem;
		z-index: -1;
		display: flex;
		align-items: center;
		width: 0;
		margin: 0 -1.7rem 0 2.8rem;
		color: rgba(variables.$colorText, 0.5);
		font-size: variables.$fontSize;
		line-height: variables.$lineHeight;
		letter-spacing: 0.02em;
		white-space: nowrap;
		pointer-events: none;
		font-variant-numeric: tabular-nums;
	}
	&__select {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: -1;
		padding: 0 $flag-padding-horizontal;
		border: none;
		background-color: transparent;
		color: transparent;
		appearance: none;
		option {
			color: variables.$colorText;
		}
		&:focus {
			outline: 0;
		}
	}
}
