@use 'config';
@use 'base/variables';
@use 'base/mixins';
@use 'core/objects/inputs/extends';

.inp-select,
.inp-group__inp,
.choices__list--single .choices__item {
	@extend %inp;
	@include mixins.text-overflow();
	padding-right: 4.7rem;
	background-image: url(variables.$imgPath + 'bg/select.svg');
	background-position: top 50% right 1.8rem;
	background-repeat: no-repeat;
	color: variables.$colorText;
	font-family: variables.$fontSecondary;
	letter-spacing: 0.08em;
	text-align: left;
	text-transform: uppercase;
	cursor: pointer;
	&.choices__input {
		display: none;
	}

	// STATES
	.has-error &,
	&.has-error & {
		border-color: variables.$colorRed;
		background-position: top 50% right 4.8rem;
	}
}
.js .inp-select[data-controller='choices'] {
	color: transparent;
}
.choices {
	--inp-h: 5rem;
	position: relative;
	outline: 0;

	// STATES
	&:has(.is-loading) {
		opacity: 0.5;
		pointer-events: none;
		&::before {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: calc(var(--inp-h) / 3);
			height: calc(var(--inp-h) / 3);
			margin: calc(var(--inp-h) / -6) 0 0 calc(var(--inp-h) / -6);
			border: 0.2rem solid variables.$colorText;
			border-top-color: transparent;
			border-radius: 50%;
			animation: animation-rotate 0.8s infinite linear;
		}
	}
	&.is-open {
		z-index: 6;
	}
	&.is-open &__list--dropdown {
		opacity: 1;
		visibility: visible;
		pointer-events: auto;
	}
	&.is-focused &__list--single &__item {
		border-color: variables.$colorSecondary;
	}
	// .has-error &__list--single &__item,
	// &.has-error &__list--single &__item {
	// 	border-color: variables.$colorRed;
	// 	background-position: top 50% right 4.8rem;
	// }

	// MODIF
	&__list--single &__item {
		line-height: 2rem;
	}
	&__list--single &__item#{&}__placeholder {
		color: transparent;
	}
	&__list--dropdown {
		position: absolute;
		top: 100%;
		left: 0;
		z-index: 3;
		width: 100%;
		max-height: 20rem;
		margin-top: -0.1rem;
		border: 0.1rem solid variables.$colorSecondary;
		background: variables.$colorWhite;
		overflow-y: auto;
		opacity: 0;
		visibility: hidden;
		pointer-events: none;
		transition: opacity variables.$t, visibility variables.$t;
	}
	&__list--dropdown &__item {
		padding: 1rem 2rem;
		line-height: calc(20 / 14);
		text-align: left;
		cursor: pointer;
		transition: background-color variables.$t, color variables.$t;

		.hoverevents &:not(.choices__item--disabled):hover,
		.hoverevents &:not(.choices__item--disabled):focus,
		&:not(.choices__item--disabled).is-highlighted {
			background: variables.$colorSecondary;
			color: variables.$colorWhite;
		}
		&--disabled {
			color: variables.$colorGray;
			cursor: default;
		}
	}
	&__group &__heading {
		padding: 1rem 2rem;
		color: variables.$colorGray;
		line-height: calc(20 / 14);
		&:empty {
			display: none;
		}
	}
	&__group:has(&__heading:not(:empty)) ~ &__item {
		padding-left: 4rem;
	}

	&__list--dropdown &__placeholder {
		display: none;
	}

	// MQ
	@media (config.$md-up) {
		&__list--dropdown {
			min-width: 16rem;
		}
	}
	@media (config.$xl-up) {
		&__list--dropdown {
			min-width: 30rem;
		}
	}
}
