@use 'config';
@use 'base/variables';
@use 'base/extends';
@use 'base/mixins';

.inp-item {
	position: relative;
	display: inline-block;
	vertical-align: top;
	padding-top: 0.15em;
	padding-left: 3.3rem;
	font-size: 1.4rem;
	cursor: pointer;
	-webkit-tap-highlight-color: transparent;
	&__inp {
		position: absolute;
		left: -500rem;
	}
	&__text {
		display: block;
		min-height: 2.5rem;
		color: variables.$colorSecondary;
		font-family: variables.$fontSecondary;
		letter-spacing: 0.08em;
		text-transform: uppercase;
		transition: color variables.$t;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 2.5rem;
			height: 2.5rem;
			border: 0.1rem solid #95989a;
			border-radius: 0.2rem;
			background: #ffffff;
			transition: border-color variables.$t, background-color variables.$t;
		}
		&::after {
			content: '';
			position: absolute;
			opacity: 0;
			transition: opacity variables.$t;
		}
		a,
		a:not([class]):visited {
			color: inherit;
		}
	}
	&__icon {
		top: -0.2rem;
		margin-right: 0.3rem;
	}

	// VARIANTS
	&--checkbox &__text {
		&::after {
			top: 0.6rem;
			left: 1rem;
			display: inline-block;
			width: 0.5rem;
			height: 1rem;
			border-right: 0.2rem solid variables.$colorSecondary;
			border-bottom: 0.2rem solid variables.$colorSecondary;
			transform: rotate(45deg);
		}
	}
	&--radio &__text {
		&::before,
		&::after {
			border-radius: 50%;
		}
		&::after {
			top: 0.7rem;
			left: 0.7rem;
			width: 1.1rem;
			height: 1.1rem;
			background: variables.$colorSecondary;
		}
	}

	// focus
	&__inp:focus + &__text {
		&::before {
			border-color: variables.$colorText;
		}
	}
	// checked
	&__inp:checked + &__text {
		color: inherit;
		&::after {
			opacity: 1;
		}
	}
	// disabled
	&__inp:disabled + &__text {
		color: variables.$colorGray;
		cursor: default;
		&::before {
			border-color: variables.$colorBd;
			border-color: #95989a;
			background: #e8eaea;
		}
	}
	&--radio &__inp:disabled + &__text {
		&::after {
			background: #6f7a86;
		}
	}
	// error
	.has-error &__text {
		color: variables.$colorRed;
		&::before {
			border-color: variables.$colorRed;
		}
	}
	.has-error &--checkbox &__inp:checked + &__text,
	&--checkbox.has-error &__inp:checked + &__text {
		color: variables.$colorRed;
		&::after {
			border-color: variables.$colorRed;
		}
	}
	.has-error &--radio &__inp:checked + &__text,
	&--radio.has-error &__inp:checked + &__text {
		color: variables.$colorRed;
		&::after {
			background: variables.$colorRed;
		}
	}
}
