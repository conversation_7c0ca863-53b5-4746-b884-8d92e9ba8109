@use 'config';
@use 'base/variables';

.switch {
	position: relative;
	display: flex;
	align-items: center;
	user-select: none;
	&__inp {
		position: absolute;
		left: -500rem;
	}
	&__label {
		margin-right: auto;
		padding-right: 2rem;
		color: rgba(variables.$colorWhite, 0.6);
	}
	&__inner {
		position: relative;
		display: flex;
		border-radius: 3rem;
		background: rgba(97, 98, 100, 0.8);
		cursor: pointer;
		transition: background-color variables.$t;
	}
	&__text {
		display: flex;
		flex: 1 1 auto;
		justify-content: center;
		align-items: center;
		width: 2.1rem;
		height: 2.3rem;
		text-align: center;
		opacity: 0;
		transition: opacity variables.$t;
		.icon-svg {
			position: relative;
			z-index: 1;
			svg {
				fill: currentcolor;
			}
		}
	}
	&__tool {
		position: absolute;
		top: 0.2rem;
		bottom: 0.2rem;
		left: 0.2rem;
		width: 1.9rem;
		border-radius: 50%;
		background: rgba(247, 247, 247, 0.8);
		transition: transform variables.$t;
	}

	// MODIF
	&__text--right {
		opacity: 1;
	}

	// STATES
	&__inp:focus + &__inner {
		border-color: variables.$colorText;
	}
	&__inp:checked + &__inner {
		background-color: rgba(0, 148, 0, 1);
	}

	&__inp:checked + &__inner &__tool {
		transform: translateX(100%);
	}
	&__inp:checked + &__inner &__text--left {
		opacity: 1;
	}
	&__inp:checked + &__inner &__text--right {
		opacity: 0;
	}
	&__inp:disabled + &__inner {
		opacity: 0.5;
		pointer-events: none;
	}
}
