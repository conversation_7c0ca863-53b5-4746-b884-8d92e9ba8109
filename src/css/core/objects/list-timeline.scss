@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.list-timeline {
	$s: &;
	@extend %reset-ul;
	line-height: calc(22 / 14);
	&__item {
		@extend %reset-ul-li;
		position: relative;
		padding: 0.6rem 0 1rem 3rem;
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 0.9rem;
		}
		&::before {
			bottom: 0;
			left: 0.5rem;
			width: 0.1rem;
			background: variables.$colorBd;
		}
		&::after {
			left: -0.2rem;
			width: 1.5rem;
			height: 1.5rem;
			border: 0.4rem solid variables.$colorWhite;
			border-radius: 0.5rem;
			background: variables.$colorSecondary;
		}
		&:last-child::before {
			display: none;
		}
		& > * {
			margin-bottom: 0;
		}
	}
	&__date {
		margin-bottom: 0.5rem;
		color: variables.$colorGray;
		font-size: 1.2rem;
		letter-spacing: 0.08em;
		&:first-child {
			margin-top: 0.1rem;
		}
	}
}
