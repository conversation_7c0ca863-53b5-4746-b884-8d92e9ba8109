@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.date {
	display: block;
	width: 8rem;
	padding: 0.8rem 0.5rem;
	border: 0.6rem solid #ededed;
	background-color: variables.$colorWhite;
	color: variables.$colorText;
	font-family: variables.$fontSecondary;
	letter-spacing: normal;
	text-align: center;
	box-shadow: 0 0 0 0.1rem variables.$colorBdDarken;
	&__day {
		display: block;
		margin-bottom: 0.5rem;
		font-size: 3.2rem;
		line-height: calc(50 / 44);
	}
	&__month,
	&__year {
		display: block;
		font-size: 10px;
		line-height: calc(16 / 12);
		letter-spacing: 0.08em;
		text-transform: uppercase;
	}

	// MQ
	@media (config.$md-up) {
		width: 10rem;
		padding: 1rem;
		&__day {
			font-size: 4.4rem;
		}
		&__month,
		&__year {
			font-size: 1.2rem;
		}
	}
}
