@use 'base/variables';

@font-face {
	font-family: 'Open Sans';
	font-weight: 300;
	font-style: normal;
	src: url(variables.$fontsPath + 'open-sans-light.woff2') format('woff2');
	font-display: fallback;
}
@font-face {
	font-family: 'Open Sans';
	font-weight: normal;
	font-style: normal;
	src: url(variables.$fontsPath + 'open-sans-regular.woff2') format('woff2');
	font-display: fallback;
}
@font-face {
	font-family: 'Open Sans';
	font-weight: 700;
	font-style: normal;
	src: url(variables.$fontsPath + 'open-sans-bold.woff2') format('woff2');
	font-display: fallback;
}
@font-face {
	font-family: Vafle;
	font-weight: normal;
	font-style: normal;
	src: url(variables.$fontsPath + 'vafle-regular.woff2') format('woff2');
	font-display: fallback;
}
