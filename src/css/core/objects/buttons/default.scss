@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.inline-btns {
	display: flex;
	flex-wrap: wrap;
	gap: 1rem;
}

.btn {
	--btn-fs: 1.4rem;
	--btn-bg: #{variables.$colorPrimary};
	--btn-c: #{variables.$colorWhite};
	--btn-bdc: #{variables.$colorPrimary};
	--btn-hover-bg: #{variables.$colorPrimaryDarken};
	--btn-hover-c: #{variables.$colorWhite};
	--btn-hover-bdc: var(--btn-hover-bg);
	--btn-icon-size: 1.8rem;
	--btn-icon-position: 2rem;
	--btn-padding-x: 4rem;
	--btn-padding-y: 1.5rem;
	--btn-h: 5rem;
	$s: &;
	display: inline-block;
	vertical-align: middle;
	padding: 0;
	border: 0;
	background: none;
	text-decoration: none;
	cursor: pointer;
	&__text {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: var(--btn-h);
		padding: var(--btn-padding-y) var(--btn-padding-x);
		border: 0.2rem solid var(--btn-bdc);
		border-radius: 0.1rem;
		background-color: var(--btn-bg);
		color: var(--btn-c);
		font-family: variables.$fontSecondary;
		font-size: var(--btn-fs);
		line-height: calc(16 / 14);
		letter-spacing: 0.08em;
		text-align: center;
		text-transform: uppercase;
		text-decoration: none;
		transition: background-color variables.$t, border-color variables.$t, color variables.$t;
	}
	&__icon {
		position: absolute;
		top: 50%;
		width: var(--btn-icon-size);
		height: var(--btn-icon-size);
		transform: translateY(-50%);
	}

	// VARIANTs
	// icons
	&--icon-l:has(&__icon) {
		#{$s}__icon {
			left: var(--btn-icon-position);
		}
		#{$s}__text {
			padding-left: 5rem;
		}
	}
	&--icon-r:has(&__icon) {
		#{$s}__icon {
			right: var(--btn-icon-position);
		}
		#{$s}__text {
			padding-right: 5rem;
		}
	}
	&--icon-only {
		--btn-fs: 0;
		#{$s}__icon {
			position: relative;
			transform: none;
		}
	}
	// sizes
	&--block {
		display: block;
		min-width: 100%;
	}
	&--wide {
		--btn-padding-x: 6rem;
	}
	&--lg {
		--btn-h: 6.8rem;
		--btn-padding-y: 2.4rem;
	}
	&--lg#{&}--icon-only {
		--btn-h: 6.4rem;
		--btn-padding-x: 2.2rem;
		--btn-padding-y: 2.1rem;
	}
	&--sm {
		--btn-h: 4rem;
		--btn-padding-y: 1rem;
		--btn-padding-x: 2rem;
	}
	&--xs {
		--btn-h: 4rem;
		--btn-fs: 1.2rem;
		--btn-padding-y: 1rem;
		--btn-padding-x: 1.5rem;
	}
	// other
	&--loader &__text::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: calc(var(--btn-h) / 3);
		height: calc(var(--btn-h) / 3);
		margin: calc(var(--btn-h) / -6) 0 0 calc(var(--btn-h) / -6);
		border: 0.2rem solid var(--btn-c);
		border-top-color: transparent;
		border-radius: 50%;
		opacity: 0;
		visibility: hidden;
		transition: opacity variables.$t, visibility variables.$t;
	}
	// colors
	&--secondary {
		--btn-bg: #{variables.$colorSecondary};
		--btn-bdc: #{variables.$colorSecondary};
		--btn-hover-bg: #{variables.$colorSecondaryDarken};
	}
	&--white {
		--btn-bg: #{variables.$colorWhite};
		--btn-c: #{variables.$colorSecondary};
		--btn-bdc: transparent;
		--btn-hover-bg: #{darken(variables.$colorWhite, 20%)};
		--btn-hover-c: #{variables.$colorSecondary};
	}
	&--blank {
		--btn-bg: transparent;
		--btn-bdc: transparent;
		--btn-c: #{variables.$colorText};
		--btn-hover-bg: transparent;
		--btn-hover-c: #{variables.$colorSecondary};
	}
	&--twitter {
		--btn-bdc: #{variables.$colorBlack};
		--btn-bg: #{variables.$colorBlack};
		--btn-c: #{variables.$colorWhite};
		--btn-hover-bg: #{lighten(variables.$colorBlack, 20%)};
	}
	// outline
	&--outline {
		--btn-c: var(--btn-bdc);
		--btn-hover-bg: var(--btn-bdc);
		--btn-hover-bdc: var(--btn-bdc);
		--btn-bg: transparent;
	}
	&--white#{&}--outline {
		--btn-bdc: #{variables.$colorWhite};
		--btn-c: #{variables.$colorWhite};
		--btn-hover-bdc: #{variables.$colorWhite};
		--btn-hover-bg: #{variables.$colorWhite};
	}

	// STATEs
	&:disabled,
	&.is-disabled {
		opacity: 0.5;
		pointer-events: none;
	}
	.is-loading &--loader,
	.hoverevents .is-loading &--loader:hover,
	&--loader.is-loading,
	.hoverevents &--loader:hover.is-loading {
		position: relative;
		pointer-events: none;
		#{$s}__text {
			background: var(--btn-bg);
			color: transparent;
			.icon-svg {
				color: transparent;
			}
			&::before {
				opacity: 1;
				visibility: visible;
				animation: animation-rotate 0.8s infinite linear;
			}
		}
	}

	// HOVERs
	.hoverevents &:hover &__text {
		border-color: var(--btn-hover-bdc);
		background-color: var(--btn-hover-bg);
		color: var(--btn-hover-c);
	}

	// MQ
	@media (config.$md-down) {
		&--block--m {
			display: block;
			width: 100%;
		}
		&--icon-only--m {
			--btn-padding-x: 2rem;
			--btn-fs: 0;
			#{$s}__icon {
				position: relative;
				transform: none;
			}
		}
	}
	@media (config.$xl-up) {
		&--sm {
			--btn-padding-x: 3rem;
		}
	}
}
