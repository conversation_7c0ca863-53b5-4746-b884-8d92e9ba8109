@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.table-meta {
	margin-bottom: 3rem;
	border: 0;
	font-family: variables.$fontSecondary;
	font-size: 1.2rem;
	line-height: calc(20 / 12);
	th,
	td {
		padding: 0.5rem 1rem;
		border: 0;
		font-weight: normal;
		letter-spacing: 0.16em;
		text-transform: uppercase;
		&:first-child {
			padding-left: 0;
		}
		&:last-child {
			padding-right: 0;
		}
	}
	a {
		word-break: break-word;
	}
	&__link {
		position: relative;
		top: -0.3rem;
		color: variables.$colorSecondary;
		font-family: variables.$font;
		font-weight: bold;
		font-size: 1.4rem;
		line-height: calc(20 / 14);
		letter-spacing: 0.02em;
		text-transform: none;
	}

	// HOVERS
	.hoverevents &__link:hover,
	&__link:focus {
		color: variables.$colorSecondaryDarken;
	}
}
