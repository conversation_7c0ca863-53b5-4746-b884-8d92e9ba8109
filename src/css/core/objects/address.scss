@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.address {
	&__title {
		margin-bottom: calc(6em / 16);
		letter-spacing: 0.08em;
		text-transform: uppercase;
	}
	&__wrap {
		@include mixins.clearfix();
		margin-left: -3rem;
	}
	&__item {
		float: left;
		margin: 0 0 0 3rem;
	}

	// MQ
	@media (config.$md-up) {
		&__title {
			font-size: 1.6rem;
		}
	}
}
