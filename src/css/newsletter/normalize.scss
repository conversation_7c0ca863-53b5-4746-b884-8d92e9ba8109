/* stylelint-disable declaration-no-important */
body {
	width: 100%;
	margin: 0;
	padding: 0;
	background: #ffffff; // v<PERSON>dy b<PERSON>l<PERSON>
	color: $colorText;
	font-family: $fontFamily;
	-webkit-font-smoothing: antialiased; // vyhlazení fontu
	font-size: $fontSize;
	line-height: $lineHeight;
	-webkit-text-size-adjust: 100%; // zrušení změny velikosti malých textů
	-ms-text-size-adjust: 100%; // zrušení změny velikosti malých textů
}

div[style*='margin: 16px 0'] {
	// android hack
	margin: 0 !important;
}

*[x-apple-data-detectors] {
	// iOS defaultní barva pro detekováné věci jako je adresa, telefon, e-mail apod.
	color: inherit !important;
	text-decoration: none !important;
}

.x-gmail-data-detectors, // Gmail defaultní barva pro detekováné věci jako je adresa, telefon, e-mail apod.
.x-gmail-data-detectors *,
.aBn {
	border-bottom: 0 !important;
	cursor: default !important;
}

.img-gmail-fix {
	// Gmail image hack
	min-width: $containerWidth;
}

table {
	table-layout: fixed;
	border-collapse: collapse; // Outlook hack
	border-spacing: 0; // Outlook hack
	width: 100%;
	margin: 0;
	padding: 0;
	color: $colorText; // Gmail & Seznam hack
	font-family: $fontFamily; // Outlooku hack
	font-size: $fontSize; // Outlooku hack
	line-height: $lineHeight; // Outlooku hack
	// mso-line-height-rule: exactly; // Outlook hack
	text-align: left;
}

td,
th {
	vertical-align: middle;
	padding: 0;
}

th {
	text-align: left; // v některých klientech je center
}

img {
	vertical-align: top; // ruší prázné místo pod obrázkem (případně lze použít display: block;)
	border: 0; // ruší border okolo obrázku, když je obrázek linkem
}
