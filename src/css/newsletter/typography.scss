h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
	margin: $md 0 $xs;
	padding: 0; // Seznam.cz hack
	color: $colorPrimary;
	font-family: $fontFamily; // v n<PERSON>kt<PERSON><PERSON><PERSON> webových klientech se musí nastavovat explicitně
	font-weight: bold;
	text-align: left;
	&:first-child {
		margin: 0 0 $xs;
	}
}
h1,
.h1 {
	font-size: 20px;
	line-height: 28px;
}
h2,
.h2 {
	font-size: 22px;
	line-height: 28px;
}
h3,
.h3 {
	font-size: 20px;
	line-height: 26px;
}
h4,
.h4 {
	font-size: 18px;
	line-height: 24px;
}
h5,
.h5,
h6,
.h6 {
	font-size: $fontSize;
	line-height: $lineHeight;
}
ol,
ul,
p {
	margin: 0 0 $sm;
}
ol,
ul {
	padding: 0 0 0 $sm;
}
ul {
	list-style: square;
}
li {
	margin: 0;
	padding: 0;
}
a {
	color: $colorPrimary;
}
img {
	height: auto; // Office 365 fix
}

.t-table {
	td,
	th {
		padding: 5px 0 5px 20px;
		&:first-child {
			padding-left: 0;
		}
	}
}
