// vertical align
.u-va {
	&-top {
		vertical-align: top; // případně atribut valign="top"
	}
	&-middle {
		vertical-align: middle; // případně atribut valign="middle"
	}
	&-bottom {
		vertical-align: bottom; // případně atribut valign="bottom"
	}
}

// font & text
.u-tt-upper {
	text-transform: uppercase;
}

.u-ta {
	&-left {
		text-align: left;
	}
	&-center {
		text-align: center;
	}
	&-right {
		text-align: right;
	}
}

.u-fs {
	&-italic {
		font-style: italic;
	}
	&-normal {
		font-style: normal;
	}
}

.u-fw {
	&-bold {
		font-weight: bold;
	}
	&-normal {
		font-weight: normal;
	}
}

// padding
.u-pl {
	&-0 {
		padding-left: 0;
	}
	&-xs {
		padding-left: $xs;
	}
	&-sm {
		padding-left: $sm;
	}
	&-md {
		padding-left: $md;
	}
	&-lg {
		padding-left: $lg;
	}
	&-xl {
		padding-left: $xl;
	}
}

.u-pr {
	&-0 {
		padding-right: 0;
	}
	&-xs {
		padding-right: $xs;
	}
	&-sm {
		padding-right: $sm;
	}
	&-md {
		padding-right: $md;
	}
	&-lg {
		padding-right: $lg;
	}
	&-xl {
		padding-right: $xl;
	}
}

.u-pt {
	&-0 {
		padding-top: 0;
	}
	&-xs {
		padding-top: $xs;
	}
	&-sm {
		padding-top: $sm;
	}
	&-md {
		padding-top: $md;
	}
	&-lg {
		padding-top: $lg;
	}
	&-xl {
		padding-top: $xl;
	}
}

.u-pb {
	&-0 {
		padding-bottom: 0;
	}
	&-xs {
		padding-bottom: $xs;
	}
	&-sm {
		padding-bottom: $sm;
	}
	&-md {
		padding-bottom: $md;
	}
	&-lg {
		padding-bottom: $lg;
	}
	&-xl {
		padding-bottom: $xl;
	}
}

// margin
.u-ml-auto {
	// potřeba kombinovat s align="right"
	margin-left: auto;
}
.u-mr-auto {
	// potřeba kombinovat s align="left"
	margin-right: auto;
}
.u-mx-auto {
	// potřeba kombinovat s align="center"
	margin-right: auto;
	margin-left: auto;
}

.u-mb {
	&-0 {
		margin-bottom: 0;
	}
	&-xs {
		margin-bottom: $xs;
	}
	&-sm {
		margin-bottom: $sm;
	}
	&-md {
		margin-bottom: $md;
	}
	&-lg {
		margin-bottom: $lg;
	}
	&-xl {
		margin-bottom: $xl;
	}
	&-last-0 {
		> :last-child {
			margin-bottom: 0;
		}
	}
}

// background
.u-bg {
	&-grey {
		background: $colorBg;
	}
	&-primary {
		background: $colorPrimary;
	}
}

// color
.u-c {
	&-white {
		color: $colorWhite;
		a,
		h1,
		h2,
		h3,
		h4,
		h5,
		[class*='u-c-'],
		table {
			color: $colorWhite;
		}
	}
	&-primary {
		color: $colorPrimary;
		a,
		h1,
		h2,
		h3,
		h4,
		h5,
		[class*='u-c-'],
		table {
			color: $colorPrimary;
		}
	}
}

.t-mb-last-0 {
	> :last-child {
		margin-bottom: 0;
	}
}
