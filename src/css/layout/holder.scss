@use 'config';
@use 'base/variables';
@use 'base/extends';
@use 'base/mixins';

.holder {
	--holder-gap: 2rem;
	margin-right: auto;
	margin-left: auto;
	padding-right: var(--holder-gap);
	padding-left: var(--holder-gap);

	// MQ
	@media (config.$md-up) {
		&--md,
		&--lg {
			--holder-gap: 4rem;
		}
	}
	@media (config.$xxl-up) {
		&--lg {
			--holder-gap: 8rem;
			padding-right: 8rem;
			padding-left: 8rem;
		}
	}
}
