/* stylelint-disable declaration-no-important */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.footer {
	$s: &;
	font-size: 1.4rem;
	&__logo {
		margin: 0;
		& > a {
			display: inline-block;
		}
		& > a,
		.icon-svg {
			max-width: 100%;
		}
		.icon-svg {
			width: 25.5rem;
		}
	}
	&__group {
		border: 0.1rem solid variables.$colorBd;
		border-width: 0.1rem 0;
	}
	&__group:has(+ #{$s}__group) {
		border-bottom-width: 0;
	}
	&__title {
		position: relative;
		margin-bottom: calc(13em / 14);
		font-size: 1.6rem;
		letter-spacing: 0.08em;
		text-transform: uppercase;
		&--faculty {
			margin-bottom: calc(24em / 14);
		}
		&-icon {
			position: absolute;
			top: 50%;
			right: 2rem;
			margin-top: -0.3rem;
			color: variables.$colorSecondary;
			transition: transform variables.$t;
			.is-open & {
				transform: scaleY(-1);
			}
		}
	}
	&__list {
		@extend %reset-ul;
		margin-bottom: -1rem;
	}
	&__item {
		@extend %reset-ul-li;
		margin-bottom: 1rem;
	}
	&__link {
		position: relative;
		display: block;
		color: variables.$colorSecondary;
		text-decoration: none;
		.hoverevents &:focus,
		.hoverevents &:hover,
		&[aria-current],
		&[aria-selected='true'] {
			color: variables.$colorSecondaryDarken;
			text-decoration: underline;
		}
		&--faculty {
			display: flex;
			align-items: center;
			line-height: calc(20 / 14);
			.hoverevents &:focus,
			.hoverevents &:hover {
				text-decoration: none;
				#{$s}__link-label {
					text-decoration: underline;
				}
			}
		}
		&-label {
			flex: 0 1 auto;
		}
	}

	@media (config.$md-down) {
		&__group {
			border-top: 0.1rem solid variables.$colorBd;
		}
		&__group + &__group:not(:last-child) {
			border-top: 0;
		}
		&__title {
			width: auto;
			margin: 0 -2rem;
			padding: 2.1rem 5.2rem 2rem 2rem;
			border-top: 0.1rem solid variables.$colorBd;
			cursor: pointer;
		}
		&__more > &__list {
			padding-bottom: 2rem;
		}
		&__logo {
			margin-bottom: 2rem;
		}
		&__info {
			margin-bottom: 3rem;
		}
		.grid__cell:not(.is-open) > &__more {
			display: none;
		}
	}
	@media (config.$md-up) {
		&__title-icon {
			display: none;
		}
		&__more {
			height: auto !important;
			overflow: visible !important;
		}
	}
	@media (config.$md-up) and (config.$xl-down) {
		&__socials {
			margin-top: 20px;
		}
	}
	@media (config.$xl-up) {
		&__title--pulled {
			margin-top: -4.2rem;
		}
		&__socials .c-socials__list {
			justify-content: flex-end;
			text-align: right;
		}
	}
}
