@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.header {
	position: relative;
	z-index: 10;
	font-size: 1.4rem;
	&__logo {
		position: relative;
		z-index: 3;
		float: left;
		margin: 0;
		.hoverevents &:hover &-main {
			transform: translate3d(0, 0, 0);
		}
		> a {
			display: inline-block;
		}
		.icon-svg--symbol {
			background: variables.$colorPrimary;
		}
		.icon-svg--logo {
			width: 29.3rem;
			height: 11rem;
		}
		.icon-svg--logo--en {
			width: 34.4rem;
			height: 11.1rem;
		}
		&-main {
			position: absolute;
			top: 0;
			left: 0;
			z-index: -1;
			transition: transform 0.75s ease-in-out;
			transform: translate3d(0, 0, 0);
			&.is-hidden {
				transform: translate3d(-100%, 0, 0);
			}
		}
	}
	&__menu,
	&__search {
		width: calc(100% - var(--menu-height));
	}
	&__menu {
		z-index: 1;
		float: left;
		height: var(--menu-height);
		border-bottom: 0.1rem solid variables.$colorBd;
	}
	&__search {
		position: absolute;
		top: 0;
		right: 0;
	}

	// MQ
	@media (config.$md-down) {
		position: sticky;
		top: 0;
		background: variables.$colorWhite;
		transition: transform variables.$t ease-in-out;
		&.is-unpinned {
			transform: translateY(-100%);
		}
		&:has(.m-main.is-open) {
			transform: none;
		}
		&__breadcrumb {
			display: none;
		}
		&__logo {
			.icon-svg--symbol {
				width: 6.5rem;
			}
			&-main,
			&-sign {
				display: none;
			}
		}
		&__search {
			width: 100%;
		}
	}
	@media (config.$md-up) {
		&__menu,
		&__breadcrumb,
		&__search {
			float: left;
			width: calc(100% - 11rem);
		}
	}
}
