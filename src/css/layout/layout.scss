@use 'config';
@use 'base/variables';
@use 'base/extends';
@use 'base/mixins';

html {
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	min-height: 100%;
	scroll-behavior: smooth;
	scrollbar-gutter: stable;
	&.tracy-bs-visible.tracy-bs-visible {
		overflow: visible;
	}
}
*,
*::before,
*::after {
	box-sizing: inherit;
}
body {
	position: relative;
	display: flex;
	flex: 1;
	flex-direction: column;
	min-width: 32rem;
	background: variables.$colorBd;

	// STATES
	&.is-modal-open {
		overflow: clip;
	}
}
:first-child {
	margin-top: 0;
}
