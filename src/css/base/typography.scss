/* stylelint-disable declaration-no-important */
@use 'config';
@use 'base/variables';
@use 'base/extends';
@use 'base/mixins';

html {
	color: variables.$colorText;
	font-size: 62.5%;
}

body {
	font-family: variables.$font;
	font-size: variables.$fontSize;
	line-height: variables.$lineHeight;
}

// Headings
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
	margin: 1.5em 0 0.5em;
	font-family: variables.$fontSecondary;
	font-weight: normal;
	line-height: 1.2;
}
h1,
.h1 {
	margin-bottom: calc(20em / 36);
	font-size: 2.6rem;
	line-height: calc(40 / 36);
	@media (config.$md-up) {
		font-size: 2.8rem;
	}
	@media (config.$xl-up) {
		font-size: 3.6rem;
	}
}
h2,
.h2 {
	font-size: 2.4rem;
	line-height: calc(35 / 30);
	@media (config.$md-up) {
		font-size: 2.6rem;
	}
	@media (config.$xl-up) {
		font-size: 3rem;
	}
}
h3,
.h3 {
	margin-bottom: calc(10em / 24);
	font-size: 2rem;
	line-height: calc(30 / 24);
	@media (config.$md-up) {
		font-size: 2.4rem;
	}
}
h4,
.h4 {
	font-size: 1.8rem;
	line-height: calc(20 / 18);
	text-transform: none;
}
h5,
.h5 {
	font-size: 1.6rem;
	line-height: calc(20 / 16);
	letter-spacing: 0.08em;
	text-transform: uppercase;
}
h6,
.h6 {
	color: variables.$colorGray;
	font-size: 1.2rem;
	line-height: calc(16 / 12);
	letter-spacing: 0.08em;
	text-transform: uppercase;
}

// Paragraph
p {
	margin: 0 0 1.25em;
	letter-spacing: 0.02em;
}
hr {
	height: 0.1rem;
	margin: 3rem 0;
	border: 0;
	border-top: 0.1rem solid variables.$colorBd;
	overflow: hidden;
	@media (config.$md-up) {
		margin: 4rem 0;
	}
	@media (config.$xl-up) {
		margin: 5rem 0;
	}
}

// Blockquote
blockquote {
	margin: calc(10em / 22) 0 calc(30em / 22);
	padding: 1rem 0 1rem 2rem;
	border-left: 0.2rem solid variables.$colorSecondary;
	font-size: 2.2rem;
	line-height: 1.5;
	p {
		margin: 0;
	}

	@media (config.$xl-up) {
		padding: 1.5rem 0 1.5rem 4rem;
		font-weight: 300;
		font-size: 3.6rem;
		line-height: calc(45 / 36);
	}
}

// Links
a,
.as-link {
	color: variables.$colorLink;
	text-decoration: underline;
	transition: color variables.$t;
	-webkit-tap-highlight-color: transparent;

	// STATES
	&:not([class]):visited {
		color: variables.$colorPrimaryVisited;
	}
	&.u-c-secondary:visited {
		color: variables.$colorSecondaryVisited;
	}

	// HOVERS
	.hoverevents &:focus,
	.hoverevents &:hover {
		color: variables.$colorHover;
		text-decoration: none;
	}
	.hoverevents &.u-c-secondary:hover {
		color: variables.$colorSecondaryDarken;
	}
}

.as-link {
	cursor: pointer;
}

// Lists
ul,
ol,
dl,
menu {
	margin: 0 0 1.25em;
	padding: 0;
	letter-spacing: 0.02em;
	list-style: none;
}
li {
	margin: 0 0 0.625em;
	padding: 0 0 0 2.5rem;
	ol,
	ul {
		margin: 0.625em 0 0;
	}
}
ul {
	li {
		@include mixins.svgBackground(variables.$svgBullet, variables.$colorRockBlue);
		background-position: 0 1.1rem;
		background-repeat: no-repeat;
		background-size: 0.4rem 0.4rem;
		li {
			@include mixins.svgBackground(variables.$svgBullet, #b8cfe7);

			li {
				@include mixins.svgBackground(variables.$svgBullet, #d1e8ff);
			}
		}
	}
}
ol {
	counter-reset: item;
	& > li {
		position: relative;
		background: none;
		&::before {
			content: counter(item) '.';
			counter-increment: item;
			position: absolute;
			top: 0.1rem;
			right: 100%;
			margin-right: -2rem;
			font-weight: bold;
			text-align: left;
		}
	}
	&[style*='list-style-type: lower-alpha'] > li {
		list-style-type: none !important;
		&::before {
			content: counter(item, lower-alpha) ')';
		}
	}
	&[style*='list-style-type: upper-alpha'] > li {
		list-style-type: none !important;
		&::before {
			content: counter(item, upper-alpha) ')';
		}
	}
	&[style*='list-style-type: lower-greek'] > li {
		list-style-type: none !important;
		&::before {
			content: counter(item, lower-greek) ')';
		}
	}
	&[style*='list-style-type: lower-roman'] > li {
		list-style-type: none !important;
		&::before {
			content: counter(item, lower-roman) '.';
		}
	}
	&[style*='list-style-type: upper-roman'] > li {
		list-style-type: none !important;
		&::before {
			content: counter(item, upper-roman) '.';
		}
	}
}
dt {
	margin: 0;
	font-weight: bold;
}
dd {
	margin: 0 0 0.75em;
	padding: 0;
}

// Tables
table {
	clear: both;
	border-collapse: collapse;
	border-spacing: 0;
	empty-cells: show;
	width: 100%;
	margin: 0 0 1.25em;
	border: 0.1rem solid variables.$colorBd;
	line-height: 1.5;
}

caption {
	padding: 0 0 1rem;
	font-weight: bold;
	text-align: left;
	caption-side: top;
}
td,
th {
	padding: 0.7rem 0.8rem;
	border: 0.1rem solid variables.$colorBd;
}
th {
	font-weight: bold;
	text-align: left;
}
thead th {
	background: variables.$colorBg;
}

// Image
figure {
	display: table;
	width: auto !important;
	margin: 1.25em 0;
	img {
		display: block;
	}
}
figcaption {
	display: table-caption;
	margin-top: calc(15em / 14);
	font-size: 1.4rem;
	line-height: calc(25 / 14);
	letter-spacing: 0.02em;
	caption-side: bottom;
}

img {
	max-width: 100%;
	height: auto;
}
