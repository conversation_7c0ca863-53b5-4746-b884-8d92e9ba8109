@use 'config';
@use 'base/variables';

// Grid
@mixin generate-grid-size($breakpoints: config.$breakpoints, $columns: variables.$gridColumns, $auto: true, $auto-grow: true) {
	@if ($auto) {
		&--auto {
			flex: 0 1 auto;
			width: auto;
		}
	}
	@if ($auto-grow) {
		&--autogrow {
			flex: 1 0 auto;
			width: auto;
		}
	}
	@for $column from 1 to ($columns + 1) {
		@at-root #{&}--#{$column}-#{$columns} {
			width: percentage(calc($column / $columns));
		}
	}
	@each $breakpoint, $value in $breakpoints {
		@include mq-checker($breakpoint) {
			@if ($auto) {
				&--auto {
					@include suffix($breakpoint) {
						flex: 0 1 auto;
						width: auto;
					}
				}
			}
			@if ($auto-grow) {
				&--autogrow {
					@include suffix($breakpoint) {
						flex: 1 0 auto;
						width: auto;
					}
				}
			}
			@for $column from 1 to ($columns + 1) {
				@at-root #{&}--#{$column}-#{$columns} {
					@include suffix($breakpoint) {
						width: percentage(calc($column / $columns));
					}
				}
			}
		}
	}
}

@mixin generate-grid-push($breakpoints: config.$breakpoints, $columns: variables.$gridColumns) {
	@for $column from 1 to ($columns + 1) {
		@at-root #{&}--#{$column}-#{$columns} {
			left: percentage(calc($column / $columns));
		}
	}
	@each $breakpoint, $value in $breakpoints {
		@include mq-checker($breakpoint) {
			@for $column from 1 to ($columns + 1) {
				@at-root #{&}--#{$column}-#{$columns} {
					@include suffix($breakpoint) {
						left: percentage(calc($column / $columns));
					}
				}
			}
		}
	}
}

@mixin generate-grid-pull($breakpoints: config.$breakpoints, $columns: variables.$gridColumns) {
	@for $column from 1 to ($columns + 1) {
		@at-root #{&}--#{$column}-#{$columns} {
			left: percentage(calc($column / $columns * -1));
		}
	}
	@each $breakpoint, $value in $breakpoints {
		@include mq-checker($breakpoint) {
			@for $column from 1 to ($columns + 1) {
				@at-root #{&}--#{$column}-#{$columns} {
					@include suffix($breakpoint) {
						left: percentage(calc($column / $columns * -1));
					}
				}
			}
		}
	}
}

@mixin mq-checker($breakpoint, $type: 'up') {
	@if (map-has-key(config.$breakpoints-vars, '#{$breakpoint}-#{$type}')) {
		@if (
			($type == 'up' and ($breakpoint == 'md' or $breakpoint == 'lg')) or
				($type == 'Down' and ($breakpoint == 'lg' or $breakpoint == 'xl'))
		) {
			@media (map-get(config.$breakpoints-vars, '#{$breakpoint}-#{$type}')), print {
				@content;
			}
		} @else {
			@media (map-get(config.$breakpoints-vars, '#{$breakpoint}-#{$type}')) {
				@content;
			}
		}
	} @else {
		@error 'Unfortunately, breakpoint `#{$breakpoint}` is not defined in config.js';
	}
}

// Suffix
@mixin suffix($suffix, $delimiter: '\\@') {
	&#{$delimiter}#{$suffix} {
		@content;
	}
}

// Breakpoints
@mixin generate-breakpoints($breakpoints: config.$breakpoints) {
	@content;
	@each $breakpoint, $value in $breakpoints {
		@include mq-checker($breakpoint) {
			@include suffix($breakpoint) {
				@content;
			}
		}
	}
}

@mixin breakpoint-up($breakpoint, $breakpoints: config.$breakpoints) {
	$bp: map-get($breakpoints, $breakpoint);
	$value: if($bp != 0, $bp, null);

	@if $value {
		@media (min-width: $value) {
			@content;
		}
	} @else {
		@content;
	}
}

// Utilities
@mixin generate-utilities($utilities: $utilities) {
	@each $breakpoint,
		$value
			in map-merge(
				(
					_: 0
				),
				config.$breakpoints
			)
	{
		@include breakpoint-up($breakpoint) {
			$suffix: if($value == 0, '', '\\@#{$breakpoint}');

			@each $key, $utility in $utilities {
				@if type-of($utility) == 'map' and (map-get($utility, responsive) or $suffix == '') {
					@include generate-utility($utility, $suffix);
				}
			}
		}
	}
}
@mixin generate-utility($utility, $suffix) {
	$values: map-get($utility, values);

	@each $key, $value in $values {
		$property: map-get($utility, property);
		$class: map-get($utility, class);

		.u-#{$class}-#{$key}#{$suffix} {
			#{$property}: $value;
		}
	}
}

// Line clamp
@mixin line-clamp($lines: 1) {
	overflow: hidden;
	@if ($lines == 1) {
		white-space: nowrap;
		text-overflow: ellipsis;
	} @else {
		display: -webkit-box;
		-webkit-line-clamp: #{$lines};
		/*! autoprefixer: ignore next */
		-webkit-box-orient: vertical;
	}
}

// Clearfix
@mixin clearfix() {
	&::before,
	&::after {
		content: '';
		display: table;
	}
	&::after {
		clear: both;
	}
}

// Hiding content
@mixin vhide() {
	position: absolute;
	width: 0.1rem;
	height: 0.1rem;
	margin: -0.1rem;
	padding: 0;
	border: 0;
	overflow: hidden;
	clip: rect(0 0 0 0);
}

@mixin button-reset {
	display: inline-block;
	padding: 0;
	border: none;
	border-radius: 0;
	background: none;
	color: inherit;
	line-height: inherit;
	text-align: inherit;
	appearance: none;
	cursor: pointer;
}

@mixin dialog-reset {
	max-width: none;
	max-height: none;
	margin: 0;
	padding: 0;
	border: none;
	background: none;
}

@function replace($search, $replace, $string) {
	$index: str-index($string, $search);
	@return str-slice($string, 1, $index - 1) + $replace + str-slice($string, $index + str-length($search));
}

@mixin svgBackground($svg, $color: variables.$colorBlack) {
	$color-str: if(type-of($color) == 'color', str-slice(inspect($color), 2), $color);
	$svg-replaced: replace('{{ color }}', $color-str, $svg);
	background-image: url(#{$svg-replaced});
}
/* Text Alignment and Transformation classes */
@mixin text-overflow() {
	vertical-align: bottom;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}
/* Inline lists */
@mixin inline-list($width, $fontSize) {
	font-size: 0;
	& > * {
		display: inline-block;
		vertical-align: top;
		font-size: $fontSize;
		@if $width != false {
			width: $width;
		}
	}
}
// Triangle
@mixin triangle($direction: 'down', $width: 1rem, $height: 1rem, $color: #000) {
	width: 0;
	height: 0;
	border-style: solid;

	@if $direction == 'up' {
		border-width: 0 ($width * 0.5) $height ($width * 0.5);
		border-color: transparent transparent $color transparent;
	} @else if $direction == 'down' {
		border-width: $height ($width * 0.5) 0 ($width * 0.5);
		border-color: $color transparent transparent transparent;
	} @else if $direction == 'left' {
		border-width: ($height * 0.5) $width ($height * 0.5) 0;
		border-color: transparent $color transparent transparent;
	} @else if $direction == 'right' {
		border-width: ($height * 0.5) 0 ($height * 0.5) $width;
		border-color: transparent transparent transparent $color;
	} @else if $direction == 'up-left' {
		border-width: $height $width 0 0;
		border-color: $color transparent transparent transparent;
	} @else if $direction == 'down-left' {
		border-width: $width 0 0 $height;
		border-color: transparent transparent transparent $color;
	} @else if $direction == 'up-right' {
		border-width: 0 $width $height 0;
		border-color: transparent $color transparent transparent;
	} @else if $direction == 'down-right' {
		border-width: 0 0 $height $width;
		border-color: transparent transparent $color transparent;
	}
}
