@use 'config';

:root {
	--menu-height: 6.5rem;
	--header-height: var(--menu-height);
	@media (config.$md-up) {
		--header-height: 11rem;
	}
}

// Colors
$colorTwitter: #1da1f2;

$colorBlack: #000000;
$colorWhite: #ffffff;
$colorRockBlue: #9eb5cd;
$colorGray: #6f7a86;
$colorGreen: #00a64f;
$colorYellow: #f5c800;

$colorBd: #e8eaea;
$colorBdDarken: #c8d0d0;
$colorBg: #f3f7f6;

$color: $colorBlack;
$colorRed: #e4002b;
$colorOrange: #f57c00;
$colorPrimary: $colorRed;
$colorPrimaryDarken: darken($colorPrimary, 20%);
$colorPrimaryVisited: #7c0219;
$colorSecondary: #1676d0;
$colorSecondaryDarken: darken($colorSecondary, 20%);
$colorSecondaryVisited: #002d57;

$colorLink: $colorPrimary;
$colorHover: $colorPrimaryDarken;
$colorText: $color;

// Faculty
// $colorFA: #7a99ac;
// $colorFADarken: darken($colorFA, 20%);
// $colorFEKT: #003da5;
// $colorFEKTDarken: darken($colorFEKT, 20%);
// $colorFCH: #00ab8e;
// $colorFCHDarken: darken($colorFCH, 20%);
// $colorFIT: #00a9e0;
// $colorFITDarken: darken($colorFIT, 20%);
// $colorFAST: #658d1b;
// $colorFASTDarken: darken($colorFAST, 20%);
// $colorFP: #8246af;
// $colorFPDarken: darken($colorFP, 20%);
// $colorFSI: #004f71;
// $colorFSIDarken: darken($colorFSI, 20%);
// $colorFaVU: #e782a9;
// $colorFaVUDarken: darken($colorFaVU, 20%);
// $colorUSI: #201547;
// $colorUSIDarken: darken($colorUSI, 20%);
// $colorSTI: #76c045;
// $colorSTIDarken: darken($colorSTI, 20%);
// $colorCESA: #009cab;
// $colorCESADarken: darken($colorCESA, 20%);
// $colorVUTIUM: #009cab;
// $colorVUTIUMDarken: darken($colorVUTIUM, 20%);

// Spacing
$utils-spacing: (
	'0': 0,
	'xs': 1rem,
	'sm': 2rem,
	'md': 4rem,
	'lg': 6rem,
	'xl': 8rem,
	'2xl': 10rem
);

// Font
$font: 'Open Sans', arial, helvetica, sans-serif;
$fontSecondary: 'Vafle', $font;

// Base font size and line height
$fontSize: 1.6rem;
$lineHeight: calc(2.5rem / $fontSize);

// Focus
$focusOutlineColor: $colorSecondary;
$focusOutlineStyle: solid;
$focusOutlineWidth: 0.1rem;

// Grid
$gridColumns: 12;
$gridGutter: 3rem;
$rowMainWidth: 128rem;

// Paths
$imgPath: map-get(config.$paths, 'images');
$fontsPath: map-get(config.$paths, 'fonts');

// Transitions
$t: 0.3s;

// SVGs
$svgBullet: 'data:image/svg+xml,%3Csvg%20viewBox%3D%270%200%204%204%27%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%3E%3Crect%20fill%3D%27%23{{ color }}%27%20width%3D%274%27%20height%3D%274%27%20rx%3D%271%27%2F%3E%3C%2Fsvg%3E';
