/* eslint-disable @babel/no-unused-expressions */
import { Controller } from '@hotwired/stimulus';
import { useIntersection } from 'stimulus-use';
import 'yt-player';
import Player from '@vimeo/player';

export const create = () => {
	return class extends Controller {
		static targets = ['video'];
		static values = { id: String, payervars: Object, autoplay: Boolean, type: String };

		connect = () => {
			const { videoTarget } = this;
			this.initialized = false;
			this.type = videoTarget.getAttribute('data-type') ? videoTarget.getAttribute('data-type') : false;
			this.videoId = videoTarget.getAttribute('data-id') ? videoTarget.getAttribute('data-id') : false;
			this.options = videoTarget.getAttribute('data-options') ? videoTarget.getAttribute('data-options') : {};
			this.autoplay = videoTarget.getAttribute('data-autoplay') ? videoTarget.getAttribute('data-autoplay') : false;
			this.loop = videoTarget.getAttribute('data-loop') ? videoTarget.getAttribute('data-loop') : false;

			useIntersection(this, {
				rootMargin: '1000px',
			});
		};

		appear = () => {
			if (!this.initialized) {
				this.init();
				this.initialized = true;
			}
		};

		init = () => {
			const { videoTarget, element, videoId, options, dispatch, type, autoplay } = this;

			// Initialization
			if (videoId && type == 'youtube') {
				// Youtube
				const YTPlayer = require('yt-player');
				this.player = new YTPlayer(videoTarget, JSON.parse(options));
				if (autoplay) {
					this.player.mute();
					this.player.load(videoId, true, 0);
				} else {
					this.player.load(videoId);
				}

				// Handle Youtube loop
				if (this.loop) {
					this.player.on('ended', () => this.player.play());
				}
			} else if (videoId && this.type == 'vimeo') {
				// Vimeo
				this.player = new Player(videoTarget, Object.assign({ id: videoId }, JSON.parse(options)));
				if (autoplay) {
					this.player.setVolume(0);
					this.player.play();
				}
			}

			// YT & Vimeo states
			const { player } = this;
			player.on('playing', () => {
				element.classList.add('is-playing');
				// dataLayer.push({
				// 	event: 'VIDEO_PLAY',
				// });
			});
			['pause', 'paused', 'ended'].forEach((e) => {
				player.on(e, () => element.classList.remove('is-playing'));
			});

			player.on('ended', () => dispatch('ended'));
		};

		toggle(event) {
			event.preventDefault();

			if (['LITE-YOUTUBE', 'LITE-VIMEO'].includes(this.videoTarget.tagName)) {
				this.videoTarget.click();
				this.element.classList.toggle('is-playing');
			} else {
				this.element.classList.contains('is-playing') ? this.player.pause() : this.player.play();
			}
		}
	};
};
