import { Controller } from '@hotwired/stimulus';
import { iconSvg } from '../tools/iconSvg';

export const create = () => {
	return class extends Controller {
		static values = { options: Object };

		async connect() {
			var htmlLang = document.documentElement.lang;
			var flatpickr = (await import(/* webpackPrefetch: true */ 'flatpickr')).default;
			var localization;

			if (htmlLang === 'cs') {
				localization = (await import(/* webpackPrefetch: true */ 'flatpickr/dist/l10n/cs')).default.cs;
			} else {
				localization = (await import(/* webpackPrefetch: true */ 'flatpickr/dist/l10n/default')).default;
			}

			this.options = {
				prevArrow: iconSvg('angle-l'),
				nextArrow: iconSvg('angle-r'),
				time_24hr: true,
				locale: localization,
				animate: false,
				altInput: true,
				altFormat: 'j. F Y',
				minuteIncrement: 10,
				disableMobile: true,
				...this.optionsValue,
			};

			const datepicker = flatpickr(this.element, this.options);

			if (datepicker.timeContainer) {
				const timeArrows = [...datepicker.timeContainer.querySelectorAll('.arrowUp, .arrowDown')];
				const { hours, minutes } = this.optionsValue.labels;
				const hourElement = datepicker.hourElement;
				const minuteElement = datepicker.minuteElement;
				const hourLabel = document.createElement('span');
				const minuteLabel = document.createElement('span');
				hourLabel.classList.add('label');
				minuteLabel.classList.add('label');
				hourLabel.textContent = hours;
				minuteLabel.textContent = minutes;
				hourElement.parentNode.appendChild(hourLabel);
				minuteElement.parentNode.appendChild(minuteLabel);
				timeArrows.forEach((element) => {
					if (element.classList.contains('arrowUp')) {
						element.innerHTML = iconSvg('angle-u');
					} else {
						element.innerHTML = iconSvg('angle-d');
					}
				});
			}
		}
	};
};
