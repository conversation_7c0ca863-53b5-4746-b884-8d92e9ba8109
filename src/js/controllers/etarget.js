import { Controller } from '@hotwired/stimulus';
import { useClickOutside } from 'stimulus-use';

export const create = () => {
	return class extends Controller {
		connect() {
			useClickOutside(this);
		}
		clickOutside() {
			if (['is-open'].some((cls) => this.element.classList.contains(cls))) {
				// e.preventDefault(); // breaks first clck
				this.element.classList.remove('is-open');
				this.element.setAttribute('aria-expanded', 'false');
			}
		}
	};
};
