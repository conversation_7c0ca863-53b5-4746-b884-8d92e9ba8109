import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['inpFaculty', 'inpProgramme', 'inpBranch'];
		static outlets = ['choices'];

		connect() {
			if (this.hasInpFacultyTarget && this.hasInpProgrammeTarget && this.hasInpBranchTarget) {
				// Init filter
				this.filterOptions(this.inpProgrammeTarget, this.inpFacultyTarget.value, false);
				this.filterOptions(this.inpBranchTarget, this.inpProgrammeTarget.value, false);

				// Change events
				this.inpFacultyTarget.addEventListener('change', () => {
					this.filterOptions(this.inpProgrammeTarget, this.inpFacultyTarget.value);
					this.filterOptions(this.inpBranchTarget, this.inpProgrammeTarget.value);
				});
				this.inpProgrammeTarget.addEventListener('change', () => {
					this.filterOptions(this.inpBranchTarget, this.inpProgrammeTarget.value);
				});
			}
		}
		filterOptions = (inp, id, reset = true) => {
			if (reset) inp.value = 0;

			var optgroups = inp.querySelectorAll('optgroup');
			optgroups.forEach((optgroup) => {
				optgroup.disabled = optgroup.getAttribute('data-group') && optgroup.getAttribute('data-group') != id ? true : false;
			});

			// refresh choices.js
			this.choicesOutlets.forEach((choicesOutlet) => {
				if (this.element.contains(choicesOutlet.element)) {
					choicesOutlet.refresh();
				}

				if (inp == choicesOutlet.element) {
					choicesOutlet.unset();
				}
			});
		};
	};
};

// todo - vyber tez fakulty zustane otevreny choices.js
