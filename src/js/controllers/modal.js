import { initModal } from '@superkoders/modal';
import { Controller } from '@hotwired/stimulus';
import naja from 'naja';
import { iconSvg } from '../tools/iconSvg';

const fetchLoader = async (url, element) =>
	await fetch(url, {
		headers: {
			'X-Requested-With': 'XMLHttpRequest',
		},
	}).then((response) => {
		const contentType = response.headers.get('Content-Type');
		if (contentType.includes('application/json')) {
			return response.json().then((data) => {
				if (element && element.dataset.snippetid && data.snippets && data.snippets[element.dataset.snippetid]) {
					return `<div id="${element.dataset.snippetid}">${data.snippets[element.dataset.snippetid]}</div>`;
				} else {
					return 'Error: No valid snippet id provided.';
				}
			});
		} else {
			return response.text();
		}
	});

export const defaultOptions = {
	closeOnBgClick: true,
	removeOnClose: true,
	closeIcon: iconSvg('close'),
	prevIcon: iconSvg('angle-l'),
	nextIcon: iconSvg('angle-r'),
	modalSelector: '[data-modal]',
	loaderTpl: function () {
		return '<div class="body-loader__loader"></div>';
	},
	onModalContentLoaded: function () {
		naja.uiHandler.bindUI(this);
	},
	fetchLoader,
};

export const defaultInit = (options) => {
	// Default modal
	initModal(options);
};

export default class Modal extends Controller {
	initialize() {
		defaultInit(defaultOptions);
	}
}
