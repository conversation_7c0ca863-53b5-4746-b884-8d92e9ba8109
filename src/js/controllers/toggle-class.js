/* eslint-disable @babel/no-unused-expressions */
import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static values = {
			triggerClass: { type: Boolean, default: true },
		};

		toggle = (e) => {
			e.preventDefault();

			this.trigger = e.currentTarget;
			this.class = this.trigger.getAttribute('data-toggle-class') ?? 'is-open';
			this.closest = this.trigger.getAttribute('data-toggle-closest');
			this.content = this.trigger.getAttribute('data-toggle-content');

			if (this.closest) {
				// Nejbližší element
				this.toggleEl(this.trigger.closest(this.closest));
			} else if (this.content) {
				// Vlastní selektor
				document.querySelectorAll(this.content).forEach((el) => this.toggleEl(el));
			} else if (this.element != this.trigger) {
				// Element, na kterém je controler
				this.toggleEl(this.element);
			}
		};

		toggleEl = (el) => {
			el.classList.toggle(this.class);
			this.open = el.classList.contains(this.class);

			// Trigger
			if (this.triggerClassValue) this.trigger.classList[this.open ? 'add' : 'remove'](this.class);
			this.trigger.hasAttribute('aria-expanded') && this.trigger.setAttribute('aria-expanded', this.open ? 'true' : 'false');

			// Focus element
			var focusEl = el.querySelector('[data-focus]');
			if (focusEl) {
				setTimeout(() => {
					focusEl.focus();
				}, 300);
			}
		};

		toggleInp = (e) => {
			this.trigger = e.currentTarget;
			this.class = this.trigger.getAttribute('data-toggle-class') ?? 'is-open';
			this.closest = this.trigger.getAttribute('data-toggle-closest');
			this.content = this.trigger.getAttribute('data-toggle-content');

			if (this.closest) {
				this.trigger.closest(this.closest).classList[this.trigger.checked ? 'add' : 'remove'](this.class);
			} else if (this.content) {
				document.querySelectorAll(this.content).forEach((el) => this.toggleEl(el));
			} else {
				this.element.classList[this.trigger.checked ? 'add' : 'remove'](this.class);
			}
		};
	};
};
