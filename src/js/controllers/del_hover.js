import { Controller } from '@hotwired/stimulus';
import { MQ } from '../tools/MQ';

export const create = () => {
	return class extends Controller {
		connect() {
			this.delay = 300;
			this.element.addEventListener('mouseenter', this.show);
			this.element.addEventListener('mouseleave', this.hide);
		}

		show = () => {
			this.shouldShow = setTimeout(() => {
				if (MQ('lgUp')) this.element.classList.add('is-hover');
			}, this.delay);
		};

		hide = () => {
			clearTimeout(this.shouldShow);
			if (MQ('lgUp')) this.element.classList.remove('is-hover');
		};
	};
};
