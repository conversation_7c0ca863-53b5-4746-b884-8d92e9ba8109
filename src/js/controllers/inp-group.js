import { Controller } from '@hotwired/stimulus';
import { useClickOutside } from 'stimulus-use';
import naja from 'naja';

export const create = () => {
	return class extends Controller {
		static targets = ['values', 'input', 'label'];

		connect() {
			this.setSelectedValues();
			useClickOutside(this);
		}
		toggle = () => {
			if (this.element.classList.contains('is-open')) this.close();
			else this.element.classList.add('is-open');
		};
		close = () => {
			this.element.classList.remove('is-open');
			if (this.hasChanged) {
				this.submitForm();
				this.hasChanged = false;
			}
		};
		clickOutside = () => this.close();
		submitForm = () => {
			const form = this.element.closest('[data-naja]');
			if (form) naja.uiHandler.submitForm(form);
		};
		setSelectedValues = () => {
			this.valuesTarget.innerHTML = this.inputTargets
				.filter((input) => input.checked)
				.map((input) => input.getAttribute('data-name'))
				.join(', ');

			this.labelTarget.classList.toggle(
				'is-active',
				this.inputTargets.some((input) => input.checked),
			);
		};
		update = () => {
			this.setSelectedValues();
			this.hasChanged = true;
		};
	};
};
