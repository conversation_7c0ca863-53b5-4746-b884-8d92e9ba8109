import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static values = {
			placement: { type: String, default: 'right' },
		};

		async connect() {
			var tippy = (await import(/* webpackPrefetch: true */ 'tippy.js')).default;

			const options = {
				interactive: true,
				duration: 300,
				placement: this.placementValue,
				allowHTML: true,
				content: (reference) => reference.getAttribute('title'),
			};

			tippy(this.element, options);
		}
	};
};
