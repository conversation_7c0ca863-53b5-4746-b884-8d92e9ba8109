import { Controller } from '@hotwired/stimulus';
import { useWindowResize } from 'stimulus-use';

export const create = () => {
	return class extends Controller {
		static targets = ['wrap', 'prev', 'next', 'item'];
		static values = { init: String };

		connect() {
			useWindowResize(this);
			setTimeout(() => this.init(), 0);
			this.wrapTarget.addEventListener('scroll', () => {
				this.checkNav();
			});
		}
		init = () => {
			const isScrollable = this.wrapTarget.scrollWidth > this.wrapTarget.clientWidth;
			if (this.initialized || !isScrollable) return;
			this.checkNav();
			this.initialized = true;
		};
		checkNav = () => {
			const maxScroll = this.wrapTarget.scrollWidth - this.wrapTarget.clientWidth;
			const currentScroll = this.wrapTarget.scrollLeft;

			if (this.hasPrevTarget) this.prevTarget.classList.toggle('is-visible', currentScroll >= 1);
			if (this.hasNextTarget) this.nextTarget.classList.toggle('is-visible', currentScroll <= maxScroll - 1);

			this.element.classList.toggle('is-left-scrollable', currentScroll >= 1);
			this.element.classList.toggle('is-right-scrollable', currentScroll <= maxScroll - 1);
		};
		scrollLeft = () => this.scroll(-150);
		scrollRight = () => this.scroll(+150);
		scroll = (amount) => {
			this.wrapTarget.scrollTo({
				left: this.wrapTarget.scrollLeft + amount,
				behavior: 'smooth',
			});
			this.checkNav();
		};
		windowResize = () => {
			this.checkNav();
		};
	};
};
