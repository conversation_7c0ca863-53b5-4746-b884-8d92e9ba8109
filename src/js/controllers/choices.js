import { Controller } from '@hotwired/stimulus';
import { useIntersection } from 'stimulus-use';

export default class choices extends Controller {
	intersectoinOptions = {
		rootMargin: '0px 0px 300px 0px',
	};

	options = {
		searchEnabled: false,
		searchChoices: false,
		shouldSort: false,
		noChoicesText: '<PERSON><PERSON><PERSON> dostupn<PERSON> m<PERSON>',
	};

	async connect() {
		this.choicesLibrary = (await import(/* webpackPrefetch: true */ 'choices.js')).default;
		useIntersection(this, this.intersectoinOptions);
	}

	appear = () => {
		this.select = new this.choicesLibrary(this.element, this.options);
		const placeholder = this.select.itemList.element.querySelector('.choices__placeholder');
		const id = this.select.passedElement.element.getAttribute('id');
		const label = document.querySelector(`[for='${id}']`);

		if (!placeholder) label?.classList.add('is-active');

		this.select.passedElement.element.addEventListener('choice', (event) => {
			const isPlaceholder = event.detail.placeholder;
			if (!isPlaceholder) label.classList.add('is-active');
		});

		this.element.addEventListener('update', () => {
			this.refresh();
		});
	};

	refresh = () => {
		if (this.select) {
			this.select.refresh();
		}
	};

	unset = () => {
		if (this.select) this.select.setChoiceByValue('0');
	};
}
