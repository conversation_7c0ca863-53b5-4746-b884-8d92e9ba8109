import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['tab', 'panel'];

		select = (e) => {
			e.preventDefault();
			const currentTab = e.currentTarget;
			this.selectTab(currentTab);
			// Update URL with query parameter instead of hash
			const tabId = currentTab.getAttribute('href').replace('#', '');
			const url = new URL(window.location);
			url.searchParams.set('tab', tabId);
			history.pushState(null, null, url);
		};

		selectTab(currentTab) {
			const href = currentTab.getAttribute('href');
			const currentPanel = this.panelTargets.find((panel) => panel.getAttribute('id') === href.replace('#', ''));

			this.tabTargets.forEach((tab) => tab.setAttribute('aria-selected', false));
			this.panelTargets.forEach((panel) => panel.setAttribute('aria-hidden', true));

			currentTab.setAttribute('aria-selected', true);
			currentPanel.setAttribute('aria-hidden', false);
		}
	};
};
