import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static values = { url: String };

		connect = () => {
			this.originalValue = this.element.value;
		};
		handleSelectChange = (e) => {
			var url = decodeURIComponent(this.urlValue).replace('%value', e.target.value);
			this.call(url);
		};

		call = (url) => {
			this.element.classList.add('is-loading');

			let xhr = new XMLHttpRequest();
			xhr.open('GET', url, true);
			xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
			xhr.onreadystatechange = () => {
				if (xhr.status == 200) {
					// OK
					console.log('ok');
				} else {
					console.log('error');
					// error - nastavit na puvodni hodnotu
					this.element.value = this.originalValue;
					this.element.dispatchEvent(new Event('update'));
				}
				this.element.classList.remove('is-loading');
			};
			xhr.send();
		};
	};
};
