import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['items'];
		static values = {
			sectionName: String,
		};

		add = (e) => {
			this.type = e.currentTarget.dataset.type;
			this.template = document.querySelector(`[data-template="${this.type}"]`);
			this.itemsTarget.insertAdjacentHTML(
				'beforeend',
				this.template.innerHTML.replace(/newItemMarker/g, `newItemMarker_${this.sectionNameValue}_${this.generateHash()}`),
			);
		};
		remove = (event) => {
			event.target.closest('[data-item]').remove();
		};
		generateHash = () => {
			return Math.random().toString(36).substring(2) + Date.now().toString(36);
		};
	};
};
