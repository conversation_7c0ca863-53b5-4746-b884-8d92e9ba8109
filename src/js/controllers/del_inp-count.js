import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['inp', 'toolMinus', 'toolPlus'];
		static values = {
			min: Number,
			max: Number,
			limitTooltip: Number,
		};
		changeValue(event) {
			const value = parseInt(this.inpTarget.value) + parseInt(event.target.dataset.step);

			// change value
			this.inpTarget.value = value;
			this.activeArrows(value);

			// trigger change
			const evt = document.createEvent('HTMLEvents');
			evt.initEvent('change', false, true);
			this.inpTarget.dispatchEvent(evt);
		}
		activeArrows = (value) => {
			this.toolMinusTarget.classList[this.minValue >= 0 && value <= this.minValue ? 'add' : 'remove']('is-disabled');
			this.toolPlusTarget.classList[this.maxValue >= 0 && value >= this.maxValue ? 'add' : 'remove']('is-disabled');
		};
		checkValue = () => {
			const value = parseInt(this.inpTarget.value);

			if (value < this.minValue) {
				this.inpTarget.value = this.minValue;
			}

			if (value > this.maxValue) {
				this.inpTarget.value = this.maxValue;
			}

			this.activeArrows(value);
		};
	};
};
