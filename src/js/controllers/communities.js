import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['select', 'group', 'item', 'empty'];

		filter = (e) => {
			const faculty = e.target.value;
			this.itemTargets.forEach((item) => {
				const faculties = item.dataset.faculty.split(',').map((f) => f.trim());
				const isVisible = faculty === 'all' || faculties.includes(faculty);
				item.classList.toggle('u-d-n', !isVisible);
			});

			this.groupTargets.forEach((group) => {
				const hasVisibleItems = Array.from(group.querySelectorAll('[data-faculty]')).some(
					(item) => !item.classList.contains('u-d-n'),
				);

				group.classList.toggle('u-d-n', !hasVisibleItems);
			});

			// empty state
			this.emptyTarget.classList.toggle(
				'u-d-n',
				this.groupTargets.some((group) => !group.classList.contains('u-d-n')),
			);
		};
	};
};
