import { Controller } from '@hotwired/stimulus';
import { useWindowResize } from 'stimulus-use';
import { getOffsetTop } from '../tools/getOffsetTop';
import { setCookie, hasCookie } from '../tools/cookie';

export const create = () => {
	return class extends Controller {
		static targets = ['logo'];

		connect() {
			// Scroll direction
			const { checkScrollPosition } = this;
			window.onscroll = function () {
				checkScrollPosition(this);
			};
			useWindowResize(this);

			if (!hasCookie('logo_shown')) {
				setCookie('logo_shown', 'true', { secure: true });
				setTimeout(() => {
					this.logoTarget.classList.add('is-hidden');
				}, 3000);
			}
		}

		checkScrollPosition = (window) => {
			this.unpinTarget = document.querySelector('[data-header-unpin]');
			var offset = this.element.clientHeight / 2;
			var direction = window.oldScroll > window.scrollY ? 'up' : 'down';
			var isOnTop = window.scrollY <= 10;
			window.oldScroll = window.scrollY;

			if (window.scrollY > offset) {
				this.element.classList[direction == 'up' ? 'add' : 'remove']('is-pinned');
				this.element.classList[direction == 'down' ? 'add' : 'remove']('is-unpinned');
			} else {
				this.element.classList.add('is-pinned');
			}

			if (isOnTop) {
				this.element.classList.remove('is-pinned', 'is-unpinned');
			}

			// Odepnutí hlavičky, pokud narazí na data-header-unpin
			this.element.classList[
				this.unpinTarget &&
				window.scrollY + this.element.querySelector('.header__main').clientHeight >= getOffsetTop(this.unpinTarget)
					? 'add'
					: 'remove'
			]('is-hidden');
		};
		windowResize = () => {
			setTimeout(() => {
				this.checkScrollPosition(window);
			}, 0);
		};
	};
};
