export class Input {
	constructor(options, countryCode) {
		this.options = options;
		this.countryCode = countryCode;
	}

	get() {
		if (this.element) return this.element;
		return this.create();
	}

	create() {
		const { className } = this.options;
		this.element = document.createElement('input');
		this.element.classList.add(`${className}__input`, 'inp__text');
		this.element.setAttribute('type', 'tel');
		this.element.setAttribute('autocomplete', 'mobile tel-national');
		this.element.setAttribute('inputmode', 'tel');

		return this.element;
	}

	setCountry(countryCode) {
		this.countryCode = countryCode;
	}

	saveCaret() {
		this.previousValue = this.element.value;
	}

	restoreCaret(key) {
		const diffIndex = this.findFirstDifferenceIndex(
			this.previousValue ? this.previousValue.replace(/[^0-9]/g, '') : '',
			this.element.value.replace(/[^0-9]/g, ''),
		);
		if (diffIndex) {
			const newPosition =
				this.findNthDigitIndex(this.element.value, diffIndex + 1) + (key === 'Backspace' || key === 'Delete' ? 0 : 1);

			this.element.selectionStart = newPosition;
			this.element.selectionEnd = newPosition;
			this.element.setSelectionRange(newPosition, newPosition);
		}
	}

	findFirstDifferenceIndex(oldValue, newValue) {
		const length = oldValue.length > newValue.length ? oldValue.length : newValue.length;
		for (let i = 0; i < length; i++) {
			if (oldValue[i] !== newValue[i]) {
				return i;
			}
		}

		return null;
	}

	findNthDigitIndex(string, n) {
		let count = 0;
		for (let i = 0; i < string.length; i++) {
			if (string[i].match(/\d/)) {
				count++;
				if (count === n) {
					return i;
				}
			}
		}

		return string.length;
	}
}
