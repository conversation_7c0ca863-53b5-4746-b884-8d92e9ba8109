import { Controller } from 'stimulus';
import { useIntersection } from 'stimulus-use';
import { useDispatch } from 'stimulus-use';

export default class Tiny extends Controller {
	static targets = ['item'];
	intersectoinOptions = {
		rootMargin: '0px 0px 300px 0px',
	};

	editor = null;

	connect() {
		useDispatch(this);
		useIntersection(this, this.intersectoinOptions);
	}

	appear() {
		const tinyController = this;
		const tinyMceConfig = {
			entity_encoding: 'raw',
			remove_linebreaks: 'false',
			gecko_spellcheck: false,
			keep_styles: true,
			accessibility_focus: true,
			tabfocus_elements: 'major-publishing-actions',
			media_strict: false,
			paste_remove_styles: false,
			paste_remove_spans: false,
			paste_strip_class_attributes: 'none',
			paste_text_use_dialog: true,
			relative_urls: false,
			extended_valid_elements: 'mark',

			setup: function(editor) {
				// function skButton() {
				// 	editor.insertContent('' + '<a href="" class="btn">' + '<span class="btn__text">Odkaz</span>' + '</a>' + '');
				// }
				// editor.ui.registry.addButton('skButton', {
				// 	text: 'Tlačítko',
				// 	tooltip: 'Tlačítko',
				// 	onAction: skButton,
				// });

				function skButtonWindow() {
					// Načti defaultní hodnoty
					var btn = editor.selection.getNode().closest('.btn');
					var link = btn ? btn.getAttribute('href') : '';
					var text = editor.selection.getNode().textContent;
					var blank = btn && btn.getAttribute('target') ? true : false;
					var type = btn && btn.classList[1] ? btn.classList[1] : '';

					editor.windowManager.open({
						title: 'Vložit tlačítko',
						body: {
							type: 'panel',
							items: [
								{
									type: 'input',
									name: 'link',
									label: 'URL',
								},
								{
									type: 'input',
									name: 'text',
									label: 'Text tlačítka',
								},
								{
									type: 'selectbox',
									name: 'type',
									label: 'Vyberte styl',
									items: [
										{ value: '', text: 'Defaultní' },
										{ value: 'btn--secondary', text: 'Sekundární' },
										{ value: 'btn--outline', text: 'Průhledný s rámečkem' },
										{ value: 'btn--outline btn--secondary', text: 'Sekundární průhledný s rámečkem' },
									],
								},
								{
									type: 'checkbox',
									name: 'blank',
									label: 'Otevřít odkaz v novém okně',
								},
							],
						},
						initialData: {
							link: link,
							text: text,
							blank: blank,
							type: type,
						},
						buttons: [
							{
								type: 'cancel',
								text: 'Close',
							},
							{
								type: 'submit',
								text: 'Save',
								primary: true,
							},
						],
						onSubmit: function(api) {
							let data = api.getData();
							var newBtn = `<a href="${data.link}" class="btn ${data.type}" ${data.blank ? 'target="_blank"' : null}>
								<span class="btn__text"><span>${data.text}</span></span></a>`;

							// Pokud tlačítko existuje, tak jej před vložením smažeme
							if (btn) editor.dom.remove(editor.selection.getNode().closest('.btn'));
							editor.insertContent(newBtn);
							api.close();
						},
					});
				}
				editor.ui.registry.addToggleButton('skButtonWindow', {
					text: 'Tlačítko',
					tooltip: 'Tlačítko',
					onAction: skButtonWindow,
					onSetup: (buttonApi) => editor.selection.selectorChangedWithUnbind('a.btn', buttonApi.setActive).unbind,
				});

				// Podbarvení text
				function skMarkWindow() {
					// Načti defaultní hodnoty
					var mark = editor.selection.getNode().closest('.mark');
					var text = editor.selection.getContent();
					var type = mark && mark.classList[1] ? mark.classList[1] : '';

					editor.windowManager.open({
						title: 'Vložit podbarvení',
						body: {
							type: 'panel',
							items: [
								{
									type: 'textarea',
									name: 'text',
									label: 'Text',
								},
								{
									type: 'selectbox',
									name: 'type',
									label: 'Barva podbarvení',
									items: [
										{ value: 'none', text: 'Žádné' },
										{ value: '', text: 'Šedá (default)' },
										{ value: 'mark--primary', text: 'Červená' },
									],
								},
							],
						},
						initialData: {
							text: text,
							type: type,
						},
						buttons: [
							{
								type: 'cancel',
								text: 'Close',
							},
							{
								type: 'submit',
								text: 'Save',
								primary: true,
							},
						],
						onSubmit: function(api) {
							let data = api.getData();
							if (data.type == 'none') {
								var newMark = `${data.text}`;
							} else {
								var newMark = `<div class="mark ${data.type}">${data.text}</div>`;
							}

							// Pokud tlačítko existuje, tak jej před vložením smažeme
							if (mark) editor.dom.remove(editor.selection.getNode().closest('.mark'));
							editor.insertContent(newMark);
							api.close();
						},
					});
				}
				editor.ui.registry.addToggleButton('skMarkWindow', {
					text: 'Podbarvení',
					tooltip: 'Podbarvení',
					onAction: skMarkWindow,
					onSetup: (buttonApi) => editor.selection.selectorChangedWithUnbind('div.mark', buttonApi.setActive).unbind,
				});

				// 'pre', 'p', 'code', 'h1', 'h5', 'h6'
				['h2', 'h3', 'h4'].forEach(function(name) {
					editor.ui.registry.addButton('style-' + name, {
						tooltip: 'Toggle ' + name,
						text: name.toUpperCase(),
						onAction: function() {
							editor.execCommand('mceToggleFormat', false, name);
						},
						onPostRender: function() {
							var self = this,
								setup = function() {
									editor.formatter.formatChanged(name, function(state) {
										self.active(state);
									});
								};
							// eslint-disable-next-line babel/no-unused-expressions
							editor.formatter ? setup() : editor.on('init', setup);
						},
					});
				});

				editor.on('blur', function() {
					editor.targetElm.dispatchEvent(new Event('blur'));
				});
			},

			valid_elements:
				'@[id|class|style|title|dir<ltr?rtl|lang|xml::lang|onclick|ondblclick|' +
				'onmousedown|onmouseup|onmouseover|onmousemove|onmouseout|onkeypress|' +
				'onkeydown|onkeyup],a[rel|rev|charset|hreflang|tabindex|accesskey|type|' +
				'name|href|target|title|class|onfocus|onblur],strong/b,em/i,strike,u,' +
				'#p,-ol[type|compact],-ul[type|compact],-li,br,img[longdesc|usemap|' +
				'src|border|alt=|title|hspace|vspace|width|height|align],-sub,-sup,' +
				'-blockquote,-table[border=0|cellspacing|cellpadding|width|frame|rules|' +
				'height|align|summary|bgcolor|background|bordercolor],-tr[rowspan|width|' +
				'height|align|valign|bgcolor|background|bordercolor],tbody,thead,tfoot,' +
				'#td[colspan|rowspan|width|height|align|valign|bgcolor|background|bordercolor' +
				'|scope],#th[colspan|rowspan|width|height|align|valign|scope],caption,-div,' +
				'-span,-code,-pre,address,-h2,-h3,-h4,-h5,-h6,hr[size|noshade],dd,dl,dt,cite,abbr,acronym,del[datetime|cite],ins[datetime|cite],' +
				'object[classid|width|height|codebase|*],param[name|value|_value],embed[type|width' +
				'|height|src|*],script[src|type],map[name],area[shape|coords|href|alt|target],bdo,' +
				'button,col[align|char|charoff|span|valign|width],colgroup[align|char|charoff|span|' +
				'valign|width],dfn,fieldset,form[action|accept|accept-charset|enctype|method],' +
				'input[accept|alt|checked|disabled|maxlength|name|readonly|size|src|type|value],' +
				'kbd,label[for],legend,noscript,optgroup[label|disabled],option[disabled|label|selected|value],' +
				'q[cite],samp,select[disabled|multiple|name|size],small,figure,figcaption,' +
				'textarea[cols|rows|disabled|name|readonly],tt,var,big,iframe[src|frameborder|allowfullscreen|width|height]',

			plugins: [
				'image wordcount link anchor table tabfocus paste media fullscreen charmap nonbreaking visualblocks lists advlist code',
			],

			template_cdate_format: '[CDATE: %m/%d/%Y : %H:%M:%S]',
			template_mdate_format: '[MDATE: %m/%d/%Y : %H:%M:%S]',

			menubar: false,

			language: 'cs',

			toolbar_mode: 'wrap',

			// for getting images from image library
			file_picker_callback: function(addCallback, value, meta) {
				if (meta.filetype === 'image') {
					// open image library
					tinyController.dispatch('openLibrary', { addCallback, variant: 'tinymce', shouldOpen: true });
				}
			},
		};

		// dafault wysiwyg
		let tinyMceDefaultConfig = tinyMceConfig;
		tinyMceDefaultConfig.formats = {
			alignleft: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'u-ta-l' },
			aligncenter: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'u-ta-c' },
			alignright: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'u-ta-r' },
		};
		tinyMceDefaultConfig.content_css = '/static/css/wysiwyg.css';
		tinyMceDefaultConfig.toolbar =
			'undo redo | style-h2 style-h3 style-h4 | bold italic | alignleft aligncenter alignright | bullist numlist outdent indent | table | link unlink anchor | image media | nonbreaking hr charmap | skButtonWindow | skMarkWindow | code';

		// eslint-disable-next-line no-undef
		this.editor = tinymce.createEditor(this.element.id, tinyMceDefaultConfig);
		this.editor.render();
	}

	disconnect() {
		if (this.editor) {
			this.editor.destroy();
		}
	}
}
