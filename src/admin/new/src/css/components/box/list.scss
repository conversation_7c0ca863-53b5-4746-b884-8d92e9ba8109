.b-list {
	&__item {
		position: relative;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		margin-bottom: $xxs;
		padding: $xs $xs $xs 0;
		border: 1px solid $colorBd;
		border-radius: $radius;
		background: $colorWhite;
		box-shadow: $shadow;
		> *:not(.b-suggest):not(.b-list__progress):not(.b-list__mask) {
			position: relative;
			margin-left: $xs;
		}
	}
	.btn-icon:not(&__mask) {
		z-index: 1;
	}
	&__mask {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		margin: 0;
		padding: 0;
		background: none;
	}
	&__img {
		width: 60px;
		img {
			width: 100%;
			max-height: 60px;
			object-fit: contain;
		}
	}
	&__text {
		flex: 1;
	}
	&__inp {
		flex: 1;
		min-width: calc(33.33% - 10px);
		margin-top: -5px;
		margin-bottom: -5px;
		.inp-text {
			padding-top: 5px;
			padding-bottom: 5px;
			box-shadow: none;
			&:hover,
			&:focus {
				background: $colorBgLighten;
			}
		}
		.choices__inner {
			padding: 1px 5px !important;
		}
		&:first-child {
			margin-top: -5px;
		}
	}
	// double selector – fix old superadmin
	&__add#{&}__add {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		margin-bottom: $xxs;
		padding: 8px $xs;
		border: 1px solid $colorBd;
		border-radius: $radius;
		background: $colorBgLighten;
		color: $colorTextLight;
		transition: color $t;
		box-sizing: border-box; // fix old superadmin
		box-shadow: $shadow;
		svg {
			width: 12px;
			height: 12px;
		}
		&--file {
			position: relative;
			input {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				opacity: 0;
				cursor: pointer;
			}
		}
	}

	&__progress {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		z-index: 0;
		width: 0;
		background-color: $colorBgLighten;
		transition: width $t linear, backgroundColor $t;
		.is-error & {
			background-color: rgba($colorRed, 0.2);
		}
		.is-success & {
			background-color: rgba($colorGreen, 0.2);
		}
	}

	&__delete {
		position: absolute;
		top: 0;
		right: 0;
	}
	&__move-up {
		position: absolute;
		top: 0;
		right: 60px;
	}
	&__move-down {
		position: absolute;
		top: 0;
		right: 30px;
	}
	&__langs {
		flex: 1;
	}
	&__langs,
	&__tags {
		display: flex;
		flex-wrap: wrap;
		margin: 0 0 $xxs * -1 $xs * -1;
		> * {
			margin: 0 0 $xxs $xs;
		}
	}

	// &__add {
	// 	position: relative;
	// 	width: 100%;
	// 	margin: $xxs 0 $xxs * -1;
	// 	padding: $xxs 0;
	// 	color: $colorTextLight;
	// 	text-align: center;
	// 	overflow: hidden;
	// 	transition: color $t;
	// 	&::before,
	// 	&::after {
	// 		content: '';
	// 		position: absolute;
	// 		top: 50%;
	// 		height: 2px;
	// 		margin-top: -1px;
	// 		background: $colorBg;
	// 	}
	// 	&::before {
	// 		right: calc(50% + 14px);
	// 		left: 0;
	// 	}
	// 	&::after {
	// 		right: 0;
	// 		left: calc(50% + 14px);
	// 	}
	// 	svg {
	// 		width: 14px;
	// 		height: 14px;
	// 	}
	// }

	// STATEs
	.hoverevents &__add:hover {
		color: $colorPrimary;
	}
}
